<!DOCTYPE html>
<html lang="en">

<!-- Mirrored from seogrove.ai/ by HTTrack Website Copier/3.x [XR&CO'2014], Wed, 09 Jul 2025 16:38:41 GMT -->
<!-- Added by HTTrack --><meta http-equiv="content-type" content="text/html;charset=utf-8" /><!-- /Added by HTTrack -->
<head>
    <!-- Google Tag Manager -->
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    '../www.googletagmanager.com/gtm5445.html?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-5CMNG3PG');</script>
    <!-- End Google Tag Manager -->
    
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Grove - AI-Powered Shopify SEO Automation | Grove - AI-Powered Shopify SEO</title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="Transform your Shopify store with AI-powered SEO automation. Generate optimized product descriptions, create smart collections, and boost organic traffic 24/7.">
    <meta name="keywords" content="shopify seo, ai seo automation, shopify optimization, ecommerce seo">
    <meta name="author" content="Grove">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="index.html">
    
    <!-- Open Graph Tags -->
    <meta property="og:type" content="website">
    <meta property="og:title" content="Grove - AI-Powered Shopify SEO Automation | Grove">
    <meta property="og:description" content="Transform your Shopify store with AI-powered SEO automation. Generate optimized product descriptions, create smart collections, and boost organic traffic 24/7.">
    <meta property="og:image" content="https://seogrove.ai/static/images/og-grove.jpg">
    <meta property="og:url" content="https://seogrove.ai/">
    <meta property="og:site_name" content="Grove">
    <meta property="og:locale" content="en_US">
    
    <!-- Twitter Card Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Grove - AI-Powered Shopify SEO Automation | Grove">
    <meta name="twitter:description" content="Transform your Shopify store with AI-powered SEO automation. Generate optimized product descriptions, create smart collections, and boost organic traffic 24/7.">
    <meta name="twitter:image" content="https://seogrove.ai/static/images/og-grove.jpg">
    <meta name="twitter:site" content="@Grove">
    
    <!-- Additional SEO Tags -->
    <meta name="theme-color" content="#ef2b70">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    
    <!-- Preconnect to External Resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com/">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin>
    <link rel="preconnect" href="https://cdn.jsdelivr.net/">
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="32x32" href="static/favicon-32x32.html">
    <link rel="icon" type="image/png" sizes="16x16" href="static/favicon-16x16.html">
    <link rel="apple-touch-icon" href="static/apple-touch-icon.html">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com/">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lexend:wght@400;500;600;700;800;900&amp;family=Inter:wght@400;500;600;700;800&amp;display=swap" rel="stylesheet">
    
    <!-- CSS Resources -->
    <link href="../cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="static/css/adcreative-style.css" rel="stylesheet">
    <link href="static/css/mega-menu.css" rel="stylesheet">
    <link href="static/css/mobile-optimization.css" rel="stylesheet">
    
    <!-- Logo styles -->
    <style>
        .ac-logo {
            display: flex;
            align-items: center;
        }
        .ac-logo img {
            display: block;
            object-fit: contain;
        }
        
        /* Simple Footer Styles */
        .footer {
            background: #f8f9fa;
            color: #1e293b;
            padding: 4rem 0 2rem;
            border-top: 1px solid #e2e8f0;
        }
        
        .footer-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }
        
        .footer-brand {
            text-align: center;
            margin-bottom: 3rem;
            padding-bottom: 3rem;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .footer-brand img {
            height: 44px;
            margin-bottom: 1rem;
        }
        
        .footer-brand p {
            color: #64748b;
            font-size: 1.125rem;
            margin: 0;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .footer-columns {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 3rem;
            margin-bottom: 3rem;
            padding-bottom: 3rem;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .footer-column h4 {
            color: #1e293b;
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin: 0 0 1.5rem;
        }
        
        .footer-column ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .footer-column li {
            margin-bottom: 0.75rem;
        }
        
        .footer-column a {
            color: #64748b;
            text-decoration: none;
            transition: color 0.2s;
        }
        
        .footer-column a:hover {
            color: #ef2b70;
        }
        
        .footer-login {
            color: #22c55e !important;
            font-weight: 500;
        }
        
        .footer-social {
            display: flex;
            gap: 0.75rem;
            margin-top: 1rem;
        }
        
        .social-link {
            width: 36px;
            height: 36px;
            background: #ffffff;
            border: 1px solid #e2e8f0;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #64748b;
            transition: all 0.2s;
        }
        
        .social-link:hover {
            background: #ef2b70;
            border-color: #ef2b70;
            color: #fff;
        }
        
        .footer-bottom {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .footer-copyright {
            color: #64748b;
            font-size: 0.875rem;
            margin: 0;
        }
        
        .footer-tagline {
            color: #64748b;
            font-size: 0.875rem;
            margin: 0;
            font-style: italic;
        }
        
        .footer-tagline i {
            color: #22c55e;
            margin-right: 0.5rem;
        }
        
        @media (max-width: 768px) {
            .footer-columns {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
            .footer-bottom {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }
        }
    </style>
    
    <!-- Schema.org JSON-LD -->
    
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "Grove",
    "applicationCategory": "BusinessApplication", 
    "operatingSystem": "Web",
    "description": "AI-powered Shopify SEO automation platform",
    "url": "https://seogrove.ai/"
}
</script>

    
    
<link href="static/css/adcreative-style.css" rel="stylesheet">
<link href="static/css/ai-rounded-light.css" rel="stylesheet">
<style>
/* Hero Dashboard Animation Styles */
.ac-hero-dashboard {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
}

/* Show sparkle only on desktop */
@media (min-width: 1024px) {
    .ac-results-section .ac-sparkle-7 {
        display: block !important;
    }
}

.ac-hero-toggle-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    border: 2px solid #e5e7eb;
    transition: all 0.3s ease;
    position: relative;
    min-height: 140px;
}

.ac-hero-toggle-card.active {
    border-color: var(--ac-grove);
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.02) 0%, rgba(34, 197, 94, 0.05) 100%);
}

/* Toggle Switch Styles */
.ac-hero-toggle-switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
}

.ac-hero-toggle-slider {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #cbd5e1;
    transition: .4s;
    border-radius: 34px;
    cursor: pointer;
}

.ac-hero-toggle-handle {
    position: absolute;
    content: '';
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

.ac-hero-toggle-slider.active {
    background-color: var(--ac-grove);
}

.ac-hero-toggle-slider.active .ac-hero-toggle-handle {
    transform: translateX(20px);
}

/* Feed Animation */
.ac-hero-feed {
    margin-top: 0.5rem;
    padding-top: 0.5rem;
    padding-bottom: 0.25rem; /* Add bottom padding */
    border-top: 1px solid #e5e7eb;
}

.ac-hero-feed.active {
    opacity: 1 !important;
    max-height: 120px !important; /* Increased to show all items */
}

.ac-hero-feed-item {
    display: flex;
    align-items: center;
    gap: 0.625rem;
    margin-bottom: 0.625rem;
    font-size: 0.813rem;
    opacity: 0;
    animation: heroSlideIn 0.5s ease forwards;
    min-height: 1.75rem;
    height: 1.75rem;
    overflow: hidden;
    position: relative;
    transition: background-color 0.3s ease;
}

/* Ensure consistent icon sizing */
.ac-hero-feed-item i {
    width: 1.25rem !important;
    min-width: 1.25rem !important;
    height: 1.25rem !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    flex-shrink: 0 !important;
    font-size: 1rem !important;
    line-height: 1 !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.ac-hero-feed-item span {
    line-height: 1.2;
    flex: 1;
}

.ac-hero-metric {
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    background: rgba(34, 197, 94, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    color: var(--ac-grove);
    font-weight: 600;
    height: auto !important; /* Override fixed height */
    min-height: 1.75rem;
}

@keyframes heroSlideIn {
    from {
        opacity: 0;
        transform: translateX(-15px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes heroGlowPulse {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
    }
    50% {
        box-shadow: 0 0 20px 5px rgba(34, 197, 94, 0.3);
    }
}

/* Metrics Bar */
.ac-hero-metrics-bar {
    margin-top: 0.75rem;
    padding-top: 0.75rem;
    border-top: 1px solid #e5e7eb;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
}

.ac-hero-metrics-bar.active {
    opacity: 1 !important;
    max-height: 100px !important;
}

.ac-hero-mini-metric {
    text-align: center;
}

.ac-hero-metric-value {
    font-size: 1.125rem;
    font-weight: 800;
    color: var(--ac-grove);
    display: block;
}

.ac-hero-metric-label {
    font-size: 0.625rem;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Animation for spinning gears */
@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Pulse animation for live indicator */
@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

/* Notification animation */
.ac-hero-notification {
    position: absolute;
    right: 0;
    background: var(--ac-grove);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.75rem;
    font-weight: 600;
    white-space: nowrap;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    animation: notificationSlide 4s ease-out forwards;
}

.ac-hero-notification::before {
    content: '\f00c'; /* Checkmark icon */
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    font-size: 0.875rem;
}

@keyframes notificationSlide {
    0% {
        opacity: 0;
        transform: translateX(100%);
        right: -100%;
    }
    10% {
        opacity: 1;
        transform: translateX(0);
        right: 1rem;
    }
    90% {
        opacity: 1;
        transform: translateX(0);
        right: 1rem;
    }
    100% {
        opacity: 0;
        transform: translateX(0);
        right: 1rem;
    }
}

/* Subtle tick effect for number changes */
@keyframes valueTick {
    0% {
        transform: translateY(0);
        opacity: 1;
    }
    50% {
        transform: translateY(-3px);
        opacity: 0.8;
        color: #16a34a;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Pulse effect for feed updates */
@keyframes feedItemPulse {
    0% {
        background-color: transparent;
    }
    50% {
        background-color: rgba(34, 197, 94, 0.08);
    }
    100% {
        background-color: transparent;
    }
}

.ac-hero-metric-value.tick {
    animation: valueTick 0.4s ease;
}

.ac-hero-feed-item.highlight {
    background-color: rgba(34, 197, 94, 0.08);
    border-radius: 6px;
    padding-left: 0.375rem;
    padding-right: 0.375rem;
    margin-left: -0.375rem;
    margin-right: -0.375rem;
}

/* Automation icon styles */
.ac-automation-icon {
    margin-left: auto;
}

.ac-hero-toggle-card.active .ac-automation-icon {
    display: inline-block !important;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .ac-hero-visual {
        margin-top: 2rem;
    }
    
    .ac-hero-dashboard .ac-browser-content {
        padding: 1rem !important;
    }
    
    .ac-hero-toggle-card {
        padding: 0.75rem;
    }
}

/* Testimonial Section Styles */
.ac-testimonial-section {
    padding: 6rem 2rem;
    background: var(--ac-bg-light);
    position: relative;
}

.ac-testimonial-container {
    max-width: 1200px;
    margin: 0 auto;
}

.ac-testimonial-content {
    display: grid;
    grid-template-columns: 1fr 380px;
    gap: 3rem;
    align-items: center;
}

.ac-testimonial-text {
    background: white;
    padding: 3rem;
    border-radius: var(--ac-radius);
    box-shadow: var(--ac-shadow);
}

.ac-testimonial-header {
    margin-bottom: 1rem;
}

.ac-company-logo-standalone {
    width: 200px;
    height: auto;
    object-fit: contain;
    opacity: 0.9;
    transition: opacity 0.3s ease;
}

.ac-company-logo-standalone:hover {
    opacity: 1;
}

.ac-title-with-flag {
    margin-bottom: 2rem;
}

.ac-testimonial-text h2 {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--ac-text-primary);
    margin-bottom: 1rem;
    line-height: 1.2;
    font-family: var(--ac-font-heading);
}

.ac-flag-inline {
    width: 40px;
    height: auto;
    display: inline-block;
    vertical-align: middle;
    margin-left: 0.75rem;
    border-radius: 3px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.08);
}

.ac-testimonial-intro {
    font-size: 1.125rem;
    line-height: 1.7;
    color: var(--ac-text-secondary);
    margin-bottom: 2rem;
    position: relative;
}

.ac-arrow-to-video {
    position: absolute;
    right: -100px;
    top: 90%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
}

.ac-arrow-img {
    width: 115px;
    height: auto;
    object-fit: contain;
}

.ac-testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.ac-testimonial-avatar {
    width: 48px;
    height: 48px;
    border-radius: var(--ac-radius-full);
    background: var(--ac-gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 1rem;
    overflow: hidden;
}

.ac-avatar-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: var(--ac-radius-full);
}

.ac-testimonial-author-info h4 {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--ac-text-primary);
    margin-bottom: 0.25rem;
    font-family: var(--ac-font-heading);
}

.ac-testimonial-author-info p {
    color: var(--ac-text-secondary);
    font-size: 0.875rem;
}

.ac-testimonial-metrics {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.ac-testimonial-metric {
    text-align: center;
    padding: 1rem;
    background: var(--ac-bg-light);
    border-radius: var(--ac-radius-sm);
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.ac-testimonial-metric:hover {
    border-color: var(--ac-primary);
    background: white;
}

.ac-testimonial-metric-value {
    font-size: 1.75rem;
    font-weight: 800;
    color: var(--ac-primary);
    margin-bottom: 0.25rem;
    font-family: var(--ac-font-heading);
}

.ac-testimonial-metric-label {
    font-size: 0.75rem;
    color: var(--ac-text-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.ac-testimonial-video {
    position: relative;
    display: flex;
    justify-content: center;
}

.ac-video-showcase {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.ac-video-container {
    position: relative;
    width: 320px;
    height: 568px;
    border-radius: var(--ac-radius-lg);
    overflow: hidden;
    box-shadow: var(--ac-shadow-xl);
    background: var(--ac-secondary);
    padding: 8px;
    transition: all 0.3s ease;
}

.ac-video-container:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 64px rgba(30, 21, 65, 0.2);
}

.ac-video-container video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: calc(var(--ac-radius-lg) - 8px);
    cursor: pointer;
}

.ac-video-overlay {
    position: absolute;
    top: 8px;
    left: 8px;
    right: 8px;
    bottom: 8px;
    background: rgba(30, 21, 65, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 1;
    transition: opacity 0.3s ease;
    cursor: pointer;
    border-radius: calc(var(--ac-radius-lg) - 8px);
}

.ac-play-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    background: white;
    color: var(--ac-secondary);
    border: none;
    padding: 1rem 1.5rem;
    border-radius: var(--ac-radius);
    font-size: 0.875rem;
    font-weight: 600;
    box-shadow: var(--ac-shadow);
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: var(--ac-font-sans);
}

.ac-play-button:hover {
    background: var(--ac-primary);
    color: white;
    transform: scale(1.05);
}

.ac-play-button i {
    font-size: 1.25rem;
}

.ac-video-badge {
    position: absolute;
    top: 1.5rem;
    left: 1.5rem;
    background: var(--ac-gradient-primary);
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: var(--ac-radius-full);
    font-size: 0.75rem;
    font-weight: 600;
    backdrop-filter: blur(10px);
    box-shadow: var(--ac-shadow);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Testimonial More CTA */
.ac-testimonial-more-cta {
    text-align: center;
    margin-top: 4rem;
}

.ac-testimonial-more-cta .ac-btn {
    font-size: 1.125rem;
    padding: 1rem 2rem;
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    transition: all 0.3s ease;
}

.ac-testimonial-more-cta .ac-btn i {
    font-size: 1.25rem;
}

.ac-testimonial-more-cta .ac-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(239, 43, 112, 0.3);
}

/* Responsive for Testimonial */
@media (max-width: 768px) {
    .ac-testimonial-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .ac-testimonial-text {
        padding: 2rem;
    }
    
    .ac-testimonial-text h2 {
        font-size: 1.75rem;
        text-align: center;
    }
    
    .ac-testimonial-metrics {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .ac-video-container {
        width: 280px;
        height: 498px;
        margin: 0 auto;
    }
    
    .ac-arrow-to-video {
        display: none;
    }
}

/* AI Brain Orchestration Section - Brain to Dashboard */
.ac-ai-brain-section {
    padding: 6rem 2rem;
    background: linear-gradient(180deg, #ffffff 0%, #f5f5f5 100%);
    position: relative;
    overflow: hidden;
}

.ac-brain-orchestration {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    gap: 3rem;
    min-height: 600px;
}

/* Left side - Clean Brain Icon */
.ac-brain-left {
    flex: 0 0 250px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding-left: 2rem;
}

.ac-brain-icon-container {
    width: 200px;
    height: 200px;
    position: relative;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
}

.ac-brain-blob {
    position: absolute;
    width: 320px;
    height: 320px;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, 
        rgba(239, 43, 112, 0.08) 0%, 
        rgba(255, 64, 133, 0.12) 30%,
        rgba(239, 43, 112, 0.06) 70%,
        rgba(255, 64, 133, 0.08) 100%);
    border: 1px solid rgba(239, 43, 112, 0.2);
    border-radius: 60% 40% 40% 60% / 40% 60% 40% 60%;
    animation: morphHeroBlob 20s ease-in-out infinite;
    z-index: 0;
    filter: blur(0.5px);
}

.ac-brain-inner {
    width: 120px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 2;
    animation: brainPulse 3s ease-in-out infinite;
}

.ac-brain-icon {
    font-size: 3.5rem;
    color: var(--ac-primary);
    animation: brainThink 2s ease-in-out infinite;
    z-index: 3;
    position: relative;
    filter: drop-shadow(0 4px 12px rgba(239, 43, 112, 0.3));
}

.ac-brain-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--ac-text-primary);
    margin-bottom: 0.5rem;
    font-family: var(--ac-font-heading);
}

.ac-brain-subtitle {
    font-size: 1rem;
    color: var(--ac-text-secondary);
    max-width: 250px;
}

/* Center - Dotted Line Connection */
.ac-brain-connection {
    flex: 1;
    position: relative;
    height: 2px;
    margin: 0 2rem 0 3rem;
    min-width: 200px;
}

.ac-dotted-line {
    width: 100%;
    height: 2px;
    background: repeating-linear-gradient(
        to right,
        rgba(239, 43, 112, 0.3) 0px,
        rgba(239, 43, 112, 0.3) 6px,
        transparent 6px,
        transparent 12px
    );
    border-radius: 1px;
    position: relative;
    overflow: visible;
}

.ac-task-icon {
    position: absolute;
    top: -12px;
    left: -30px;
    width: 24px;
    height: 24px;
    background: linear-gradient(135deg, var(--ac-primary) 0%, #ff4085 100%);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.75rem;
    opacity: 0;
    box-shadow: 0 4px 12px rgba(239, 43, 112, 0.4);
    z-index: 10;
    transition: all 0.3s ease;
}

.ac-task-icon.traveling {
    animation: iconTravel 1.5s ease-in-out;
}

/* Right side - Dashboard Mockup */
.ac-brain-dashboard {
    flex: 0 0 400px;
    background: white;
    border-radius: 20px;
    box-shadow: 0 24px 80px rgba(0, 0, 0, 0.15);
    border: 2px solid #e5e7eb;
    overflow: hidden;
    position: relative;
    transition: all 0.4s ease;
}

.ac-brain-dashboard.task-received {
    border-color: #10b981;
    box-shadow: 0 24px 80px rgba(16, 185, 129, 0.25);
    transform: scale(1.02);
}

.ac-brain-dashboard.task-scheduled {
    border-color: #10b981;
    box-shadow: 0 24px 80px rgba(16, 185, 129, 0.2);
}

.ac-dashboard-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.ac-dashboard-dots {
    display: flex;
    gap: 0.5rem;
}

.ac-dashboard-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #cbd5e1;
}

.ac-dashboard-dot.red { background: #ef4444; }
.ac-dashboard-dot.yellow { background: #f59e0b; }
.ac-dashboard-dot.green { background: #10b981; }

/* Browser window dots */
.ac-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.ac-dot-red {
    background: #ff5f57;
}

.ac-dot-yellow {
    background: #ffbd2e;
}

.ac-dot-green {
    background: #28ca42;
}

.ac-dashboard-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--ac-text-primary);
}

.ac-dashboard-content {
    padding: 2rem;
    height: 400px;
    position: relative;
    overflow: hidden;
}

.ac-task-scheduled-indicator {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 700;
    opacity: 0;
    transform: translateY(-10px) scale(0.8);
    transition: all 0.3s ease;
    z-index: 100;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.ac-task-scheduled-indicator.show {
    opacity: 1;
    transform: translateY(0) scale(1);
}

.ac-task-scheduled-indicator.hide {
    opacity: 0;
    transform: translateY(-10px) scale(0.8);
}

.ac-task-scheduled-indicator i {
    font-size: 0.875rem;
    margin-right: 0.25rem;
    vertical-align: middle;
}

.ac-notifications-header {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--ac-text-primary);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.ac-live-dot {
    width: 8px;
    height: 8px;
    background: #10b981;
    border-radius: 50%;
    animation: livePulse 2s ease-in-out infinite;
}

.ac-notifications-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.ac-notification {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.625rem 0.875rem;
    background: white;
    border-radius: 10px;
    border: 1px solid #e5e7eb;
    opacity: 0;
    transform: translateY(-15px) scale(0.95);
    animation: notificationPop 0.8s ease-out forwards;
    position: relative;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
}

.ac-notification:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.ac-notification-icon {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, var(--ac-primary) 0%, #ff4085 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
    flex-shrink: 0;
    box-shadow: 0 2px 8px rgba(239, 43, 112, 0.3);
}

.ac-notification-content {
    flex: 1;
}

.ac-notification-title {
    font-size: 0.8125rem;
    font-weight: 700;
    color: var(--ac-text-primary);
    margin-bottom: 0.125rem;
    line-height: 1.2;
}

.ac-notification-message {
    font-size: 0.75rem;
    color: var(--ac-text-secondary);
    line-height: 1.3;
}

.ac-notification-time {
    font-size: 0.6875rem;
    color: var(--ac-text-muted);
    font-weight: 500;
    margin-left: auto;
    flex-shrink: 0;
}

/* Animations */
@keyframes brainPulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes rippleBrain {
    0% {
        transform: scale(1);
        opacity: 0.6;
    }
    100% {
        transform: scale(1.8);
        opacity: 0;
    }
}

@keyframes brainThink {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

@keyframes morphHeroBlob {
    0%, 100% {
        border-radius: 60% 40% 40% 60% / 40% 60% 40% 60%;
        transform: translate(-50%, -50%) rotate(0deg) scale(1);
    }
    20% {
        border-radius: 40% 60% 60% 40% / 60% 40% 60% 40%;
        transform: translate(-50%, -50%) rotate(72deg) scale(1.05);
    }
    40% {
        border-radius: 60% 40% 40% 60% / 40% 60% 40% 60%;
        transform: translate(-50%, -50%) rotate(144deg) scale(0.95);
    }
    60% {
        border-radius: 40% 60% 60% 40% / 60% 40% 60% 40%;
        transform: translate(-50%, -50%) rotate(216deg) scale(1.02);
    }
    80% {
        border-radius: 60% 40% 40% 60% / 40% 60% 40% 60%;
        transform: translate(-50%, -50%) rotate(288deg) scale(0.98);
    }
}

@keyframes iconTravel {
    0% {
        left: -30px;
        opacity: 0;
        transform: scale(0.8);
    }
    15% {
        opacity: 1;
        transform: scale(1);
    }
    85% {
        opacity: 1;
        transform: scale(1);
    }
    100% {
        left: calc(100% + 10px);
        opacity: 0;
        transform: scale(1.2);
    }
}

@keyframes livePulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.3;
    }
}

@keyframes notificationPop {
    0% {
        opacity: 0;
        transform: translateY(-15px) scale(0.95);
    }
    60% {
        opacity: 1;
        transform: translateY(-3px) scale(1.02);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes notificationSlideDown {
    0% {
        opacity: 0;
        transform: translateY(-20px) scale(0.9);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.ac-notification.new {
    animation: notificationSlideDown 0.6s ease-out forwards;
}

/* Static notification animation delays - only for initial load */
.ac-notification:not(.new):nth-child(1) { animation-delay: 0.5s; }
.ac-notification:not(.new):nth-child(2) { animation-delay: 0.7s; }
.ac-notification:not(.new):nth-child(3) { animation-delay: 0.9s; }
.ac-notification:not(.new):nth-child(4) { animation-delay: 1.1s; }
.ac-notification:not(.new):nth-child(5) { animation-delay: 1.3s; }

/* Brain Sparkles - Match automation section style */
.ac-brain-sparkle {
    position: absolute;
    pointer-events: none;
}

.ac-brain-sparkle svg {
    width: 24px;
    height: 24px;
    fill: #ef2b70;
    opacity: 0.6;
    animation: sparkPulse 3s ease-in-out infinite;
}

.ac-brain-sparkle.small svg {
    width: 16px;
    height: 16px;
}

.ac-brain-sparkle.large svg {
    width: 32px;
    height: 32px;
}

/* Position sparkles around dashboard - avoid overlapping */
.ac-brain-sparkle-1 {
    top: 30%;
    left: 57%;
    animation-delay: 0s;
}

.ac-brain-sparkle-2 {
    bottom: 30%;
    right: 12%;
    animation-delay: 1.5s;
}

.ac-brain-sparkle-3 {
    top: 50%;
    right: 5%;
    animation-delay: 2.5s;
}

@keyframes sparkPulse {
    0%, 100% {
        opacity: 0.4;
        transform: scale(0.9);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.1);
    }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .ac-brain-orchestration {
        gap: 2rem;
    }
    
    .ac-brain-left {
        flex: 0 0 230px;
        padding-left: 1rem;
    }
    
    .ac-brain-dashboard {
        flex: 0 0 380px;
    }
}

@media (max-width: 1024px) {
    .ac-brain-orchestration {
        flex-direction: column;
        gap: 3rem;
        text-align: center;
    }
    
    .ac-brain-left {
        flex: none;
        padding-left: 0;
    }
    
    .ac-brain-connection {
        display: none;
    }
    
    .ac-brain-dashboard {
        flex: none;
        width: 100%;
        max-width: 500px;
        margin: 0 auto;
    }
}

@media (max-width: 768px) {
    .ac-brain-icon-container {
        width: 150px;
        height: 150px;
    }
    
    .ac-brain-icon {
        font-size: 3rem;
    }
    
    .ac-brain-title {
        font-size: 1.25rem;
    }
    
    .ac-dashboard-content {
        padding: 1.5rem;
        height: 350px;
    }
}
</style>

    
    
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=AW-17160853988"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'AW-17160853988');
    </script>
    
    <!-- Event snippet for Google Shopping App Purchase conversion page -->
    <script>
    function gtag_report_conversion(url) {
      var callback = function () {
        if (typeof(url) != 'undefined') {
          window.location = url;
        }
      };
      gtag('event', 'conversion', {
          'send_to': 'AW-17160853988/uVkCCKrgnNQaEOSz9_Y_',
          'value': 1.0,
          'currency': 'GBP',
          'transaction_id': '',
          'event_callback': callback
      });
      return false;
    }
    </script>
    
    <!-- Mobile Logo Switch -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            function updateLogo() {
                const logo = document.querySelector('.ac-logo-img');
                if (logo && window.innerWidth <= 767) {
                    logo.src = 'static/images/Grovelogosmall.png';
                } else if (logo && window.innerWidth > 767) {
                    logo.src = 'static/images/grove-logo.png';
                }
            }
            
            // Run on load
            updateLogo();
            
            // Run on resize
            let resizeTimer;
            window.addEventListener('resize', function() {
                clearTimeout(resizeTimer);
                resizeTimer = setTimeout(updateLogo, 250);
            });
        });
    </script>
</head>
<body>
    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5CMNG3PG"
    height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->
    
    <!-- Navigation -->
    <nav class="ac-nav">
        <div class="ac-nav-container">
            <a href="index.html" class="ac-logo">
                <img src="static/images/grove-logo.png" alt="Grove" class="ac-logo-img">
            </a>
            
            <div class="ac-nav-menu">
                <!-- Why Grove Mega Menu -->
                <div class="ac-nav-dropdown">
                    <a href="#" class="ac-nav-link ac-dropdown-toggle">Why Grove</a>
                    <div class="ac-mega-menu">
                        <div class="ac-mega-menu-inner">
                            <div class="ac-mega-grid ac-mega-grid-features">
                            <!-- Features Column 1 -->
                            <div class="ac-mega-column ac-mega-feature-column">
                                <a href="features.html#product-seo-engine" class="ac-mega-feature">
                                    <div class="ac-mega-feature-icon">
                                        <i class="fas fa-brain"></i>
                                    </div>
                                    <div class="ac-mega-feature-content">
                                        <span class="ac-mega-feature-title">Product SEO Engine</span>
                                        <span class="ac-mega-feature-desc">AI-powered descriptions, meta tags & smart alt tags</span>
                                    </div>
                                </a>
                                <a href="features.html#smart-collections" class="ac-mega-feature">
                                    <div class="ac-mega-feature-icon">
                                        <i class="fas fa-network-wired"></i>
                                    </div>
                                    <div class="ac-mega-feature-content">
                                        <span class="ac-mega-feature-title">Smart Collections</span>
                                        <span class="ac-mega-feature-desc">Trend-based & seasonal collection creation</span>
                                    </div>
                                </a>
                                <a href="features.html#technical-seo" class="ac-mega-feature">
                                    <div class="ac-mega-feature-icon">
                                        <i class="fas fa-cog"></i>
                                    </div>
                                    <div class="ac-mega-feature-content">
                                        <span class="ac-mega-feature-title">Technical SEO</span>
                                        <span class="ac-mega-feature-desc">Sitemaps, speed monitoring & error fixes</span>
                                    </div>
                                </a>
                            </div>
                            
                            <!-- Features Column 2 -->
                            <div class="ac-mega-column ac-mega-feature-column">
                                <a href="features.html#search-insights" class="ac-mega-feature">
                                    <div class="ac-mega-feature-icon">
                                        <i class="fas fa-microscope"></i>
                                    </div>
                                    <div class="ac-mega-feature-content">
                                        <span class="ac-mega-feature-title">Search Insights</span>
                                        <span class="ac-mega-feature-desc">Google Search Console analytics & keyword tracking</span>
                                    </div>
                                </a>
                                <a href="features.html#content-machine" class="ac-mega-feature">
                                    <div class="ac-mega-feature-icon">
                                        <i class="fas fa-feather-alt"></i>
                                    </div>
                                    <div class="ac-mega-feature-content">
                                        <span class="ac-mega-feature-title">Content Machine</span>
                                        <span class="ac-mega-feature-desc">Blog posts, guides & internal linking automation</span>
                                    </div>
                                </a>
                                <a href="features.html#competitor-intel" class="ac-mega-feature">
                                    <div class="ac-mega-feature-icon">
                                        <i class="fas fa-user-secret"></i>
                                    </div>
                                    <div class="ac-mega-feature-content">
                                        <span class="ac-mega-feature-title">Competitor Intel</span>
                                        <span class="ac-mega-feature-desc">Monitor competitor keywords & strategies</span>
                                    </div>
                                </a>
                            </div>
                            
                            <!-- Features Column 3 -->
                            <div class="ac-mega-column ac-mega-feature-column">
                                <a href="features.html#link-building" class="ac-mega-feature">
                                    <div class="ac-mega-feature-icon">
                                        <i class="fas fa-link"></i>
                                    </div>
                                    <div class="ac-mega-feature-content">
                                        <span class="ac-mega-feature-title">Link Building</span>
                                        <span class="ac-mega-feature-desc">Automated outreach & partnership opportunities</span>
                                    </div>
                                </a>
                                <a href="features.html#translations" class="ac-mega-feature">
                                    <div class="ac-mega-feature-icon">
                                        <i class="fas fa-globe"></i>
                                    </div>
                                    <div class="ac-mega-feature-content">
                                        <span class="ac-mega-feature-title">Translations</span>
                                        <span class="ac-mega-feature-desc">Multi-language SEO optimization</span>
                                    </div>
                                </a>
                            </div>
                            </div>
                            
                            <!-- Featured Section -->
                            <div class="ac-mega-featured">
                                <div class="ac-mega-featured-content">
                                    <h4 class="ac-mega-featured-title">🚀 See All Features in Action</h4>
                                    <p class="ac-mega-featured-desc">
                                        Join 7,064 Shopify stores already dominating their competition
                                    </p>
                                    <div class="ac-mega-featured-links">
                                        <a href="features.html" class="ac-mega-featured-link">
                                            View All Features
                                            <i class="fas fa-arrow-right"></i>
                                        </a>
                                        <a href="pre-registration.html" class="ac-mega-featured-link">
                                            Join Waitlist
                                            <i class="fas fa-arrow-right"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Solutions Mega Menu -->
                <div class="ac-nav-dropdown">
                    <a href="#" class="ac-nav-link ac-dropdown-toggle">Solutions</a>
                    <div class="ac-mega-menu">
                        <div class="ac-mega-menu-inner">
                            <div class="ac-mega-grid-solutions">
                            <!-- By Store Type (Full Width) -->
                            <div class="ac-mega-solutions-main">
                                <h3 class="ac-mega-column-header">
                                    <i class="fas fa-store"></i>
                                    Solutions By Store Type
                                </h3>
                                <div class="ac-store-types-grid-full">
                                    <a href="dropshipping.html" class="ac-store-type-card">
                                        <div class="ac-store-type-icon">
                                            <i class="fas fa-box"></i>
                                        </div>
                                        <div class="ac-store-type-content">
                                            <span class="ac-store-type-title">Dropshipping</span>
                                            <span class="ac-store-type-desc">Bulk optimize unlimited SKUs</span>
                                        </div>
                                    </a>
                                    <a href="fashion.html" class="ac-store-type-card">
                                        <div class="ac-store-type-icon">
                                            <i class="fas fa-tshirt"></i>
                                        </div>
                                        <div class="ac-store-type-content">
                                            <span class="ac-store-type-title">Fashion & Apparel</span>
                                            <span class="ac-store-type-desc">Style-aware descriptions</span>
                                        </div>
                                    </a>
                                    <a href="beauty.html" class="ac-store-type-card">
                                        <div class="ac-store-type-icon">
                                            <i class="fas fa-palette"></i>
                                        </div>
                                        <div class="ac-store-type-content">
                                            <span class="ac-store-type-title">Beauty & Cosmetics</span>
                                            <span class="ac-store-type-desc">Ingredient-focused SEO</span>
                                        </div>
                                    </a>
                                    <a href="tech.html" class="ac-store-type-card">
                                        <div class="ac-store-type-icon">
                                            <i class="fas fa-laptop"></i>
                                        </div>
                                        <div class="ac-store-type-content">
                                            <span class="ac-store-type-title">Electronics & Tech</span>
                                            <span class="ac-store-type-desc">Spec-optimized content</span>
                                        </div>
                                    </a>
                                    <a href="home-decor.html" class="ac-store-type-card">
                                        <div class="ac-store-type-icon">
                                            <i class="fas fa-home"></i>
                                        </div>
                                        <div class="ac-store-type-content">
                                            <span class="ac-store-type-title">Home & Decor</span>
                                            <span class="ac-store-type-desc">Room & style targeting</span>
                                        </div>
                                    </a>
                                    <a href="jewelry.html" class="ac-store-type-card">
                                        <div class="ac-store-type-icon">
                                            <i class="fas fa-gem"></i>
                                        </div>
                                        <div class="ac-store-type-content">
                                            <span class="ac-store-type-title">Jewelry & Accessories</span>
                                            <span class="ac-store-type-desc">Luxury keyword focus</span>
                                        </div>
                                    </a>
                                    <a href="fitness.html" class="ac-store-type-card">
                                        <div class="ac-store-type-icon">
                                            <i class="fas fa-dumbbell"></i>
                                        </div>
                                        <div class="ac-store-type-content">
                                            <span class="ac-store-type-title">Sports & Fitness</span>
                                            <span class="ac-store-type-desc">Performance keywords</span>
                                        </div>
                                    </a>
                                    <a href="pets.html" class="ac-store-type-card">
                                        <div class="ac-store-type-icon">
                                            <i class="fas fa-paw"></i>
                                        </div>
                                        <div class="ac-store-type-content">
                                            <span class="ac-store-type-title">Pet Supplies</span>
                                            <span class="ac-store-type-desc">Pet parent targeting</span>
                                        </div>
                                    </a>
                                    <a href="food.html" class="ac-store-type-card">
                                        <div class="ac-store-type-icon">
                                            <i class="fas fa-utensils"></i>
                                        </div>
                                        <div class="ac-store-type-content">
                                            <span class="ac-store-type-title">Food & Beverage</span>
                                            <span class="ac-store-type-desc">Taste & dietary SEO</span>
                                        </div>
                                    </a>
                                    <a href="gaming.html" class="ac-store-type-card">
                                        <div class="ac-store-type-icon">
                                            <i class="fas fa-gamepad"></i>
                                        </div>
                                        <div class="ac-store-type-content">
                                            <span class="ac-store-type-title">Gaming & Hobbies</span>
                                            <span class="ac-store-type-desc">Enthusiast targeting</span>
                                        </div>
                                    </a>
                                    <a href="baby.html" class="ac-store-type-card">
                                        <div class="ac-store-type-icon">
                                            <i class="fas fa-baby"></i>
                                        </div>
                                        <div class="ac-store-type-content">
                                            <span class="ac-store-type-title">Baby & Kids</span>
                                            <span class="ac-store-type-desc">Parent-focused SEO</span>
                                        </div>
                                    </a>
                                    <a href="eco.html" class="ac-store-type-card">
                                        <div class="ac-store-type-icon">
                                            <i class="fas fa-leaf"></i>
                                        </div>
                                        <div class="ac-store-type-content">
                                            <span class="ac-store-type-title">Eco-Friendly</span>
                                            <span class="ac-store-type-desc">Sustainability keywords</span>
                                        </div>
                                    </a>
                                </div>
                                
                                <!-- By Problem Section -->
                                <h3 class="ac-mega-column-header" style="margin-top: 2rem;">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    Solutions By Problem
                                </h3>
                                <div class="ac-problem-grid">
                                    <a href="shopify-seo-problems.html" class="ac-problem-card">
                                        <div class="ac-problem-icon">
                                            <i class="fas fa-times-circle"></i>
                                        </div>
                                        <div class="ac-problem-content">
                                            <span class="ac-problem-title">No Organic Traffic</span>
                                            <span class="ac-problem-desc">Fix visibility issues & start ranking</span>
                                        </div>
                                    </a>
                                    <a href="shopify-seo-for-beginners.html" class="ac-problem-card">
                                        <div class="ac-problem-icon">
                                            <i class="fas fa-clock"></i>
                                        </div>
                                        <div class="ac-problem-content">
                                            <span class="ac-problem-title">No Time for SEO</span>
                                            <span class="ac-problem-desc">24/7 automation handles everything</span>
                                        </div>
                                    </a>
                                    <a href="shopify-seo-guide.html" class="ac-problem-card">
                                        <div class="ac-problem-icon">
                                            <i class="fas fa-question-circle"></i>
                                        </div>
                                        <div class="ac-problem-content">
                                            <span class="ac-problem-title">Don't Know SEO</span>
                                            <span class="ac-problem-desc">We handle everything automatically</span>
                                        </div>
                                    </a>
                                </div>
                            </div>
                            
                            </div>
                            
                            <!-- Featured Section -->
                            <div class="ac-mega-featured">
                                <div class="ac-mega-featured-content">
                                    <h4 class="ac-mega-featured-title">📊 Get Your Free SEO Audit</h4>
                                    <p class="ac-mega-featured-desc">
                                        See exactly what's holding your store back and how to fix it
                                    </p>
                                    <div class="ac-mega-featured-links">
                                        <a href="pre-registration.html" class="ac-mega-featured-link">
                                            Get Early Access
                                            <i class="fas fa-arrow-right"></i>
                                        </a>
                                        <a href="shopify-dropshipping-case-study.html" class="ac-mega-featured-link">
                                            Success Stories
                                            <i class="fas fa-arrow-right"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Resources Dropdown -->
                <div class="ac-nav-dropdown">
                    <a href="#" class="ac-nav-link ac-dropdown-toggle">Resources</a>
                    <div class="ac-mega-menu">
                        <div class="ac-mega-menu-inner">
                            <div class="ac-mega-grid">
                            <!-- Learn SEO -->
                            <div class="ac-mega-column">
                                <h3 class="ac-mega-column-header">
                                    <i class="fas fa-graduation-cap"></i>
                                    Learn SEO
                                </h3>
                                <a href="shopify-seo-guide.html" class="ac-mega-item">
                                    <div class="ac-mega-icon">
                                        <i class="fas fa-book"></i>
                                    </div>
                                    <div class="ac-mega-content">
                                        <span class="ac-mega-title">Ultimate Guide</span>
                                        <span class="ac-mega-desc">Everything you need</span>
                                    </div>
                                </a>
                                <a href="shopify-seo-for-beginners.html" class="ac-mega-item">
                                    <div class="ac-mega-icon">
                                        <i class="fas fa-user-graduate"></i>
                                    </div>
                                    <div class="ac-mega-content">
                                        <span class="ac-mega-title">Beginners Course</span>
                                        <span class="ac-mega-desc">Start from scratch</span>
                                    </div>
                                </a>
                                <a href="shopify-seo-checklist.html" class="ac-mega-item">
                                    <div class="ac-mega-icon">
                                        <i class="fas fa-check-square"></i>
                                    </div>
                                    <div class="ac-mega-content">
                                        <span class="ac-mega-title">SEO Checklist</span>
                                        <span class="ac-mega-desc">Step-by-step guide</span>
                                    </div>
                                </a>
                                <a href="shopify-seo-problems.html" class="ac-mega-item">
                                    <div class="ac-mega-icon">
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </div>
                                    <div class="ac-mega-content">
                                        <span class="ac-mega-title">Common Problems</span>
                                        <span class="ac-mega-desc">Quick fixes</span>
                                    </div>
                                </a>
                            </div>
                            
                            <!-- From the Blog -->
                            <div class="ac-mega-column">
                                <h3 class="ac-mega-column-header">
                                    <i class="fas fa-rss"></i>
                                    From the Blog
                                </h3>
                                <a href="blog/how-ai-is-revolutionizing-shopify-seo-2025.html" class="ac-blog-item">
                                    <div class="ac-blog-meta">
                                        <span class="ac-blog-date">Dec 18, 2024</span>
                                        <span class="ac-blog-read">8 min read</span>
                                    </div>
                                    <h4 class="ac-blog-title">How AI is Revolutionizing Shopify SEO in 2025</h4>
                                    <p class="ac-blog-excerpt">Discover how artificial intelligence is transforming e-commerce SEO...</p>
                                </a>
                                <a href="blog/10-shopify-seo-mistakes-costing-you-sales.html" class="ac-blog-item">
                                    <div class="ac-blog-meta">
                                        <span class="ac-blog-date">Dec 17, 2024</span>
                                        <span class="ac-blog-read">10 min read</span>
                                    </div>
                                    <h4 class="ac-blog-title">10 Shopify SEO Mistakes That Cost You Sales</h4>
                                    <p class="ac-blog-excerpt">Avoid these common pitfalls that prevent your store from ranking...</p>
                                </a>
                                <a href="blog/welcome-to-grove-ai.html" class="ac-blog-item">
                                    <div class="ac-blog-meta">
                                        <span class="ac-blog-date">Dec 19, 2024</span>
                                        <span class="ac-blog-read">3 min read</span>
                                    </div>
                                    <h4 class="ac-blog-title">Welcome to Grove AI</h4>
                                    <p class="ac-blog-excerpt">A note from our founder on revolutionizing Shopify SEO...</p>
                                </a>
                                <a href="blog.html" class="ac-blog-view-all">
                                    View All Articles
                                    <i class="fas fa-arrow-right"></i>
                                </a>
                            </div>
                            
                            <!-- Tools -->
                            <div class="ac-mega-column">
                                <h3 class="ac-mega-column-header">
                                    <i class="fas fa-tools"></i>
                                    Tools
                                </h3>
                                <a href="shopify-product-description-seo.html" class="ac-mega-item">
                                    <div class="ac-mega-icon">
                                        <i class="fas fa-pen"></i>
                                    </div>
                                    <div class="ac-mega-content">
                                        <span class="ac-mega-title">Product SEO Guide</span>
                                        <span class="ac-mega-desc">Optimize descriptions</span>
                                    </div>
                                </a>
                                <a href="shopify-technical-seo.html" class="ac-mega-item">
                                    <div class="ac-mega-icon">
                                        <i class="fas fa-cog"></i>
                                    </div>
                                    <div class="ac-mega-content">
                                        <span class="ac-mega-title">Technical SEO</span>
                                        <span class="ac-mega-desc">Site health check</span>
                                    </div>
                                </a>
                            </div>
                            </div>
                            
                            <!-- Featured Section -->
                            <div class="ac-mega-featured">
                                <div class="ac-mega-featured-content">
                                    <h4 class="ac-mega-featured-title">📚 Master Shopify SEO</h4>
                                    <p class="ac-mega-featured-desc">
                                        Get our complete library of guides, tools, and insights
                                    </p>
                                    <div class="ac-mega-featured-links">
                                        <a href="shopify-seo-guide.html" class="ac-mega-featured-link">
                                            Download Free Guide
                                            <i class="fas fa-arrow-right"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Pricing Link -->
                <a href="pricing.html" class="ac-nav-link">Pricing</a>
                
                <!-- Company Dropdown -->
                <div class="ac-nav-dropdown">
                    <a href="about.html" class="ac-nav-link ac-dropdown-toggle">Company</a>
                    <div class="ac-company-menu">
                        <a href="about.html" class="ac-company-item">
                            <div class="ac-company-icon">
                                <i class="fas fa-building"></i>
                            </div>
                            <div class="ac-company-content">
                                <span class="ac-company-title">About Grove</span>
                                <span class="ac-company-desc">Our story, mission & values</span>
                            </div>
                        </a>
                        <a href="contact.html" class="ac-company-item">
                            <div class="ac-company-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="ac-company-content">
                                <span class="ac-company-title">Contact Us</span>
                                <span class="ac-company-desc">Get help or say hello</span>
                            </div>
                        </a>
                    </div>
                </div>
                
                <!-- CTAs -->
                <div class="ac-nav-cta">
                    <a href="auth/login.html" class="ac-btn ac-btn-secondary">Login</a>
                    <a href="pre-registration.html" class="ac-btn ac-btn-primary">Join Waitlist</a>
                </div>
            </div>
            
            <!-- Mobile Menu Toggle -->
            <button class="ac-mobile-toggle" aria-label="Toggle menu">
                <i class="fas fa-bars"></i>
            </button>
        </div>
    </nav>
    
    <!-- Mobile Menu Overlay -->
    <div class="ac-mobile-menu" style="display: none;">
        <div class="ac-mobile-header">
            <a href="index.html" class="ac-logo">
                <img src="static/images/grove-logo.png" alt="Grove" style="height: 36px; width: auto;">
            </a>
            <button class="ac-mobile-close">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="ac-mobile-nav">
            <a href="index.html" class="ac-mobile-item">Home</a>
            <a href="features.html" class="ac-mobile-item">Features</a>
            <a href="pricing.html" class="ac-mobile-item">Pricing</a>
            <a href="about.html" class="ac-mobile-item">About</a>
            <a href="contact.html" class="ac-mobile-item">Contact</a>
            <div style="padding: 1rem 0;">
                <a href="pre-registration.html" class="ac-btn ac-btn-primary ac-btn-block">Join Waitlist</a>
            </div>
        </div>
    </div>
    
    <!-- Main Content -->
    <main>
        

<!-- Hero Section -->
<section class="ac-hero">
    <div class="ac-hero-container">
        <!-- Static Blob - Independent from animations -->
        <div class="ac-hero-blob"></div>
        <div class="ac-hero-grid">
            <div class="ac-hero-content" style="padding-top: 4rem;">
                <h1>
                    Replace Your <span class="ac-hero-highlight">$5,000/mo SEO Agency</span> 
                    with AI That Actually Works
                </h1>
                <p class="ac-hero-subtitle">
                    Stop paying agencies that deliver Excel sheets instead of results. Grove 
                    optimizes your entire Shopify catalog 24/7, creating content that ranks and converts.
                </p>
                
                <!-- Trust Badges -->
                <div class="ac-trust-badges">
                    <div class="ac-badge">
                        <i class="fab fa-shopify ac-badge-icon"></i>
                        <span>Made for Shopify</span>
                    </div>
                    <div class="ac-badge">
                        <i class="fas fa-dollar-sign ac-badge-icon"></i>
                        <span>Save $60k+/year</span>
                    </div>
                    <div class="ac-badge">
                        <i class="fas fa-brain ac-badge-icon"></i>
                        <span>Fully Agentic</span>
                    </div>
                </div>
                
                <!-- URL Analysis Form -->
                <div class="ac-url-analysis">
                    <form id="url-analysis-form" class="ac-url-form">
                        <div class="ac-url-input-group">
                            <input type="text" 
                                   id="store-url" 
                                   class="ac-url-input" 
                                   placeholder="Enter your Shopify store URL" 
                                   required>
                            <button type="submit" class="ac-btn ac-btn-primary ac-btn-lg">
                                Reserve Your Spot
                                <i class="fas fa-arrow-right"></i>
                            </button>
                        </div>
                        <div class="ac-shopify-hint">
                            <i class="fas fa-arrow-up" style="color: var(--ac-grove); font-size: 1rem; margin-right: 0.5rem;"></i>
                            <i class="fab fa-shopify" style="color: var(--ac-grove);"></i>
                            <span style="color: var(--ac-grove); font-weight: 500;">Get access to the world's first agentic SEO app for Shopify</span>
                        </div>
                    </form>
                </div>
            </div>
            
            <div class="ac-hero-visual">
                <style>
                    /* Force dots to be visible in hero section */
                    .ac-hero-visual .ac-console-dots {
                        display: flex !important;
                        gap: 0.5rem !important;
                        visibility: visible !important;
                        opacity: 1 !important;
                        z-index: 999 !important;
                    }
                    .ac-hero-visual .ac-dot {
                        width: 12px !important;
                        height: 12px !important;
                        border-radius: 50% !important;
                        display: block !important;
                        visibility: visible !important;
                        opacity: 1 !important;
                    }
                    .ac-hero-visual .ac-dot-red {
                        background: #ff5f57 !important;
                    }
                    .ac-hero-visual .ac-dot-yellow {
                        background: #ffbd2e !important;
                    }
                    .ac-hero-visual .ac-dot-green {
                        background: #28ca42 !important;
                    }
                </style>
                <div class="ac-browser-mockup ac-hero-dashboard" style="min-height: 440px; display: flex; flex-direction: column; position: relative;">
                    <div class="ac-browser-header">
                        <div class="ac-console-dots">
                            <span class="ac-dot ac-dot-red"></span>
                            <span class="ac-dot ac-dot-yellow"></span>
                            <span class="ac-dot ac-dot-green"></span>
                        </div>
                        <div style="position: absolute; left: 50%; transform: translateX(-50%); font-size: 0.625rem; color: #999;">grove.ai/automations</div>
                    </div>
                    <div class="ac-browser-content" style="padding: 1rem; background: #f8f9fa; position: relative; flex: 1; display: flex; flex-direction: column; overflow: hidden;">
                        <!-- Dashboard Header -->
                        <div style="margin-bottom: 1.25rem;">
                            <h4 style="font-size: 1.375rem; color: #1a1a1a; margin: 0 0 0.375rem; font-weight: 700;">AI Automation Center</h4>
                            <p style="color: #666; margin: 0; font-size: 0.938rem;">Toggle on = AI starts working</p>
                        </div>
                        
                        <!-- Animated Toggle Cards -->
                        <div style="display: grid; grid-template-columns: 1fr; gap: 1rem; flex: 1; align-content: start;">
                            <!-- Product Optimization Toggle -->
                            <div class="ac-hero-toggle-card" id="hero-product-card" style="padding: 1.5rem;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.75rem;">
                                    <div>
                                        <h5 style="font-size: 1.125rem; font-weight: 700; color: #1a1a1a; margin: 0 0 0.375rem; display: flex; align-items: center; gap: 0.5rem;">
                                            <i class="fas fa-box" style="color: var(--ac-grove); font-size: 0.875rem;"></i>
                                            Product SEO
                                            <i class="fas fa-sync-alt ac-automation-icon" style="color: var(--ac-grove); font-size: 0.875rem; animation: spin 2s linear infinite; display: none; margin-left: 0.5rem;"></i>
                                        </h5>
                                        <p style="font-size: 0.813rem; color: #666; margin: 0;">Rewrite descriptions & meta tags</p>
                                    </div>
                                    <div class="ac-hero-toggle-switch">
                                        <div class="ac-hero-toggle-slider">
                                            <span class="ac-hero-toggle-handle"></span>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Animated Feed -->
                                <div class="ac-hero-feed" style="opacity: 0; max-height: 0; overflow: hidden; transition: all 0.3s ease;">
                                    <div class="ac-hero-feed-item" style="animation-delay: 0s;">
                                        <i class="fas fa-check-circle" style="color: var(--ac-grove);"></i>
                                        <span style="font-size: 0.875rem; color: #333;">Optimizing "Summer Dress"...</span>
                                    </div>
                                    <div class="ac-hero-feed-item" style="animation-delay: 0.3s;">
                                        <i class="fas fa-star" style="color: #fbbf24; width: 1.25rem; height: 1.25rem; display: inline-flex; align-items: center; justify-content: center;"></i>
                                        <span style="font-size: 0.875rem; color: #333;">5 keywords added</span>
                                    </div>
                                    <div class="ac-hero-feed-item ac-hero-metric" style="animation-delay: 0.6s;">
                                        <i class="fas fa-arrow-up" style="color: var(--ac-grove);"></i>
                                        <span style="font-size: 0.875rem; color: var(--ac-grove);">+47% visibility</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Collection Creator Toggle -->
                            <div class="ac-hero-toggle-card" id="hero-collection-card" style="padding: 1.5rem;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.75rem;">
                                    <div>
                                        <h5 style="font-size: 1.125rem; font-weight: 700; color: #1a1a1a; margin: 0 0 0.375rem; display: flex; align-items: center; gap: 0.5rem;">
                                            <i class="fas fa-layer-group" style="color: var(--ac-grove); font-size: 0.875rem;"></i>
                                            Smart Collections
                                            <i class="fas fa-sync-alt ac-automation-icon" style="color: var(--ac-grove); font-size: 0.875rem; animation: spin 2s linear infinite; display: none; margin-left: 0.5rem;"></i>
                                        </h5>
                                        <p style="font-size: 0.813rem; color: #666; margin: 0;">Create trending collections</p>
                                    </div>
                                    <div class="ac-hero-toggle-switch">
                                        <div class="ac-hero-toggle-slider">
                                            <span class="ac-hero-toggle-handle"></span>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Animated Feed -->
                                <div class="ac-hero-feed" style="opacity: 0; max-height: 0; overflow: hidden; transition: all 0.3s ease;">
                                    <div class="ac-hero-feed-item" style="animation-delay: 0s;">
                                        <i class="fas fa-fire" style="color: #ef4444;"></i>
                                        <span style="font-size: 0.875rem; color: #333;">"Valentine's Gifts" created</span>
                                    </div>
                                    <div class="ac-hero-feed-item" style="animation-delay: 0.3s;">
                                        <i class="fas fa-layer-group" style="color: var(--ac-grove); width: 1.25rem; height: 1.25rem; display: inline-flex; align-items: center; justify-content: center;"></i>
                                        <span style="font-size: 0.875rem; color: #333;">12 collections live</span>
                                    </div>
                                    <div class="ac-hero-feed-item ac-hero-metric" style="animation-delay: 0.6s;">
                                        <i class="fas fa-chart-line" style="color: var(--ac-grove);"></i>
                                        <span style="font-size: 0.875rem; color: var(--ac-grove);">+234% traffic</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Mini Metrics Bar -->
                        <div class="ac-hero-metrics-bar" style="opacity: 0; max-height: 0; overflow: hidden; transition: all 0.3s ease;">
                            <div class="ac-hero-mini-metric">
                                <span class="ac-hero-metric-value">156</span>
                                <span class="ac-hero-metric-label">Optimized</span>
                            </div>
                            <div class="ac-hero-mini-metric">
                                <span class="ac-hero-metric-value">89</span>
                                <span class="ac-hero-metric-label">Keywords</span>
                            </div>
                            <div class="ac-hero-mini-metric">
                                <span class="ac-hero-metric-value">$8.4K</span>
                                <span class="ac-hero-metric-label">Revenue</span>
                            </div>
                        </div>
                        
                        <!-- Live indicator -->
                        <div class="ac-live-indicator" style="display: none; position: absolute; top: 1rem; right: 1rem; background: #ef4444; color: white; padding: 0.25rem 0.5rem; border-radius: 12px; font-size: 0.625rem; font-weight: 600; align-items: center; gap: 0.375rem;">
                            <span style="display: inline-block; width: 6px; height: 6px; background: white; border-radius: 50%; animation: pulse 1.5s ease-in-out infinite;"></span>
                            LIVE
                        </div>
                        
                        <!-- Notification container -->
                        <div class="ac-hero-notifications" style="position: absolute; right: 0; top: 3rem; width: 250px; pointer-events: none; z-index: 10;"></div>
                    </div>
                </div>
                
                <!-- Performance Metrics -->
                <div class="ac-metrics">
                    <div class="ac-metric">
                        <div class="ac-metric-value">10X</div>
                        <div class="ac-metric-label">Cheaper than agencies</div>
                    </div>
                    <div class="ac-metric">
                        <div class="ac-metric-value">24/7</div>
                        <div class="ac-metric-label">SEO machine</div>
                    </div>
                    <div class="ac-metric">
                        <div class="ac-metric-value">2.8X</div>
                        <div class="ac-metric-label">More traffic on average</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Logo Slider -->
<section class="ac-logos-section">
    <p class="ac-logos-title">Proudly Built for Shopify Stores</p>
    <div class="ac-logos-slider">
        <!-- First set -->
        <div class="ac-logo-item"><img src="static/images/client-logos/claddaghring.png" alt="Claddagh Ring"></div>
        <div class="ac-logo-item"><img src="static/images/client-logos/aewilliamsshop.png" alt="A.E. Williams"></div>
        <div class="ac-logo-item"><img src="static/images/client-logos/dapperfam.png" alt="Dapper Fam"></div>
        <div class="ac-logo-item"><img src="static/images/client-logos/ridertack.png" alt="Rider Tack"></div>
        <div class="ac-logo-item"><img src="static/images/client-logos/headsets4business.png" alt="Headsets 4 Business"></div>
        <div class="ac-logo-item"><img src="static/images/client-logos/homeandroost.png" alt="Home and Roost"></div>
        <div class="ac-logo-item"><img src="static/images/client-logos/montenativo.png" alt="Monte Nativo"></div>
        <div class="ac-logo-item"><img src="static/images/client-logos/mattressland.png" alt="Mattress Land"></div>
        <div class="ac-logo-item"><img src="static/images/client-logos/2men.png" alt="2MEN Italian Fashion"></div>
        <!-- Second set for seamless loop -->
        <div class="ac-logo-item"><img src="static/images/client-logos/claddaghring.png" alt="Claddagh Ring"></div>
        <div class="ac-logo-item"><img src="static/images/client-logos/aewilliamsshop.png" alt="A.E. Williams"></div>
        <div class="ac-logo-item"><img src="static/images/client-logos/dapperfam.png" alt="Dapper Fam"></div>
        <div class="ac-logo-item"><img src="static/images/client-logos/ridertack.png" alt="Rider Tack"></div>
        <div class="ac-logo-item"><img src="static/images/client-logos/headsets4business.png" alt="Headsets 4 Business"></div>
        <div class="ac-logo-item"><img src="static/images/client-logos/homeandroost.png" alt="Home and Roost"></div>
        <div class="ac-logo-item"><img src="static/images/client-logos/montenativo.png" alt="Monte Nativo"></div>
        <div class="ac-logo-item"><img src="static/images/client-logos/mattressland.png" alt="Mattress Land"></div>
        <div class="ac-logo-item"><img src="static/images/client-logos/2men.png" alt="2MEN Italian Fashion"></div>
        <!-- Third set for longer duration -->
        <div class="ac-logo-item"><img src="static/images/client-logos/claddaghring.png" alt="Claddagh Ring"></div>
        <div class="ac-logo-item"><img src="static/images/client-logos/aewilliamsshop.png" alt="A.E. Williams"></div>
        <div class="ac-logo-item"><img src="static/images/client-logos/dapperfam.png" alt="Dapper Fam"></div>
        <div class="ac-logo-item"><img src="static/images/client-logos/ridertack.png" alt="Rider Tack"></div>
        <div class="ac-logo-item"><img src="static/images/client-logos/headsets4business.png" alt="Headsets 4 Business"></div>
        <div class="ac-logo-item"><img src="static/images/client-logos/homeandroost.png" alt="Home and Roost"></div>
        <div class="ac-logo-item"><img src="static/images/client-logos/montenativo.png" alt="Monte Nativo"></div>
        <div class="ac-logo-item"><img src="static/images/client-logos/mattressland.png" alt="Mattress Land"></div>
        <div class="ac-logo-item"><img src="static/images/client-logos/2men.png" alt="2MEN Italian Fashion"></div>
    </div>
</section>


<!-- SEO Analysis Popup -->
<div class="ac-analysis-popup" id="analysis-popup">
    <div class="ac-popup-overlay" onclick="closeAnalysisPopup()"></div>
    <div class="ac-popup-content">
        <!-- PDF Header Bar -->
        <div class="ac-pdf-header">
            <div class="ac-pdf-title">
                <i class="fas fa-file-pdf"></i>
                <span>SEO Opportunity Report</span>
            </div>
            <div class="ac-pdf-actions">
                <div class="ac-pdf-status" id="pdf-status">
                    <i class="fas fa-circle-notch fa-spin"></i>
                    <span>Generating report...</span>
                </div>
                <button class="ac-popup-close" onclick="closeAnalysisPopup()">×</button>
            </div>
        </div>
        
        <!-- PDF Document Content -->
        <div class="ac-pdf-document">
            <div class="ac-analysis-header">
                <div class="ac-analysis-loading" id="analysis-loading">
                    <div class="ac-loading-spinner"></div>
                    <h3>Analyzing Your Store...</h3>
                    <p>Discovering SEO opportunities</p>
                    <div class="ac-generation-progress">
                        <div class="ac-progress-bar"></div>
                    </div>
                </div>
                
                <div class="ac-analysis-complete" id="analysis-complete" style="display: none;">
                    <div class="ac-report-date" id="report-date"></div>
                    <div class="ac-store-info">
                        <i class="fab fa-shopify"></i>
                        <h3 id="store-name">Your Store</h3>
                    </div>
                    <div class="ac-score-meter">
                        <div class="ac-score-circle">
                            <svg width="120" height="120">
                                <circle cx="60" cy="60" r="54" stroke="#e0e0e0" stroke-width="12" fill="none"/>
                                <circle cx="60" cy="60" r="54" stroke="#ea4335" stroke-width="12" fill="none"
                                        stroke-dasharray="339" stroke-dashoffset="237" 
                                        transform="rotate(-90 60 60)"/>
                            </svg>
                            <div class="ac-score-value">32</div>
                        </div>
                        <p class="ac-score-label">SEO Score</p>
                    </div>
                </div>
            </div>
            
            <div class="ac-analysis-body" id="analysis-body" style="display: none;">
            <!-- Executive Summary -->
            <div class="ac-executive-summary">
                <h4>Executive Summary</h4>
                <div class="ac-summary-content">
                    <p>Our comprehensive analysis of your Shopify store has identified <strong>15 critical SEO issues</strong> that are preventing you from ranking on page 1 of Google. These issues are costing you an estimated <strong>$8,400 per month</strong> in lost revenue.</p>
                    <p>The good news: Every single issue can be fixed automatically with Grove's AI-powered automation. Most stores see significant improvements within 30 days.</p>
                </div>
            </div>
            
            <!-- Performance Benchmarks -->
            <div class="ac-benchmarks">
                <h4>📊 Performance vs Industry Standards</h4>
                <div class="ac-benchmark-grid">
                    <div class="ac-benchmark-item">
                        <div class="ac-benchmark-label">Page Load Speed</div>
                        <div class="ac-benchmark-bar">
                            <div class="ac-benchmark-fill" style="width: 30%; background: #ea4335;"></div>
                        </div>
                        <div class="ac-benchmark-values">
                            <span class="ac-your-value">4.2s</span>
                            <span class="ac-industry-value">Industry: 2.1s</span>
                        </div>
                    </div>
                    <div class="ac-benchmark-item">
                        <div class="ac-benchmark-label">Mobile Optimization</div>
                        <div class="ac-benchmark-bar">
                            <div class="ac-benchmark-fill" style="width: 45%; background: #fbbc04;"></div>
                        </div>
                        <div class="ac-benchmark-values">
                            <span class="ac-your-value">45%</span>
                            <span class="ac-industry-value">Industry: 85%</span>
                        </div>
                    </div>
                    <div class="ac-benchmark-item">
                        <div class="ac-benchmark-label">Content Quality Score</div>
                        <div class="ac-benchmark-bar">
                            <div class="ac-benchmark-fill" style="width: 25%; background: #ea4335;"></div>
                        </div>
                        <div class="ac-benchmark-values">
                            <span class="ac-your-value">C-</span>
                            <span class="ac-industry-value">Industry: A</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="ac-issues-found">
                <h4>🚨 Critical Issues Found</h4>
                
                <div class="ac-issue-item error">
                    <i class="fas fa-times-circle"></i>
                    <div class="ac-issue-content">
                        <strong>Only 11 collections found</strong>
                        <p>Missing out on 50+ trending search opportunities. Competitors have 60+ collections.</p>
                    </div>
                </div>
                
                <div class="ac-issue-item error">
                    <i class="fas fa-times-circle"></i>
                    <div class="ac-issue-content">
                        <strong>Single language detected</strong>
                        <p>Huge opportunity missed! Multi-language stores see 3.2x more organic traffic.</p>
                    </div>
                </div>
                
                <div class="ac-issue-item warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <div class="ac-issue-content">
                        <strong>67% of products missing meta descriptions</strong>
                        <p>Google can't understand what you're selling. Easy fix with massive impact.</p>
                    </div>
                </div>
                
                <div class="ac-issue-item warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <div class="ac-issue-content">
                        <strong>No schema markup found</strong>
                        <p>Your products are invisible to Google Shopping. Missing rich snippets.</p>
                    </div>
                </div>
                
                <div class="ac-issue-item info">
                    <i class="fas fa-info-circle"></i>
                    <div class="ac-issue-content">
                        <strong>Average page load: 4.2 seconds</strong>
                        <p>53% of visitors leave sites that take over 3 seconds to load.</p>
                    </div>
                </div>
            </div>
            
            <div class="ac-opportunity-summary">
                <h4>💰 Your Revenue Opportunity</h4>
                <div class="ac-opportunity-box">
                    <div class="ac-opportunity-stat">
                        <div class="ac-opp-value">+$8,400</div>
                        <div class="ac-opp-label">Monthly revenue potential</div>
                    </div>
                    <div class="ac-opportunity-stat">
                        <div class="ac-opp-value">2,100</div>
                        <div class="ac-opp-label">New visitors per month</div>
                    </div>
                </div>
            </div>
            
            <!-- Competitor Comparison -->
            <div class="ac-competitor-comparison">
                <h4>🎯 How You Compare to Top Competitors</h4>
                <div class="ac-comparison-table">
                    <div class="ac-comparison-row header">
                        <div class="ac-comp-metric">Metric</div>
                        <div class="ac-comp-you">Your Store</div>
                        <div class="ac-comp-avg">Competitor Avg</div>
                        <div class="ac-comp-gap">Gap</div>
                    </div>
                    <div class="ac-comparison-row">
                        <div class="ac-comp-metric">Products with SEO</div>
                        <div class="ac-comp-you bad">33%</div>
                        <div class="ac-comp-avg">92%</div>
                        <div class="ac-comp-gap">-59%</div>
                    </div>
                    <div class="ac-comparison-row">
                        <div class="ac-comp-metric">Collection Pages</div>
                        <div class="ac-comp-you bad">11</div>
                        <div class="ac-comp-avg">68</div>
                        <div class="ac-comp-gap">-57</div>
                    </div>
                    <div class="ac-comparison-row">
                        <div class="ac-comp-metric">Organic Traffic</div>
                        <div class="ac-comp-you bad">892/mo</div>
                        <div class="ac-comp-avg">12,400/mo</div>
                        <div class="ac-comp-gap">-93%</div>
                    </div>
                    <div class="ac-comparison-row">
                        <div class="ac-comp-metric">Page 1 Rankings</div>
                        <div class="ac-comp-you bad">3</div>
                        <div class="ac-comp-avg">147</div>
                        <div class="ac-comp-gap">-144</div>
                    </div>
                </div>
                <p class="ac-comparison-note">*Based on analysis of 5 similar stores in your niche</p>
            </div>
            
            <div class="ac-analysis-cta">
                <h4>Grove can fix all these issues automatically</h4>
                <p>Join 147 Shopify stores already growing with AI-powered SEO</p>
                <a href="pre-registration.html" class="ac-btn ac-btn-primary ac-btn-block" onclick="closeAnalysisPopup()">
                    Join Waitlist to Fix These Issues
                    <i class="fas fa-arrow-right"></i>
                </a>
                <p class="ac-cta-note">3-day free trial • Exclusive benefits</p>
            </div>
        </div>
        </div>
    </div>
</div>

<!-- Automation Section (moved up) -->
<section class="ac-automation-section" style="position: relative;">
    <div class="ac-features-container" style="position: relative;">
        <div class="ac-section-header">
            <h2 class="ac-section-title">Set It Once. Watch Your Rankings Soar.</h2>
            <p class="ac-section-subtitle">
                Toggle on the automations you want. Our AI handles the rest 24/7. No maintenance required.
            </p>
        </div>
        
        <!-- AI Sparkles -->
        <div class="ac-ai-sparkle ac-sparkle-1">
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 0 L14 10 L24 12 L14 14 L12 24 L10 14 L0 12 L10 10 L12 0 Z" fill="#ef2b70"/>
            </svg>
        </div>
        <div class="ac-ai-sparkle ac-sparkle-2 small">
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 0 L14 10 L24 12 L14 14 L12 24 L10 14 L0 12 L10 10 L12 0 Z" fill="#ef2b70"/>
            </svg>
        </div>
        <div class="ac-ai-sparkle ac-sparkle-3 large">
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 0 L14 10 L24 12 L14 14 L12 24 L10 14 L0 12 L10 10 L12 0 Z" fill="#ef2b70"/>
            </svg>
        </div>
        <div class="ac-ai-sparkle ac-sparkle-4">
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 0 L14 10 L24 12 L14 14 L12 24 L10 14 L0 12 L10 10 L12 0 Z" fill="#ef2b70"/>
            </svg>
        </div>
        <div class="ac-ai-sparkle ac-sparkle-5 small">
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 0 L14 10 L24 12 L14 14 L12 24 L10 14 L0 12 L10 10 L12 0 Z" fill="#ef2b70"/>
            </svg>
        </div>
        <div class="ac-ai-sparkle ac-sparkle-6">
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 0 L14 10 L24 12 L14 14 L12 24 L10 14 L0 12 L10 10 L12 0 Z" fill="#ef2b70"/>
            </svg>
        </div>
        <!-- Desktop-only sparkle on left side of heading -->
        <div class="ac-ai-sparkle ac-sparkle-7 desktop-only large" style="position: absolute; left: 5%; top: 8%; animation-delay: 3.5s;">
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 0 L14 10 L24 12 L14 14 L12 24 L10 14 L0 12 L10 10 L12 0 Z" fill="#ef2b70"/>
            </svg>
        </div>
        
        <div class="ac-ai-console-rounded">
            <div class="ac-console-header-rounded">
                <div class="ac-console-dots" style="display: flex; gap: 0.5rem;">
                    <span class="ac-dot ac-dot-red" style="width: 12px; height: 12px; border-radius: 50%; background: #ff5f57; display: block;"></span>
                    <span class="ac-dot ac-dot-yellow" style="width: 12px; height: 12px; border-radius: 50%; background: #ffbd2e; display: block;"></span>
                    <span class="ac-dot ac-dot-green" style="width: 12px; height: 12px; border-radius: 50%; background: #28ca42; display: block;"></span>
                </div>
                <div class="ac-console-status">
                    <span class="ac-status-indicator-rounded"></span>
                    <span>AI Automations Active</span>
                </div>
            </div>
            
            <div class="ac-ai-grid-rounded">
                <!-- Product SEO Agent Rounded -->
                <div class="ac-ai-agent-card-rounded" id="automation-products">
                <div class="ac-agent-header">
                    <div class="ac-agent-avatar-rounded">
                        <i class="fas fa-brain"></i>
                        <span class="ac-agent-status-ring-rounded"></span>
                    </div>
                    <div class="ac-agent-info">
                        <h3 class="ac-agent-name">Product SEO Engine</h3>
                    </div>
                    <label class="ac-agent-toggle-rounded">
                        <input type="checkbox" id="automation-toggle-products" onchange="toggleAutomation('products')">
                        <span class="ac-toggle-slider-rounded"></span>
                    </label>
                </div>
                
                <ul class="ac-automation-features">
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-pen"></i>
                        </div>
                        <span class="ac-automation-feature-text">AI-powered descriptions</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-tag"></i>
                        </div>
                        <span class="ac-automation-feature-text">Meta tag optimization</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-image"></i>
                        </div>
                        <span class="ac-automation-feature-text">Smart alt tags</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-sync"></i>
                        </div>
                        <span class="ac-automation-feature-text">Daily updates</span>
                    </li>
                </ul>
                
                <div class="ac-agent-live-strip">
                    <span class="ac-live-indicator"></span>
                    <span class="ac-live-text">Analyzing 1,247 products...</span>
                </div>
            </div>
            
            <!-- Collections AI Agent Rounded -->
            <div class="ac-ai-agent-card-rounded" id="automation-collections">
                <div class="ac-agent-header">
                    <div class="ac-agent-avatar-rounded">
                        <i class="fas fa-network-wired"></i>
                        <span class="ac-agent-status-ring-rounded"></span>
                    </div>
                    <div class="ac-agent-info">
                        <h3 class="ac-agent-name">Smart Collections</h3>
                    </div>
                    <label class="ac-agent-toggle-rounded">
                        <input type="checkbox" id="automation-toggle-collections" onchange="toggleAutomation('collections')">
                        <span class="ac-toggle-slider-rounded"></span>
                    </label>
                </div>
                
                <ul class="ac-automation-features">
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-layer-group"></i>
                        </div>
                        <span class="ac-automation-feature-text">Trending collections</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-calendar"></i>
                        </div>
                        <span class="ac-automation-feature-text">Seasonal updates</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-bullseye"></i>
                        </div>
                        <span class="ac-automation-feature-text">Keyword targeting</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <span class="ac-automation-feature-text">Performance-based</span>
                    </li>
                </ul>
                
                <div class="ac-agent-live-strip">
                    <span class="ac-live-indicator"></span>
                    <span class="ac-live-text">Found trending opportunity</span>
                </div>
            </div>
            
            <!-- Search Insights AI Agent Rounded -->
            <div class="ac-ai-agent-card-rounded" id="automation-search">
                <div class="ac-agent-header">
                    <div class="ac-agent-avatar-rounded">
                        <i class="fas fa-microscope"></i>
                        <span class="ac-agent-status-ring-rounded"></span>
                    </div>
                    <div class="ac-agent-info">
                        <h3 class="ac-agent-name">Search Insights</h3>
                    </div>
                    <label class="ac-agent-toggle-rounded">
                        <input type="checkbox" id="automation-toggle-search" onchange="toggleAutomation('search')">
                        <span class="ac-toggle-slider-rounded"></span>
                    </label>
                </div>
                
                <ul class="ac-automation-features">
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fab fa-google"></i>
                        </div>
                        <span class="ac-automation-feature-text">Search Console sync</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <span class="ac-automation-feature-text">Opportunity finder</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-rocket"></i>
                        </div>
                        <span class="ac-automation-feature-text">Position 1 targeting</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-trophy"></i>
                        </div>
                        <span class="ac-automation-feature-text">Daily tracking</span>
                    </li>
                </ul>
                
                <div class="ac-agent-live-strip">
                    <span class="ac-live-indicator"></span>
                    <span class="ac-live-text">Monitoring 1,892 keywords</span>
                </div>
            </div>
            
            <!-- Content Machine AI Agent Rounded -->
            <div class="ac-ai-agent-card-rounded" id="automation-content">
                <div class="ac-agent-header">
                    <div class="ac-agent-avatar-rounded">
                        <i class="fas fa-feather-alt"></i>
                        <span class="ac-agent-status-ring-rounded"></span>
                    </div>
                    <div class="ac-agent-info">
                        <h3 class="ac-agent-name">Content Machine</h3>
                    </div>
                    <label class="ac-agent-toggle-rounded">
                        <input type="checkbox" id="automation-toggle-content" onchange="toggleAutomation('content')">
                        <span class="ac-toggle-slider-rounded"></span>
                    </label>
                </div>
                
                <ul class="ac-automation-features">
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-newspaper"></i>
                        </div>
                        <span class="ac-automation-feature-text">Weekly blog posts</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-link"></i>
                        </div>
                        <span class="ac-automation-feature-text">Smart internal links</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-globe"></i>
                        </div>
                        <span class="ac-automation-feature-text">Multi-language content</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-book"></i>
                        </div>
                        <span class="ac-automation-feature-text">Topical authority</span>
                    </li>
                </ul>
                
                <div class="ac-agent-live-strip">
                    <span class="ac-live-indicator"></span>
                    <span class="ac-live-text">Writing buying guide...</span>
                </div>
            </div>
            
            <!-- Technical SEO -->
            <div class="ac-ai-agent-card-rounded" id="automation-technical">
                <div class="ac-agent-header">
                    <div class="ac-agent-avatar-rounded">
                        <i class="fas fa-cog"></i>
                        <span class="ac-agent-status-ring-rounded"></span>
                    </div>
                    <div class="ac-agent-info">
                        <h3 class="ac-agent-name">Technical SEO</h3>
                    </div>
                    <label class="ac-agent-toggle-rounded">
                        <input type="checkbox" id="automation-toggle-technical" onchange="toggleAutomation('technical')">
                        <span class="ac-toggle-slider-rounded"></span>
                    </label>
                </div>
                
                <ul class="ac-automation-features">
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-sitemap"></i>
                        </div>
                        <span class="ac-automation-feature-text">Smart sitemaps</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-code"></i>
                        </div>
                        <span class="ac-automation-feature-text">Schema markup</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                        <span class="ac-automation-feature-text">Speed monitoring</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <span class="ac-automation-feature-text">404 auto-fix</span>
                    </li>
                </ul>
                
                <div class="ac-agent-live-strip">
                    <span class="ac-live-indicator"></span>
                    <span class="ac-live-text">Optimizing site structure...</span>
                </div>
            </div>
            
            <!-- Competitor Tracking -->
            <div class="ac-ai-agent-card-rounded" id="automation-competitor">
                <div class="ac-agent-header">
                    <div class="ac-agent-avatar-rounded">
                        <i class="fas fa-user-secret"></i>
                        <span class="ac-agent-status-ring-rounded"></span>
                    </div>
                    <div class="ac-agent-info">
                        <h3 class="ac-agent-name">Competitor Intel</h3>
                    </div>
                    <label class="ac-agent-toggle-rounded">
                        <input type="checkbox" id="automation-toggle-competitor" onchange="toggleAutomation('competitor')">
                        <span class="ac-toggle-slider-rounded"></span>
                    </label>
                </div>
                
                <ul class="ac-automation-features">
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-user-secret"></i>
                        </div>
                        <span class="ac-automation-feature-text">Competitor tracking</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-lightbulb"></i>
                        </div>
                        <span class="ac-automation-feature-text">Keyword discovery</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-flag"></i>
                        </div>
                        <span class="ac-automation-feature-text">Opportunity alerts</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-crosshairs"></i>
                        </div>
                        <span class="ac-automation-feature-text">Weakness targeting</span>
                    </li>
                </ul>
                
                <div class="ac-agent-live-strip">
                    <span class="ac-live-indicator"></span>
                    <span class="ac-live-text">Tracking 3 competitors...</span>
                </div>
            </div>
            
            <!-- Link Building -->
            <div class="ac-ai-agent-card-rounded" id="automation-linkbuilding">
                <div class="ac-agent-header">
                    <div class="ac-agent-avatar-rounded">
                        <i class="fas fa-link"></i>
                        <span class="ac-agent-status-ring-rounded"></span>
                    </div>
                    <div class="ac-agent-info">
                        <h3 class="ac-agent-name">Link Building</h3>
                    </div>
                    <label class="ac-agent-toggle-rounded">
                        <input type="checkbox" id="automation-toggle-linkbuilding" onchange="toggleAutomation('linkbuilding')">
                        <span class="ac-toggle-slider-rounded"></span>
                    </label>
                </div>
                
                <ul class="ac-automation-features">
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-link"></i>
                        </div>
                        <span class="ac-automation-feature-text">Quality backlinks</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-handshake"></i>
                        </div>
                        <span class="ac-automation-feature-text">Auto outreach</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-network-wired"></i>
                        </div>
                        <span class="ac-automation-feature-text">Strategic links</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <span class="ac-automation-feature-text">Authority tracking</span>
                    </li>
                </ul>
                
                <div class="ac-agent-live-strip">
                    <span class="ac-live-indicator"></span>
                    <span class="ac-live-text">Building authority links...</span>
                </div>
            </div>
            
            <!-- Translations -->
            <div class="ac-ai-agent-card-rounded" id="automation-translations">
                <div class="ac-agent-header">
                    <div class="ac-agent-avatar-rounded">
                        <i class="fas fa-globe"></i>
                        <span class="ac-agent-status-ring-rounded"></span>
                    </div>
                    <div class="ac-agent-info">
                        <h3 class="ac-agent-name">Translations</h3>
                    </div>
                    <label class="ac-agent-toggle-rounded">
                        <input type="checkbox" id="automation-toggle-translations" onchange="toggleAutomation('translations')">
                        <span class="ac-toggle-slider-rounded"></span>
                    </label>
                </div>
                
                <ul class="ac-automation-features">
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-globe"></i>
                        </div>
                        <span class="ac-automation-feature-text">Multi-language SEO</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-language"></i>
                        </div>
                        <span class="ac-automation-feature-text">Native translations</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <span class="ac-automation-feature-text">Local keywords</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-flag-checkered"></i>
                        </div>
                        <span class="ac-automation-feature-text">Global expansion</span>
                    </li>
                </ul>
                
                <div class="ac-agent-live-strip">
                    <span class="ac-live-indicator"></span>
                    <span class="ac-live-text">Translating content...</span>
                </div>
            </div>
        </div>
        </div>
    </div>
</section>

<!-- Comparison Section -->
<section class="ac-comparison-section">
    <div class="ac-features-container">
        <div class="ac-section-header">
            <h2 class="ac-section-title">Why We're Different</h2>
            <p class="ac-section-subtitle">
                Stop choosing between cheap tools that don't work and agencies that overcharge
            </p>
        </div>
        
        <div class="ac-comparison-grid">
            <!-- Cheap AI Tools -->
            <div class="ac-comparison-card ac-comparison-bad">
                <div class="ac-comparison-header">
                    <h3>Cheap AI Tools</h3>
                    <div class="ac-comparison-price">$29-99/mo</div>
                </div>
                <div class="ac-comparison-verdict">
                    <i class="fas fa-times-circle"></i>
                    <span>Waste of Time</span>
                </div>
                <ul class="ac-comparison-list">
                    <li class="ac-comparison-item negative">
                        <i class="fas fa-times"></i>
                        <span>Generic AI that doesn't understand Shopify</span>
                    </li>
                    <li class="ac-comparison-item negative">
                        <i class="fas fa-times"></i>
                        <span>Creates duplicate content Google hates</span>
                    </li>
                    <li class="ac-comparison-item negative">
                        <i class="fas fa-times"></i>
                        <span>No real SEO strategy or intelligence</span>
                    </li>
                    <li class="ac-comparison-item negative">
                        <i class="fas fa-times"></i>
                        <span>Requires constant manual work</span>
                    </li>
                    <li class="ac-comparison-item negative">
                        <i class="fas fa-times"></i>
                        <span>Support via chatbot only</span>
                    </li>
                </ul>
            </div>
            
            <!-- SEO Agencies -->
            <div class="ac-comparison-card">
                <div class="ac-comparison-header">
                    <h3>SEO Agencies</h3>
                    <div class="ac-comparison-price">$3,000-8,000/mo</div>
                </div>
                <div class="ac-comparison-verdict">
                    <i class="fas fa-times-circle"></i>
                    <span>Overpriced & Slow</span>
                </div>
                <ul class="ac-comparison-list">
                    <li class="ac-comparison-item negative">
                        <i class="fas fa-times"></i>
                        <span>Lock you into 6-12 month contracts</span>
                    </li>
                    <li class="ac-comparison-item negative">
                        <i class="fas fa-times"></i>
                        <span>Deliver reports, not results</span>
                    </li>
                    <li class="ac-comparison-item negative">
                        <i class="fas fa-times"></i>
                        <span>Take months to implement changes</span>
                    </li>
                    <li class="ac-comparison-item negative">
                        <i class="fas fa-times"></i>
                        <span>Junior staff working on your account</span>
                    </li>
                    <li class="ac-comparison-item negative">
                        <i class="fas fa-times"></i>
                        <span>Generic strategies for all clients</span>
                    </li>
                </ul>
            </div>
            
            <!-- Grove - The Winner -->
            <div class="ac-comparison-card ac-comparison-best">
                <div class="ac-comparison-header">
                    <h3>Grove Ecosystem</h3>
                    <div class="ac-comparison-price"><del style="color: #9ca3af; font-size: 0.875em;">$499</del> $99/mo</div>
                </div>
                <div class="ac-comparison-verdict">
                    <i class="fas fa-brain"></i>
                    <span>The Smart Choice</span>
                </div>
                <ul class="ac-comparison-list">
                    <li class="ac-comparison-item positive">
                        <i class="fas fa-check"></i>
                        <span>Built specifically for Shopify stores</span>
                    </li>
                    <li class="ac-comparison-item positive">
                        <i class="fas fa-check"></i>
                        <span>AI works 24/7, no human delays</span>
                    </li>
                    <li class="ac-comparison-item positive">
                        <i class="fas fa-check"></i>
                        <span>Optimizes entire catalog in hours</span>
                    </li>
                    <li class="ac-comparison-item positive">
                        <i class="fas fa-check"></i>
                        <span>Real-time performance tracking</span>
                    </li>
                    <li class="ac-comparison-item positive">
                        <i class="fas fa-check"></i>
                        <span>Cancel anytime, no contracts</span>
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- CTA for comparison section -->
        <div class="ac-comparison-cta">
            <a href="pre-registration.html" class="ac-btn ac-btn-primary">
                Join the Waitlist
                <i class="fas fa-arrow-right"></i>
            </a>
            <p>3-day free trial • Exclusive benefits</p>
        </div>
    </div>
</section>


<!-- AI Brain Orchestration Section -->
<section class="ac-ai-brain-section">
    <div class="ac-features-container">
        <div class="ac-section-header">
            <h2 class="ac-section-title">Agentic SEO for Shopify</h2>
            <p class="ac-section-subtitle">
                8 specialized agents. One AI brain. Your store growing 24/7.
            </p>
        </div>
        
        <div class="ac-brain-orchestration">
            <!-- Left side - Brain -->
            <div class="ac-brain-left">
                <div class="ac-brain-icon-container">
                    <div class="ac-brain-blob"></div>
                    <div class="ac-brain-inner">
                        <div class="ac-brain-icon">
                            <i class="fas fa-brain"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Center - Connection Line -->
            <div class="ac-brain-connection">
                <div class="ac-dotted-line">
                    <div class="ac-task-icon" data-agent="product">
                        <i class="fas fa-brain"></i>
                    </div>
                </div>
            </div>
            
            <!-- Right side - Dashboard -->
            <div class="ac-brain-dashboard">
                <div class="ac-dashboard-header">
                    <div class="ac-dashboard-dots">
                        <div class="ac-dashboard-dot red"></div>
                        <div class="ac-dashboard-dot yellow"></div>
                        <div class="ac-dashboard-dot green"></div>
                    </div>
                    <div class="ac-dashboard-title" style="font-family: var(--ac-font-heading);">AI Automation Center</div>
                </div>
                
                <div class="ac-dashboard-content">
                    <div class="ac-task-scheduled-indicator">
                        <i class="fas fa-check-circle"></i> TASK COMPLETE
                    </div>
                    
                    <div class="ac-notifications-header">
                        <div class="ac-live-dot"></div>
                        24/7 Growth
                    </div>
                    
                    <div class="ac-notifications-list">
                        <!-- Notifications will be dynamically added here when tasks complete -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- AI Sparkles -->
        <div class="ac-brain-sparkle ac-brain-sparkle-1">
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 0 L14 10 L24 12 L14 14 L12 24 L10 14 L0 12 L10 10 L12 0 Z" fill="#ef2b70"/>
            </svg>
        </div>
        <div class="ac-brain-sparkle ac-brain-sparkle-2 small">
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 0 L14 10 L24 12 L14 14 L12 24 L10 14 L0 12 L10 10 L12 0 Z" fill="#ef2b70"/>
            </svg>
        </div>
        <div class="ac-brain-sparkle ac-brain-sparkle-3">
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 0 L14 10 L24 12 L14 14 L12 24 L10 14 L0 12 L10 10 L12 0 Z" fill="#ef2b70"/>
            </svg>
        </div>
    </div>
</section>

<!-- Pricing Section -->
<section class="ac-pricing-section" id="pricing">
    <div class="ac-features-container">
        <div class="ac-section-header">
            <h2 class="ac-section-title">Simple, Transparent Pricing</h2>
            <p class="ac-section-subtitle">
                Limited founding member spots. Lock in 80% off forever. Be part of something special.
            </p>
        </div>
        
        <!-- Desktop Two-Column Layout -->
        <div class="ac-pricing-layout" style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; max-width: 1200px; margin: 0 auto; align-items: stretch;">
            <!-- Founder's Note Box -->
            <div class="ac-waitlist-box" style="background: white; border: 2px solid #e5e7eb; border-radius: 16px; padding: 2.5rem; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); display: flex; flex-direction: column;">
                <div style="display: inline-flex; align-items: center; justify-content: center; width: 50px; height: 50px; background: linear-gradient(135deg, rgba(239, 43, 112, 0.1) 0%, rgba(239, 43, 112, 0.15) 100%); border-radius: 10px; margin-bottom: 1.5rem;">
                    <i class="fas fa-envelope-open-text" style="font-size: 1.25rem; color: var(--ac-primary);"></i>
                </div>
                <h3 style="font-size: 1.25rem; margin-bottom: 1rem; color: var(--ac-text-primary); text-align: left;">Why is there a waitlist?</h3>
                <div style="font-size: 0.95rem; line-height: 1.7; color: var(--ac-text-secondary); text-align: left;">
                    <p style="margin-bottom: 1rem;">
                        I'm building Grove because I'm tired of watching Shopify stores get ripped off by agencies charging $5k/month for PDFs and promises.
                    </p>
                    <p style="margin-bottom: 1rem;">
                        Instead of launching to everyone, I'm hand-selecting <strong style="color: var(--ac-primary);">serious store owners</strong> who want to be part of something special. Together we'll shape the future of SEO and drive growth that'll have the big players worried.
                    </p>
                    <p style="margin-bottom: 1rem;">
                        As a founding member, you'll have my personal phone number. We'll do regular calls. Your feedback, your struggles and your ideas will inform what we build.
                    </p>
                    <p style="margin-bottom: 1.5rem;">
                        If you're serious about SEO and want to be part of a tight-knit community that's building the future together, consider joining our waitlist.
                    </p>
                </div>
                
                <!-- Waitlist Benefits Icons -->
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 1rem; margin: 1.5rem 0; padding: 1.5rem 0; border-top: 1px solid #e5e7eb; border-bottom: 1px solid #e5e7eb;">
                    <div style="text-align: center;">
                        <div style="display: inline-flex; align-items: center; justify-content: center; width: 40px; height: 40px; background: rgba(34, 197, 94, 0.1); border-radius: 8px; margin-bottom: 0.5rem;">
                            <i class="fas fa-tag" style="font-size: 1.125rem; color: var(--ac-grove);"></i>
                        </div>
                        <p style="font-size: 0.75rem; font-weight: 600; color: var(--ac-text-primary); margin: 0;">80% Off</p>
                    </div>
                    <div style="text-align: center;">
                        <div style="display: inline-flex; align-items: center; justify-content: center; width: 40px; height: 40px; background: rgba(34, 197, 94, 0.1); border-radius: 8px; margin-bottom: 0.5rem;">
                            <i class="fas fa-phone" style="font-size: 1.125rem; color: var(--ac-grove);"></i>
                        </div>
                        <p style="font-size: 0.75rem; font-weight: 600; color: var(--ac-text-primary); margin: 0;">Direct Access</p>
                    </div>
                    <div style="text-align: center;">
                        <div style="display: inline-flex; align-items: center; justify-content: center; width: 40px; height: 40px; background: rgba(34, 197, 94, 0.1); border-radius: 8px; margin-bottom: 0.5rem;">
                            <i class="fas fa-users" style="font-size: 1.125rem; color: var(--ac-grove);"></i>
                        </div>
                        <p style="font-size: 0.75rem; font-weight: 600; color: var(--ac-text-primary); margin: 0;">Exclusive Community</p>
                    </div>
                </div>
                
                <!-- Founder Signature -->
                <div style="margin-top: auto;">
                    <div style="display: flex; align-items: center; gap: 1rem; padding-top: 1.5rem;">
                        <img src="static/images/hamish-avatar.jpg" alt="Hamish" style="width: 48px; height: 48px; border-radius: 50%; object-fit: cover;">
                        <div style="text-align: left;">
                            <p style="font-weight: 600; margin: 0; color: var(--ac-text-primary);">Hamish</p>
                            <p style="font-size: 0.813rem; color: var(--ac-text-secondary); margin: 0;">Founder & CTO</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Pricing Card -->
            <div class="ac-pricing-grid" style="grid-template-columns: 1fr; margin: 0;">
            <!-- Single Growth Engine Plan -->
            <div class="ac-pricing-card ac-pricing-featured">
                <div class="ac-pricing-badge"><span class="desktop-only">Early Access: </span>80% Off!</div>
                <h3 class="ac-pricing-name">Growth Engine</h3>
                <div class="ac-pricing-price">
                    <span style="font-family: 'Lexend', sans-serif; font-size: 2.5rem; font-weight: 700; color: #ef2b70; text-decoration: line-through; margin-right: 1rem;">$499</span>
                    <span class="ac-price-amount">$99</span>
                    <span class="ac-price-period">/month</span>
                </div>
                <p class="ac-pricing-description">
                    Complete SEO ecosystem for serious store owners
                </p>
                
                <ul class="ac-pricing-features">
                    <li><i class="fas fa-check"></i> <strong>Unlimited</strong> Product SEO Engine</li>
                    <li><i class="fas fa-check"></i> <strong>Auto-scaling</strong> Smart Collections</li>
                    <li><i class="fas fa-check"></i> <strong>Real-time</strong> Search Insights</li>
                    <li><i class="fas fa-check"></i> <strong>Daily</strong> Content Machine</li>
                    <li><i class="fas fa-check"></i> <strong>24/7</strong> Technical SEO</li>
                    <li><i class="fas fa-check"></i> <strong>Deep</strong> Competitor Intel</li>
                    <li><i class="fas fa-check"></i> <strong>Automated</strong> Link Building</li>
                    <li><i class="fas fa-check"></i> <strong>Full catalog</strong> Translations</li>
                </ul>
                
                <a href="pre-registration.html" class="ac-btn ac-btn-primary ac-btn-block">
                    Join the Waitlist
                </a>
                <p class="ac-pricing-note">3-day free trial • Exclusive benefits</p>
            </div>
            </div>
        </div>
        
        <!-- Interactive ROI Calculator -->
        <div class="ac-roi-calculator" id="roi-calculator" style="margin-top: 5rem;">
            <h3>Calculate Your Grove ROI</h3>
            <p class="ac-roi-subtitle">See how much you'll save AND earn with AI-powered SEO</p>
            
            <!-- Input Controls -->
            <div class="ac-roi-inputs">
                <div class="ac-roi-input-group">
                    <label for="traffic-slider">Your current monthly traffic</label>
                    <div class="ac-slider-container">
                        <input type="range" id="traffic-slider" class="ac-traffic-slider" 
                               min="0" max="500000" value="0" step="1000">
                        <div class="ac-slider-value"><span id="traffic-display">0</span> visitors/mo</div>
                    </div>
                </div>
            </div>
            
            <!-- Results Display -->
            <div class="ac-roi-results">
                <div class="ac-roi-breakdown">
                    <!-- Revenue from Traffic -->
                    <div class="ac-roi-item ac-roi-revenue">
                        <div class="ac-roi-item-header">
                            <i class="fas fa-chart-line"></i>
                            <span>Revenue from AI-Driven Traffic Growth</span>
                        </div>
                        <div class="ac-roi-item-value">+$<span id="revenue-growth">3,250</span>/mo</div>
                        <div class="ac-roi-item-detail"><span id="new-visitors">2,500</span> new visitors × 2% conversion × $65 AOV</div>
                    </div>
                    
                    <!-- No Agency Costs -->
                    <div class="ac-roi-item ac-roi-cost">
                        <div class="ac-roi-item-header">
                            <i class="fas fa-ban"></i>
                            <span>vs. Full Service SEO Agency</span>
                        </div>
                        <div class="ac-roi-item-value">+$<span id="cost-savings">4,501</span>/mo</div>
                        <div class="ac-roi-item-detail">vs. typical $5,000/mo agency</div>
                    </div>
                    
                    <!-- Time Savings -->
                    <div class="ac-roi-item ac-roi-time">
                        <div class="ac-roi-item-header">
                            <i class="fas fa-clock"></i>
                            <span>Time Savings</span>
                        </div>
                        <div class="ac-roi-item-value">+$<span id="time-savings">2,000</span>/mo</div>
                        <div class="ac-roi-item-detail"><span id="hours-saved">20</span> hours freed up × $100/hr</div>
                    </div>
                </div>
                
                <!-- Total Impact -->
                <div class="ac-roi-total">
                    <div class="ac-roi-total-label">Total Monthly Impact</div>
                    <div class="ac-roi-total-value">$<span id="total-impact">9,751</span></div>
                    <div class="ac-roi-annual">That's $<span id="annual-impact">117,012</span> per year!</div>
                </div>
                
                <!-- CTA for high ROI -->
                <div class="ac-roi-cta" id="roi-cta" style="display: none;">
                    <p>🚀 With ROI this high, every day you wait costs you money!</p>
                    <a href="pre-registration.html" class="ac-btn ac-btn-primary ac-btn-block">
                        Be First to Grow Revenue
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section 1 -->
<section class="ac-cta ac-cta-gradient">
    <div class="ac-cta-container">
        <h2 class="ac-cta-title">Ready to Fire Your Agency?</h2>
        <p class="ac-cta-subtitle">
            Join smart Shopify owners saving $5,000/mo while getting 3x better results
        </p>
        <div class="ac-cta-buttons">
            <a href="pre-registration.html" class="ac-btn ac-btn-white">
                Join Waitlist
                <i class="fas fa-arrow-right"></i>
            </a>
            <a href="features.html" class="ac-btn ac-btn-white-outline">
                <i class="fas fa-list"></i>
                See All Features
            </a>
        </div>
        <div class="ac-cta-stats">
            <span><i class="fas fa-check"></i> 3-day free trial</span>
            <span><i class="fas fa-check"></i> 14-day money back guarantee</span>
            <span><i class="fas fa-check"></i> Cancel anytime</span>
        </div>
    </div>
</section>

<!-- Features Section (Why Agencies) -->
<section class="ac-features" id="features">
    <div class="ac-features-container">
        <div class="ac-section-header">
            <h2 class="ac-section-title">Why Shopify Stores Are Firing Their SEO Agencies</h2>
            <p class="ac-section-subtitle">
                Get better results for 10X less. No more waiting weeks for "deliverables."
            </p>
        </div>
        
        <div class="ac-features-grid">
            <div class="ac-feature-card">
                <div class="ac-feature-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <h3 class="ac-feature-title">Instant Results vs 6-Month Contracts</h3>
                <p class="ac-feature-description">
                    Agencies lock you into long contracts. We optimize your entire catalog in hours, 
                    not months. See traffic improvements in days, not quarters.
                </p>
            </div>
            
            <div class="ac-feature-card">
                <div class="ac-feature-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h3 class="ac-feature-title">Real Rankings, Not Reports</h3>
                <p class="ac-feature-description">
                    No more paying $5k/mo for PDF reports. Our AI actually optimizes your products 
                    and tracks real ranking improvements daily.
                </p>
            </div>
            
            <div class="ac-feature-card">
                <div class="ac-feature-icon">
                    <i class="fas fa-magic"></i>
                </div>
                <h3 class="ac-feature-title">AI That Knows Shopify</h3>
                <p class="ac-feature-description">
                    Built specifically for Shopify's structure. Agencies use generic tactics. 
                    We optimize metafields, handles, and collections the Shopify way.
                </p>
            </div>
            
            <div class="ac-feature-card">
                <div class="ac-feature-icon">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <h3 class="ac-feature-title">$499/mo vs $5,000/mo</h3>
                <p class="ac-feature-description">
                    Agencies charge $60k+ yearly. We deliver better results for less than 
                    their monthly retainer. No hidden fees, no surprises.
                </p>
            </div>
            
            <div class="ac-feature-card">
                <div class="ac-feature-icon">
                    <i class="fas fa-robot"></i>
                </div>
                <h3 class="ac-feature-title">Works While You Sleep</h3>
                <p class="ac-feature-description">
                    Agencies work 9-5 (maybe). Our AI optimizes your store 24/7, catching 
                    trends and opportunities while your competitors sleep.
                </p>
            </div>
            
            <div class="ac-feature-card">
                <div class="ac-feature-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h3 class="ac-feature-title">No More Agency BS</h3>
                <p class="ac-feature-description">
                    Cancel anytime. No contracts, no "strategy sessions," no account managers. 
                    Just AI that works, delivering real results you can measure.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Results Dashboard Section -->
<section class="ac-results-section" style="position: relative; overflow: visible;">
    <div class="ac-features-container" style="position: relative;">
        <div class="ac-section-header">
            <h2 class="ac-section-title">Watch Your Traffic Explode on Autopilot</h2>
            <p class="ac-section-subtitle">
                Real results from stores using our AI automation. Set it once, watch it grow forever.
            </p>
        </div>
        
        <!-- Desktop-only sparkle on left side -->
        <div class="ac-ai-sparkle ac-sparkle-7" style="position: absolute; left: 5%; top: 8%; display: none;">
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 0 L14 10 L24 12 L14 14 L12 24 L10 14 L0 12 L10 10 L12 0 Z" fill="#ef2b70"/>
            </svg>
        </div>
        
        <div class="ac-results-dashboard" style="position: relative;">
            <!-- Metric Cards -->
            <div class="ac-metric-cards">
                <div class="ac-metric-card">
                    <div class="ac-metric-icon" style="background: rgba(66, 133, 244, 0.1); color: #4285f4;">
                        <i class="fas fa-eye"></i>
                    </div>
                    <div class="ac-metric-content">
                        <div class="ac-metric-value" data-value="892" data-suffix="K">0</div>
                        <div class="ac-metric-label">Impressions</div>
                        <div class="ac-metric-change" style="color: #4285f4;">+312% vs last month</div>
                    </div>
                </div>
                <div class="ac-metric-card">
                    <div class="ac-metric-icon" style="background: rgba(52, 168, 83, 0.1); color: #34a853;">
                        <i class="fas fa-seedling"></i>
                    </div>
                    <div class="ac-metric-content">
                        <div class="ac-metric-value" data-value="147" data-suffix="">0</div>
                        <div class="ac-metric-label">Keywords Ranking</div>
                        <div class="ac-metric-change" style="color: #34a853;">+52 new keywords</div>
                    </div>
                </div>
                <div class="ac-metric-card">
                    <div class="ac-metric-icon" style="background: rgba(251, 188, 4, 0.1); color: #fbbc04;">
                        <i class="fas fa-mouse-pointer"></i>
                    </div>
                    <div class="ac-metric-content">
                        <div class="ac-metric-value" data-value="24" data-suffix="K">0</div>
                        <div class="ac-metric-label">Clicks</div>
                        <div class="ac-metric-change" style="color: #fbbc04;">+189% CTR improvement</div>
                    </div>
                </div>
                <div class="ac-metric-card">
                    <div class="ac-metric-icon" style="background: rgba(234, 67, 53, 0.1); color: #ea4335;">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="ac-metric-content">
                        <div class="ac-metric-value" data-prefix="$" data-value="127847">$0</div>
                        <div class="ac-metric-label">Revenue</div>
                        <div class="ac-metric-change" style="color: #ea4335;">+$42k this month</div>
                    </div>
                </div>
            </div>
            
            
            <!-- Autopilot Badge -->
            <div class="ac-autopilot-badge">
                <i class="fas fa-robot"></i>
                <span>AI Optimization Active 24/7</span>
                <div class="ac-pulse-indicator"></div>
            </div>
        </div>
    </div>
</section>

<!-- Testimonial Section from About Page -->
<section class="ac-testimonial-section">
    <div class="ac-testimonial-container">
        <div class="ac-section-header" style="text-align: center; margin-bottom: 3rem;">
            <h2 class="ac-section-title">Real Results from Real Store Owners</h2>
            <p class="ac-section-subtitle">
                See how leading brands are dominating their markets with Grove
            </p>
        </div>
        
        <div class="ac-testimonial-content">
            <div class="ac-testimonial-text">
                <div class="ac-testimonial-header">
                    <img src="static/images/client-logos/dapperfam.png" alt="DapperFam" class="ac-company-logo-standalone">
                </div>
                
                <div class="ac-title-with-flag">
                    <h2>Page #1 for "Hats" <img src="../upload.wikimedia.org/wikipedia/en/thumb/a/a4/Flag_of_the_United_States.svg/255px-Flag_of_the_United_States.svg.png" alt="US Flag" style="height: 0.43em; vertical-align: middle; margin-left: 0.25em;"></h2>
                </div>
                
                <div class="ac-testimonial-intro">
                    DapperFam is a leading men's luxury hats manufacturer and retailer who has continued to dominate the industry. Here's what Brandon had to say about using Grove
                    <div class="ac-arrow-to-video">
                        <img src="static/images/arrow-png.png" alt="Arrow" class="ac-arrow-img">
                    </div>
                </div>
                
                <div class="ac-testimonial-author">
                    <div class="ac-testimonial-avatar">
                        <img src="static/images/brandon-johnson-avatar.jpg" alt="Brandon Johnson" class="ac-avatar-img">
                    </div>
                    <div class="ac-testimonial-author-info">
                        <h4>Brandon Johnson</h4>
                        <p>Founder, DapperFam</p>
                    </div>
                </div>
                
                <div class="ac-testimonial-metrics">
                    <div class="ac-testimonial-metric">
                        <div class="ac-testimonial-metric-value">547%</div>
                        <div class="ac-testimonial-metric-label">Traffic Increase</div>
                    </div>
                    <div class="ac-testimonial-metric">
                        <div class="ac-testimonial-metric-value">75K</div>
                        <div class="ac-testimonial-metric-label">Daily Impressions</div>
                    </div>
                    <div class="ac-testimonial-metric">
                        <div class="ac-testimonial-metric-value">90</div>
                        <div class="ac-testimonial-metric-label">Days to Success</div>
                    </div>
                </div>
            </div>
            
            <div class="ac-testimonial-video">
                <div class="ac-video-container">
                    <video
                        id="testimonialVideo"
                        autoplay
                        muted
                        loop
                        playsinline
                        preload="auto"
                        onclick="playTestimonialVideo()"
                    >
                        <source src="static/videos/testimonial.mp4" type="video/mp4">
                        Your browser does not support the video tag.
                    </video>
                    
                    <div class="ac-video-overlay" onclick="playTestimonialVideo()">
                        <div class="ac-play-button">
                            <i class="fas fa-volume-up"></i>
                            <span>Tap for sound</span>
                        </div>
                    </div>
                    
                    <div class="ac-video-badge">
                        <i class="fas fa-check-circle"></i>
                        Verified Customer
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>


    </main>
    
    <!-- Footer -->
    <footer class="footer">
        <div class="footer-container">
            <div class="footer-brand">
                <img src="static/images/grove-logo.png" alt="Grove">
                <p>The world's first agentic SEO app for Shopify. Replace your $5,000/mo agency with AI that actually works.</p>
            </div>
            
            <div class="footer-columns">
                <div class="footer-column">
                    <h4>Product</h4>
                    <ul>
                        <li><a href="features.html">All Features</a></li>
                        <li><a href="pricing.html">Pricing</a></li>
                        <li><a href="pre-registration.html">Join Waitlist</a></li>
                        <li><a href="about.html">How It Works</a></li>
                        <li><a href="auth/login.html" class="footer-login">Customer Login</a></li>
                    </ul>
                </div>
                
                <div class="footer-column">
                    <h4>Features</h4>
                    <ul>
                        <li><a href="features.html#product-seo-engine">Product SEO Engine</a></li>
                        <li><a href="features.html#smart-collections">Smart Collections</a></li>
                        <li><a href="features.html#search-insights">Search Insights</a></li>
                        <li><a href="features.html#content-machine">Content Machine</a></li>
                        <li><a href="features.html#technical-seo">Technical SEO</a></li>
                        <li><a href="features.html#competitor-intel">Competitor Intel</a></li>
                        <li><a href="features.html#link-building">Link Building</a></li>
                        <li><a href="features.html#translations">Translations</a></li>
                    </ul>
                </div>
                
                <div class="footer-column">
                    <h4>Resources</h4>
                    <ul>
                        <li><a href="blog.html">Blog</a></li>
                        <li><a href="shopify-seo-guide.html">Complete SEO Guide</a></li>
                        <li><a href="shopify-seo-checklist.html">SEO Checklist</a></li>
                    </ul>
                </div>
                
                <div class="footer-column">
                    <h4>Company</h4>
                    <ul>
                        <li><a href="about.html">About Grove</a></li>
                        <li><a href="contact.html">Contact Us</a></li>
                        <li><a href="privacy.html">Privacy Policy</a></li>
                        <li><a href="terms.html">Terms of Service</a></li>
                    </ul>
                    <div class="footer-social">
                        <a href="https://twitter.com/groveai" class="social-link">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="https://linkedin.com/company/groveai" class="social-link">
                            <i class="fab fa-linkedin"></i>
                        </a>
                        <a href="https://youtube.com/@groveai" class="social-link">
                            <i class="fab fa-youtube"></i>
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p class="footer-copyright">© 2025 Grove AI Inc. All rights reserved.</p>
                <p class="footer-tagline">
                    <i class="fas fa-robot"></i> AI That Actually Does SEO, Not Just Reports It
                </p>
            </div>
        </div>
    </footer>
    
    <!-- Scripts -->
    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add scroll effect to navigation
        window.addEventListener('scroll', function() {
            const nav = document.querySelector('.ac-nav');
            if (window.scrollY > 50) {
                nav.classList.add('scrolled');
            } else {
                nav.classList.remove('scrolled');
            }
        });
        
        // Mega Menu Interactions with Smart Positioning
        const dropdowns = document.querySelectorAll('.ac-nav-dropdown');
        let closeTimeout;
        
        dropdowns.forEach(dropdown => {
            const megaMenu = dropdown.querySelector('.ac-mega-menu');
            const simpleMenu = dropdown.querySelector('.ac-dropdown-menu');
            let openTimeout;
            
            dropdown.addEventListener('mouseenter', function() {
                clearTimeout(closeTimeout);
                clearTimeout(openTimeout);
                
                // Small delay before opening to prevent accidental hovers
                openTimeout = setTimeout(() => {
                    // Close other dropdowns
                    dropdowns.forEach(d => {
                        if (d !== dropdown) {
                            d.classList.remove('active');
                        }
                    });
                    dropdown.classList.add('active');
                    
                    // Smart positioning for mega menus
                    if (megaMenu) {
                        const rect = dropdown.getBoundingClientRect();
                        const menuWidth = 900; // Match CSS width
                        const viewportWidth = window.innerWidth;
                        
                        // Reset classes
                        megaMenu.classList.remove('align-right', 'align-center');
                        
                        // Check if menu would go off right edge
                        if (rect.left + menuWidth > viewportWidth - 20) {
                            // Check if there's enough space on the left
                            if (rect.right - menuWidth > 20) {
                                megaMenu.classList.add('align-right');
                            } else {
                                // Center it if no good option
                                megaMenu.classList.add('align-center');
                            }
                        }
                        // Check if menu would go off left edge
                        else if (rect.left < 20) {
                            // Keep default left alignment
                        }
                    }
                }, 50); // 50ms delay
            });
            
            dropdown.addEventListener('mouseleave', function() {
                clearTimeout(openTimeout); // Cancel opening if user leaves quickly
                closeTimeout = setTimeout(() => {
                    dropdown.classList.remove('active');
                }, 200);
            });
        });
        
        // Mobile Menu
        const mobileToggle = document.querySelector('.ac-mobile-toggle');
        const mobileMenu = document.querySelector('.ac-mobile-menu');
        const mobileClose = document.querySelector('.ac-mobile-close');
        
        if (mobileToggle) {
            mobileToggle.addEventListener('click', function() {
                mobileMenu.style.display = 'block';
                // Small delay to ensure display change takes effect before animation
                setTimeout(() => {
                    mobileMenu.classList.add('active');
                }, 10);
                document.body.style.overflow = 'hidden';
            });
        }
        
        if (mobileClose) {
            mobileClose.addEventListener('click', function() {
                mobileMenu.classList.remove('active');
                document.body.style.overflow = '';
                // Hide after transition completes
                setTimeout(() => {
                    mobileMenu.style.display = 'none';
                }, 300);
            });
        }
        
        // Close mobile menu on outside click
        mobileMenu?.addEventListener('click', function(e) {
            if (e.target === mobileMenu) {
                mobileMenu.classList.remove('active');
                document.body.style.overflow = '';
                // Hide after transition completes
                setTimeout(() => {
                    mobileMenu.style.display = 'none';
                }, 300);
            }
        });
        
        // Prevent dropdown closing on click inside
        const megaMenus = document.querySelectorAll('.ac-mega-menu');
        megaMenus.forEach(menu => {
            menu.addEventListener('click', function(e) {
                e.stopPropagation();
            });
        });
    </script>
    
    
<script>
// Smooth scrolling for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Add scroll effect to navigation
window.addEventListener('scroll', function() {
    const nav = document.querySelector('.ac-nav');
    if (window.scrollY > 50) {
        nav.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
    } else {
        nav.style.boxShadow = 'none';
    }
});

// Animate metrics on scroll
const observerOptions = {
    threshold: 0.5,
    rootMargin: '0px'
};

const observer = new IntersectionObserver(function(entries) {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            const metrics = entry.target.querySelectorAll('.ac-metric-value');
            metrics.forEach(metric => {
                const finalValue = metric.textContent;
                // Skip animation for these specific values
                if (finalValue === '10X' || finalValue === '24/7' || finalValue === '2.8X') {
                    return;
                }
                const numValue = parseInt(finalValue.replace(/,/g, ''));
                let current = 0;
                const increment = numValue / 50;
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= numValue) {
                        current = numValue;
                        clearInterval(timer);
                    }
                    metric.textContent = Math.floor(current).toLocaleString();
                }, 30);
            });
            observer.unobserve(entry.target);
        }
    });
}, observerOptions);

// Observe metrics section
const metricsSection = document.querySelector('.ac-metrics');
if (metricsSection) {
    observer.observe(metricsSection);
}

// Toggle automation function
function toggleAutomation(type) {
    const card = document.getElementById('automation-' + type);
    const checkbox = card.querySelector('input[type="checkbox"]');
    
    // Mobile-specific behavior
    if (window.innerWidth <= 767) {
        const liveStrip = card.querySelector('.ac-agent-live-strip');
        const liveIndicator = card.querySelector('.ac-live-indicator');
        const statusRing = card.querySelector('.ac-agent-status-ring-rounded');
        
        if (checkbox.checked) {
            card.classList.add('active');
            
            // Animate the status ring if it exists
            if (statusRing) {
                statusRing.style.animation = 'pulse-ring 2s infinite';
            }
            
            // Animate the live indicator
            if (liveIndicator) {
                liveIndicator.style.animation = 'pulse-dot 2s infinite';
            }
            
            // Show the live strip with animation
            if (liveStrip) {
                liveStrip.style.opacity = '1';
                liveStrip.style.transform = 'translateY(0)';
            }
            
            // Animate feature icons
            const featureIcons = card.querySelectorAll('.ac-automation-feature-icon');
            featureIcons.forEach((icon, iconIndex) => {
                setTimeout(() => {
                    icon.style.transform = 'scale(1.2)';
                    setTimeout(() => {
                        icon.style.transform = 'scale(1)';
                    }, 200);
                }, iconIndex * 100);
            });
        } else {
            card.classList.remove('active');
            
            // Reset animations
            if (statusRing) {
                statusRing.style.animation = '';
            }
            if (liveIndicator) {
                liveIndicator.style.animation = '';
            }
            if (liveStrip) {
                liveStrip.style.opacity = '0';
                liveStrip.style.transform = 'translateY(10px)';
            }
        }
    } else {
        // Desktop behavior (original)
        if (checkbox.checked) {
            card.classList.add('active');
            
            // Add pulse animation to feature icons (copied from rounded section)
            const featureIcons = card.querySelectorAll('.ac-automation-feature-icon');
            featureIcons.forEach((icon, iconIndex) => {
                setTimeout(() => {
                    icon.style.transform = 'scale(1.2)';
                    setTimeout(() => {
                        icon.style.transform = 'scale(1)';
                    }, 200);
                }, iconIndex * 100);
            });
        } else {
            card.classList.remove('active');
        }
    }
}

// Show activation success animation (removed - no longer showing notifications)

// Add smooth transition styles to feature icons
document.querySelectorAll('.ac-automation-feature-icon').forEach(icon => {
    icon.style.transition = 'all 0.3s ease';
});

// Automation scroll observers - run after DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    const automationSection = document.querySelector('.ac-automation-section');
    
    if (automationSection) {
        // Mobile observer
        if (window.innerWidth <= 767) {
            let mobileAutomationActivated = false;
            
            // Function to activate automations
            const activateAutomations = function() {
                if (!mobileAutomationActivated) {
                    mobileAutomationActivated = true;
                    console.log('Mobile: Starting automation sequence...');
                    
                    // Activate automations in sequence
                    const automationTypes = ['products', 'collections', 'search', 'content', 'technical', 'competitor', 'linkbuilding', 'translations'];
                    automationTypes.forEach((type, index) => {
                        setTimeout(() => {
                            const card = document.getElementById(`automation-${type}`);
                            const checkbox = card ? card.querySelector('input[type="checkbox"]') : null;
                            console.log(`Mobile: Activating ${type}:`, card, checkbox);
                            if (checkbox && !checkbox.checked) {
                                checkbox.checked = true;
                                toggleAutomation(type);
                            }
                        }, index * 400);
                    });
                }
            };
            
            const mobileAutomationObserver = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        console.log('Mobile: Automation section is intersecting');
                        activateAutomations();
                    }
                });
            }, { threshold: 0.3, rootMargin: '-50px' });
            
            mobileAutomationObserver.observe(automationSection);
            console.log('Mobile: Observing automation section');
            
            // Check if already in view on load
            const rect = automationSection.getBoundingClientRect();
            if (rect.top < window.innerHeight && rect.bottom > 0) {
                console.log('Mobile: Automation section already in view on load');
                // Delay to ensure everything is ready
                setTimeout(activateAutomations, 500);
            }
        } 
        // Desktop observer
        else {
            let desktopAutomationActivated = false;
            
            const desktopAutomationObserver = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting && !desktopAutomationActivated) {
                        desktopAutomationActivated = true;
                        console.log('Desktop: Automation section in view, starting sequence...');
                        
                        // Activate automations in sequence
                        const automationTypes = ['products', 'collections', 'search', 'content', 'technical', 'competitor', 'linkbuilding', 'translations'];
                        automationTypes.forEach((type, index) => {
                            setTimeout(() => {
                                const card = document.getElementById(`automation-${type}`);
                                const checkbox = card ? card.querySelector('input[type="checkbox"]') : null;
                                if (checkbox && !checkbox.checked) {
                                    checkbox.checked = true;
                                    toggleAutomation(type);
                                }
                            }, index * 400);
                        });
                    }
                });
            }, { threshold: 0.3, rootMargin: '-100px' });
            
            desktopAutomationObserver.observe(automationSection);
            console.log('Desktop: Observing automation section');
        }
    }
});

// Results dashboard animations
const resultsObserverOptions = {
    threshold: 0.3,
    rootMargin: '0px'
};

const resultsObserver = new IntersectionObserver(function(entries) {
    entries.forEach(entry => {
        if (entry.isIntersecting && !entry.target.classList.contains('animated')) {
            entry.target.classList.add('animated');
            
            // Animate metric values counting up
            const metricValues = entry.target.querySelectorAll('.ac-metric-value');
            metricValues.forEach(metric => {
                const finalValue = parseFloat(metric.dataset.value);
                const prefix = metric.dataset.prefix || '';
                const suffix = metric.dataset.suffix || '';
                const precision = parseInt(metric.dataset.precision) || 0;
                let currentValue = 0;
                const increment = finalValue / 50;
                const timer = setInterval(() => {
                    currentValue += increment;
                    if (currentValue >= finalValue) {
                        currentValue = finalValue;
                        clearInterval(timer);
                    }
                    if (precision > 0) {
                        metric.textContent = prefix + currentValue.toFixed(precision) + suffix;
                    } else {
                        metric.textContent = prefix + Math.floor(currentValue).toLocaleString() + suffix;
                    }
                }, 30);
            });
            
            // Animate chart lines drawing
            const chartLines = entry.target.querySelectorAll('[class^="ac-line-"]');
            chartLines.forEach((line, index) => {
                setTimeout(() => {
                    line.style.animation = 'drawLine 2s ease-out forwards';
                }, index * 300);
            });
            
            // Dots are now static, no animation needed
        }
    });
}, resultsObserverOptions);

// Observe results section
const resultsSection = document.querySelector('.ac-results-section');
if (resultsSection) {
    resultsObserver.observe(resultsSection);
}

// Interactive ROI Calculator
const trafficSlider = document.getElementById('traffic-slider');
const trafficDisplay = document.getElementById('traffic-display');
const roiCta = document.getElementById('roi-cta');

// Calculate and update ROI
function calculateROI() {
    const currentTraffic = parseInt(trafficSlider.value);
    
    // Format traffic display
    trafficDisplay.textContent = currentTraffic.toLocaleString();
    
    // Calculate new traffic and revenue
    let newTraffic;
    if (currentTraffic === 0) {
        // If starting from 0, show realistic first few months traffic
        newTraffic = 2500; // New stores can realistically reach 2.5k/mo in first months
    } else if (currentTraffic < 1000) {
        // For very low traffic, show achievable growth
        newTraffic = 3000; // Can grow to 3-4k total traffic
    } else {
        // Grove delivers 85% more traffic on average for existing traffic
        const trafficMultiplier = 1.85;
        newTraffic = Math.round(currentTraffic * (trafficMultiplier - 1)); // Additional traffic only
    }
    const conversionRate = 0.02; // 2% e-commerce conversion rate (more conservative)
    const averageOrderValue = 65; // $65 average order value (more realistic)
    
    // Calculate revenue from new traffic
    const revenueGrowth = Math.round(newTraffic * conversionRate * averageOrderValue);
    
    // Update new visitors display
    document.getElementById('new-visitors').textContent = newTraffic.toLocaleString();
    
    // Fixed cost savings (vs typical $5,000/mo agency)
    const costSavings = 4501; // $5,000 - $499
    
    // Fixed time savings (20 hours/month dealing with agency/tools)
    const hoursSaved = 20;
    const timeSavings = hoursSaved * 100; // $100/hour value
    
    // Calculate total impact
    const totalImpact = revenueGrowth + costSavings + timeSavings;
    const annualImpact = totalImpact * 12;
    
    // Update display with smooth counter animation
    animateValue('revenue-growth', revenueGrowth);
    animateValue('cost-savings', costSavings);
    animateValue('time-savings', timeSavings);
    animateValue('total-impact', totalImpact);
    animateValue('annual-impact', annualImpact);
    
    // Update hours saved
    document.getElementById('hours-saved').textContent = hoursSaved;
    
    
    // Show CTA if ROI is high
    if (totalImpact > 10000) {
        roiCta.style.display = 'block';
    } else {
        roiCta.style.display = 'none';
    }
}

// Animate counter
function animateValue(id, value) {
    const element = document.getElementById(id);
    const current = parseInt(element.textContent.replace(/,/g, ''));
    const increment = (value - current) / 30;
    let step = 0;
    
    const timer = setInterval(() => {
        step++;
        const newValue = Math.round(current + (increment * step));
        element.textContent = newValue.toLocaleString();
        
        if (step >= 30) {
            clearInterval(timer);
            element.textContent = value.toLocaleString();
        }
    }, 20);
}

// Event listeners
if (trafficSlider) {
    trafficSlider.addEventListener('input', calculateROI);
    
    // Initialize on load
    calculateROI();
}

// Observe ROI calculator for animation
const roiObserver = new IntersectionObserver(function(entries) {
    entries.forEach(entry => {
        if (entry.isIntersecting && !entry.target.classList.contains('roi-animated')) {
            entry.target.classList.add('roi-animated');
            calculateROI(); // Recalculate to trigger animations
        }
    });
}, { threshold: 0.3 });

const roiCalculator = document.getElementById('roi-calculator');
if (roiCalculator) {
    roiObserver.observe(roiCalculator);
}

// URL Analysis Form and Popup
const urlAnalysisForm = document.getElementById('url-analysis-form');
const analysisPopup = document.getElementById('analysis-popup');
const analysisLoading = document.getElementById('analysis-loading');
const analysisComplete = document.getElementById('analysis-complete');
const analysisBody = document.getElementById('analysis-body');
const storeName = document.getElementById('store-name');

// Handle form submission
if (urlAnalysisForm) {
    urlAnalysisForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const storeUrl = document.getElementById('store-url').value;
        // Normalize URL - remove protocol and trailing slashes
        let normalizedUrl = storeUrl.replace(/^https?:\/\//, '').replace(/\/$/, '');
        
        // Redirect to pre-registration page with URL parameter
        window.location.href = '/pre-registration?store=' + encodeURIComponent(normalizedUrl);
    });
}

// Show analysis popup
function showAnalysisPopup(url) {
    // Extract store name from URL
    const urlParts = url.replace(/https?:\/\//, '').split('.');
    const name = urlParts[0].charAt(0).toUpperCase() + urlParts[0].slice(1);
    storeName.textContent = name + ' Store';
    
    // Set report date
    const reportDate = document.getElementById('report-date');
    const today = new Date();
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    reportDate.textContent = today.toLocaleDateString('en-US', options);
    
    // Show popup
    analysisPopup.style.display = 'block';
    analysisLoading.style.display = 'block';
    analysisComplete.style.display = 'none';
    analysisBody.style.display = 'none';
    
    // Update PDF status
    const pdfStatus = document.getElementById('pdf-status');
    
    // Simulate analysis process
    setTimeout(() => {
        analysisLoading.style.display = 'none';
        analysisComplete.style.display = 'block';
        
        // Update status
        pdfStatus.innerHTML = '<i class="fas fa-check-circle"></i><span>Report generated</span>';
        
        // Animate score circle
        animateScoreCircle();
        
        // Show body after score animation
        setTimeout(() => {
            analysisBody.style.display = 'block';
        }, 1000);
    }, 2500);
}

// Close analysis popup
function closeAnalysisPopup() {
    analysisPopup.style.display = 'none';
}

// Animate score circle
function animateScoreCircle() {
    const circle = document.querySelector('.ac-score-circle circle:last-child');
    const scoreValue = document.querySelector('.ac-score-value');
    
    // Animate circle stroke
    let dashOffset = 237;
    const targetDashOffset = 237; // 30% of circumference (339)
    const animationDuration = 1500;
    const startTime = Date.now();
    
    function animate() {
        const currentTime = Date.now();
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / animationDuration, 1);
        
        // Easing function
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        
        dashOffset = 339 - (339 - targetDashOffset) * easeOutQuart;
        circle.setAttribute('stroke-dashoffset', dashOffset);
        
        // Animate score number
        const score = Math.round(32 * easeOutQuart);
        scoreValue.textContent = score;
        
        if (progress < 1) {
            requestAnimationFrame(animate);
        }
    }
    
    animate();
}

// Close popup on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && analysisPopup.style.display === 'block') {
        closeAnalysisPopup();
    }
});

// Hero Dashboard Animation
function initHeroDashboardAnimation() {
    const productCard = document.getElementById('hero-product-card');
    const collectionCard = document.getElementById('hero-collection-card');
    const metricsBar = document.querySelector('.ac-hero-metrics-bar');
    
    if (!productCard || !collectionCard) return;
    
    const productToggle = productCard.querySelector('.ac-hero-toggle-slider');
    const collectionToggle = collectionCard.querySelector('.ac-hero-toggle-slider');
    const productFeed = productCard.querySelector('.ac-hero-feed');
    const collectionFeed = collectionCard.querySelector('.ac-hero-feed');
    
    let animationSequence;
    let isAnimating = false;
    
    function activateToggle(card, toggle, feed) {
        card.classList.add('active');
        toggle.classList.add('active');
        feed.classList.add('active');
        
        // Show spinning gear icon
        const gearIcon = card.querySelector('.ac-automation-icon');
        if (gearIcon) {
            gearIcon.style.display = 'inline-block';
        }
        
        // Reset and replay feed animations
        const feedItems = feed.querySelectorAll('.ac-hero-feed-item');
        feedItems.forEach((item, index) => {
            // Ensure opacity is reset
            item.style.opacity = '0';
            item.style.animation = 'none';
            
            // Force browser to recognize the change
            void item.offsetHeight;
            
            setTimeout(() => {
                item.style.animation = `heroSlideIn 0.5s ease ${index * 0.3}s forwards`;
            }, 50);
        });
    }
    
    function deactivateToggle(card, toggle, feed) {
        card.classList.remove('active');
        toggle.classList.remove('active');
        feed.classList.remove('active');
    }
    
    function runAnimation() {
        if (isAnimating) return;
        isAnimating = true;
        
        // Step 1: Activate Product SEO
        setTimeout(() => {
            activateToggle(productCard, productToggle, productFeed);
        }, 1000);
        
        // Step 2: Activate Collection Builder
        setTimeout(() => {
            activateToggle(collectionCard, collectionToggle, collectionFeed);
        }, 3000);
        
        // Step 3: Show metrics
        setTimeout(() => {
            metricsBar.classList.add('active');
            animateHeroMetrics();
        }, 4500);
        
        // Step 4: Pause to show complete state
        setTimeout(() => {
            // Add glow effect to cards
            productCard.style.animation = 'heroGlowPulse 2s ease-in-out';
            collectionCard.style.animation = 'heroGlowPulse 2s ease-in-out 0.5s';
        }, 6000);
        
        // Step 5: Transition to live mode instead of resetting
        setTimeout(() => {
            productCard.style.animation = '';
            collectionCard.style.animation = '';
            isAnimating = false;
            
            // Stop the main animation loop
            clearInterval(animationSequence);
            
            // Start live mode
            startLiveMode();
        }, 9000);
    }
    
    function animateHeroMetrics() {
        const metrics = metricsBar.querySelectorAll('.ac-hero-metric-value');
        const values = [156, 89, '$8.4K'];
        
        metrics.forEach((metric, index) => {
            if (index < 2) {
                // Animate numbers
                let current = 0;
                const target = parseInt(values[index]);
                const increment = target / 30;
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    metric.textContent = Math.floor(current);
                }, 50);
            } else {
                // Just show the revenue
                metric.textContent = values[index];
            }
        });
    }
    
    // Start initial animation after page load
    setTimeout(runAnimation, 2000);
    
    // Repeat animation every 12 seconds
    animationSequence = setInterval(runAnimation, 12000);
    
    // Pause animation when user hovers
    const dashboard = document.querySelector('.ac-hero-dashboard');
    if (dashboard) {
        dashboard.addEventListener('mouseenter', () => {
            clearInterval(animationSequence);
            stopLiveMode();
            isAnimating = false;
        });
        
        dashboard.addEventListener('mouseleave', () => {
            if (liveMode) {
                startLiveMode();
            }
        });
    }
    
    let liveMode = false;
    let liveIntervals = [];
    
    function startLiveMode() {
        if (liveMode) return;
        liveMode = true;
        
        // Show live indicator
        const liveIndicator = document.querySelector('.ac-live-indicator');
        if (liveIndicator) {
            liveIndicator.style.display = 'flex';
        }
        
        // Add subtle glow to toggles
        const productToggle = document.querySelector('#hero-product-card .ac-hero-toggle-slider');
        const collectionToggle = document.querySelector('#hero-collection-card .ac-hero-toggle-slider');
        if (productToggle) productToggle.classList.add('live-mode');
        if (collectionToggle) collectionToggle.classList.add('live-mode');
        
        // Start continuous counter updates
        startLiveCounters();
        
        // Start notification system
        startNotifications();
        
        // Start rolling feed updates
        startRollingFeed();
    }

    function startLiveCounters() {
        const metricsBar = document.querySelector('.ac-hero-metrics-bar');
        if (!metricsBar) return;
        
        const metrics = metricsBar.querySelectorAll('.ac-hero-metric-value');
        
        // Update counters every 2-3 seconds
        liveIntervals.push(setInterval(() => {
            // Products optimized - bigger increases
            const productsMetric = metrics[0];
            const currentProducts = parseInt(productsMetric.textContent);
            productsMetric.textContent = currentProducts + Math.floor(Math.random() * 5) + 3; // Increased from 3+1 to 5+3
            
            // Add tick effect
            productsMetric.classList.remove('tick');
            void productsMetric.offsetWidth; // Force reflow
            productsMetric.classList.add('tick');
            
            // Keywords (more frequently and bigger increases)
            if (Math.random() > 0.5) { // Changed from 0.7 to 0.5 for more frequency
                const keywordsMetric = metrics[1];
                const currentKeywords = parseInt(keywordsMetric.textContent);
                keywordsMetric.textContent = currentKeywords + Math.floor(Math.random() * 3) + 1; // Increased from +1 to random 1-3
                
                // Add tick effect
                keywordsMetric.classList.remove('tick');
                void keywordsMetric.offsetWidth;
                keywordsMetric.classList.add('tick');
            }
            
            // Revenue - bigger increases
            const revenueMetric = metrics[2];
            const currentRevenue = parseFloat(revenueMetric.textContent.replace('$', '').replace('K', '')) * 1000;
            const newRevenue = currentRevenue + (Math.random() * 500) + 200; // Increased from 100+20 to 500+200
            revenueMetric.textContent = '$' + (newRevenue / 1000).toFixed(1) + 'K';
            
            // Add tick effect
            revenueMetric.classList.remove('tick');
            void revenueMetric.offsetWidth;
            revenueMetric.classList.add('tick');
        }, 2500));
    }

    function startNotifications() {
        const notificationContainer = document.querySelector('.ac-hero-notifications');
        if (!notificationContainer) return;
        
        const productNotifications = [
            '11 New Sales Today',
            'SKU-324 Just Hit Page #1',
            'Product Page Views up 134%',
            '31 Product Titles Optimized',
            '+5 Product Keywords',
            '24 Meta Tags Updated'
        ];
        
        const collectionNotifications = [
            '7 New Collections Live',
            'Smart Rules Applied',
            '+83% Collection Traffic',
            'Fathers Day Collection Created',
            'Researching Collection Ideas',
            '3 Collections Trending'
        ];
        
        // Track notification positions to stack them
        let notificationCount = 0;
        const notificationGap = 50; // pixels between notifications
        
        // Show random notifications
        liveIntervals.push(setInterval(() => {
            if (Math.random() > 0.6) {
                // Randomly choose between product and collection notifications
                const isProduct = Math.random() > 0.5;
                const notifications = isProduct ? productNotifications : collectionNotifications;
                
                const notification = document.createElement('div');
                notification.className = 'ac-hero-notification';
                notification.textContent = notifications[Math.floor(Math.random() * notifications.length)];
                
                // Stack notifications vertically
                notification.style.top = (notificationCount * notificationGap) + 'px';
                notificationCount++;
                
                notificationContainer.appendChild(notification);
                
                // Remove after animation and adjust count
                setTimeout(() => {
                    notification.remove();
                    notificationCount = Math.max(0, notificationCount - 1);
                    
                    // Reposition remaining notifications
                    const remaining = notificationContainer.querySelectorAll('.ac-hero-notification');
                    remaining.forEach((notif, index) => {
                        notif.style.top = (index * notificationGap) + 'px';
                    });
                }, 4000);
            }
        }, 3000)); // Reduced interval for better flow
    }

    function startRollingFeed() {
        const productFeed = document.querySelector('#hero-product-card .ac-hero-feed');
        const collectionFeed = document.querySelector('#hero-collection-card .ac-hero-feed');
        
        // Track currently displayed items to prevent duplicates
        const displayedProductFeeds = new Set();
        const displayedCollectionFeeds = new Set();
        
        const productFeeds = [
            { icon: 'fa-check-circle', color: 'var(--ac-grove)', text: 'Optimized "Blue Denim Jacket"' },
            { icon: 'fa-trophy', color: '#fbbf24', text: 'Ranked #1 for "wireless earbuds"' },
            { icon: 'fa-star', color: '#fbbf24', text: 'Product SEO score: 98/100' },
            { icon: 'fa-chart-line', color: 'var(--ac-grove)', text: 'Product views up 156%' },
            { icon: 'fa-tag', color: '#ef4444', text: 'Meta title enhanced' },
            { icon: 'fa-image', color: 'var(--ac-grove)', text: 'Alt text optimized' }
        ];
        
        const collectionFeeds = [
            { icon: 'fa-fire', color: '#ef4444', text: '"Summer Sale" collection trending' },
            { icon: 'fa-layer-group', color: 'var(--ac-grove)', text: 'Smart collection created' },
            { icon: 'fa-check-circle', color: 'var(--ac-grove)', text: '"Best Sellers" optimized' },
            { icon: 'fa-chart-line', color: 'var(--ac-grove)', text: 'Collection traffic +234%' },
            { icon: 'fa-magic', color: '#fbbf24', text: 'Rules auto-applied' },
            { icon: 'fa-star', color: '#fbbf24', text: 'Featured in search results' }
        ];
        
        // Update feeds occasionally with staggered timing
        liveIntervals.push(setInterval(() => {
            // Update one feed at a time, not both
            const updateProduct = Math.random() > 0.5;
            
            if (updateProduct) {
                // Update product feed
                if (productFeed && Math.random() > 0.5) {
                    const items = productFeed.querySelectorAll('.ac-hero-feed-item:not(.ac-hero-metric)');
                    if (items.length > 0) {
                        // Get current displayed texts
                        displayedProductFeeds.clear();
                        items.forEach(item => {
                            const span = item.querySelector('span');
                            if (span) displayedProductFeeds.add(span.textContent.trim());
                        });
                        
                        // Find feeds not currently displayed
                        const availableFeeds = productFeeds.filter(f => !displayedProductFeeds.has(f.text));
                        if (availableFeeds.length > 0) {
                            // Pick a random non-metric item to update
                            const updateableItems = Array.from(items);
                            const randomItem = updateableItems[Math.floor(Math.random() * updateableItems.length)];
                            const newFeed = availableFeeds[Math.floor(Math.random() * availableFeeds.length)];
                            updateFeedItem(randomItem, newFeed);
                        }
                    }
                }
            } else {
                // Update collection feed
                if (collectionFeed) {
                    const items = collectionFeed.querySelectorAll('.ac-hero-feed-item:not(.ac-hero-metric)');
                    if (items.length > 0) {
                        // Get current displayed texts
                        displayedCollectionFeeds.clear();
                        items.forEach(item => {
                            const span = item.querySelector('span');
                            if (span) displayedCollectionFeeds.add(span.textContent.trim());
                        });
                        
                        // Find feeds not currently displayed
                        const availableFeeds = collectionFeeds.filter(f => !displayedCollectionFeeds.has(f.text));
                        if (availableFeeds.length > 0) {
                            // Pick a random non-metric item to update
                            const updateableItems = Array.from(items);
                            const randomItem = updateableItems[Math.floor(Math.random() * updateableItems.length)];
                            const newFeed = availableFeeds[Math.floor(Math.random() * availableFeeds.length)];
                            updateFeedItem(randomItem, newFeed);
                        }
                    }
                }
            }
        }, 3500)); // Reduced interval for more frequent updates
        
        function updateFeedItem(item, feed) {
            // Skip if this is a metric item (has different structure)
            if (item.classList.contains('ac-hero-metric')) {
                return;
            }
            
            // Add highlight effect
            item.classList.add('highlight');
            
            // Fade out
            item.style.transition = 'opacity 0.2s ease';
            item.style.opacity = '0';
            
            setTimeout(() => {
                // Always rebuild the complete structure to ensure consistency
                const iconHtml = `<i class="fas ${feed.icon}" style="color: ${feed.color}; width: 1.25rem; height: 1.25rem; display: inline-flex; align-items: center; justify-content: center; font-size: 1rem;"></i>`;
                const spanHtml = `<span style="font-size: 0.875rem; color: #333; line-height: 1.2;">${feed.text}</span>`;
                
                item.innerHTML = iconHtml + spanHtml;
                
                // Force Font Awesome to re-render
                if (window.FontAwesome) {
                    window.FontAwesome.dom.scan();
                }
                
                // Force browser to recalculate styles
                void item.offsetHeight;
                
                // Fade back in
                item.style.opacity = '1';
                
                // Remove highlight after animation
                setTimeout(() => {
                    item.classList.remove('highlight');
                }, 600);
            }, 200);
        }
    }

    function stopLiveMode() {
        liveMode = false;
        liveIntervals.forEach(interval => clearInterval(interval));
        liveIntervals = [];
    }
}

// Initialize hero animation when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initHeroDashboardAnimation);
} else {
    initHeroDashboardAnimation();
}

// AI Agent Animation
function initAIAgentAnimation() {
    const aiAgentCards = document.querySelectorAll('.ac-ai-agent-card');
    const commandPrompt = document.querySelector('.ac-prompt-text');
    
    // Observe when AI section comes into view
    const aiObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // Activate agents sequentially
                aiAgentCards.forEach((card, index) => {
                    setTimeout(() => {
                        const toggle = card.querySelector('input[type="checkbox"]');
                        if (toggle && !toggle.checked) {
                            toggle.checked = true;
                            card.classList.add('active');
                            
                            // Update command prompt
                            updateCommandPrompt(index + 1, aiAgentCards.length);
                        }
                    }, index * 500);
                });
                
                // Start activity log animations
                setTimeout(startActivityAnimations, 2000);
            }
        });
    }, { threshold: 0.3 });
    
    const aiSection = document.querySelector('.ac-ai-automation-section');
    if (aiSection) {
        aiObserver.observe(aiSection);
    }
    
    // Handle manual toggle clicks
    aiAgentCards.forEach(card => {
        const toggle = card.querySelector('input[type="checkbox"]');
        if (toggle) {
            toggle.addEventListener('change', (e) => {
                if (e.target.checked) {
                    card.classList.add('active');
                } else {
                    card.classList.remove('active');
                }
                updateAgentCount();
            });
        }
    });
    
    function updateCommandPrompt(current, total) {
        if (commandPrompt) {
            if (current === total) {
                commandPrompt.textContent = 'All AI agents operating at peak efficiency';
            } else {
                commandPrompt.textContent = `Initializing AI agent ${current} of ${total}...`;
            }
        }
    }
    
    function updateAgentCount() {
        const activeAgents = document.querySelectorAll('.ac-ai-agent-card.active').length;
        const totalAgents = aiAgentCards.length;
        if (commandPrompt) {
            commandPrompt.textContent = `${activeAgents} of ${totalAgents} AI agents active`;
        }
    }
    
    function startActivityAnimations() {
        const activityLogs = [
            { card: 'ai-agent-products', entries: [
                { icon: 'fas fa-circle-notch fa-spin', text: 'Optimizing "Winter Jacket"...' },
                { icon: 'fas fa-check-circle', text: 'Added 12 LSI keywords' },
                { icon: 'fas fa-chart-line', text: '+62% search visibility' }
            ]},
            { card: 'ai-agent-collections', entries: [
                { icon: 'fas fa-lightbulb', text: 'Trend detected: "Cozy Home"' },
                { icon: 'fas fa-layer-group', text: 'Building collection...' },
                { icon: 'fas fa-fire', text: '47 products matched' }
            ]},
            { card: 'ai-agent-search', entries: [
                { icon: 'fas fa-satellite-dish', text: 'Scanning SERPs...' },
                { icon: 'fas fa-trophy', text: '3 keywords hit #1' },
                { icon: 'fas fa-bullseye', text: '23 gaps identified' }
            ]}
        ];
        
        // Cycle through different activities
        setInterval(() => {
            const randomLog = activityLogs[Math.floor(Math.random() * activityLogs.length)];
            const card = document.getElementById(randomLog.card);
            if (card && card.classList.contains('active')) {
                const logContainer = card.querySelector('.ac-activity-log');
                if (logContainer) {
                    updateActivityLog(logContainer, randomLog.entries);
                }
            }
        }, 5000);
    }
    
    function updateActivityLog(container, newEntries) {
        // Fade out current entries
        const currentEntries = container.querySelectorAll('.ac-log-entry');
        currentEntries.forEach((entry, index) => {
            setTimeout(() => {
                entry.style.opacity = '0';
                entry.style.transform = 'translateX(-10px)';
            }, index * 100);
        });
        
        // Update with new entries
        setTimeout(() => {
            container.innerHTML = newEntries.map(entry => `
                <div class="ac-log-entry">
                    <i class="${entry.icon}"></i>
                    <span>${entry.text}</span>
                </div>
            `).join('');
            
            // Trigger reflow and animations
            container.offsetHeight;
            container.querySelectorAll('.ac-log-entry').forEach((entry, index) => {
                entry.style.opacity = '0';
                entry.style.transform = 'translateX(-10px)';
                setTimeout(() => {
                    entry.style.opacity = '1';
                    entry.style.transform = 'translateX(0)';
                }, index * 100);
            });
        }, 500);
    }
}

// Initialize AI animations
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initAIAgentAnimation);
} else {
    initAIAgentAnimation();
}

// Light Theme AI Section Animations
function initLightThemeAI() {
    const lightAgentCards = document.querySelectorAll('.ac-ai-light-card');
    const lightSection = document.querySelector('.ac-ai-light-section');
    
    if (!lightSection) return;
    
    // Toggle functionality for light theme
    lightAgentCards.forEach(card => {
        const toggle = card.querySelector('input[type="checkbox"]');
        if (toggle) {
            toggle.addEventListener('change', function() {
                if (this.checked) {
                    card.classList.add('active');
                } else {
                    card.classList.remove('active');
                }
                updateLightCommandStatus();
            });
        }
    });
    
    // Update command center status
    function updateLightCommandStatus() {
        const activeAgents = document.querySelectorAll('.ac-ai-light-card.active').length;
        const statValue = document.querySelector('.ac-light-stats-grid .ac-stat-value');
        if (statValue) {
            statValue.textContent = `${activeAgents}/8`;
        }
    }
    
    // Intersection Observer for light theme section
    const lightObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // Stagger card animations
                lightAgentCards.forEach((card, index) => {
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                        
                        // Auto-activate toggles
                        setTimeout(() => {
                            const toggle = card.querySelector('input[type="checkbox"]');
                            if (toggle && !toggle.checked) {
                                toggle.checked = true;
                                card.classList.add('active');
                                updateLightCommandStatus();
                            }
                        }, 500 + (index * 200));
                    }, index * 100);
                });
                
                lightObserver.disconnect();
            }
        });
    }, { threshold: 0.3 });
    
    lightObserver.observe(lightSection);
    
    // Initial card styling
    lightAgentCards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
    });
}

// Initialize light theme on DOM ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initLightThemeAI);
} else {
    initLightThemeAI();
}

// Carbon Light Theme AI Section
function initCarbonLightAI() {
    const carbonAgentCards = document.querySelectorAll('.ac-ai-agent-card-light');
    const carbonSection = document.querySelector('.ac-ai-carbon-light-section');
    
    if (!carbonSection) return;
    
    // Toggle functionality
    carbonAgentCards.forEach(card => {
        const toggle = card.querySelector('input[type="checkbox"]');
        if (toggle) {
            toggle.addEventListener('change', function() {
                if (this.checked) {
                    card.classList.add('active');
                } else {
                    card.classList.remove('active');
                }
                updateCarbonCommandPrompt();
            });
        }
    });
    
    // Update command prompt
    function updateCarbonCommandPrompt() {
        const activeAgents = document.querySelectorAll('.ac-ai-agent-card-light.active').length;
        const commandPrompt = document.querySelector('.ac-command-prompt-light span:nth-child(2)');
        if (commandPrompt) {
            commandPrompt.textContent = `${activeAgents} of 8 AI agents optimizing your store 24/7`;
        }
    }
    
    // Intersection Observer for carbon section
    const carbonObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // Activate agents sequentially
                carbonAgentCards.forEach((card, index) => {
                    setTimeout(() => {
                        const toggle = card.querySelector('input[type="checkbox"]');
                        if (toggle && !toggle.checked) {
                            toggle.checked = true;
                            card.classList.add('active');
                        }
                        updateCarbonCommandPrompt();
                    }, index * 300);
                });
                
                // Start activity animations after activation
                setTimeout(startCarbonActivityAnimations, 2000);
                
                carbonObserver.disconnect();
            }
        });
    }, { threshold: 0.3 });
    
    carbonObserver.observe(carbonSection);
    
    // Activity animations for carbon light theme
    function startCarbonActivityAnimations() {
        const activityLogs = [
            { card: 'carbon-agent-products', entries: [
                { icon: 'fas fa-circle-notch fa-spin', text: 'Optimizing "Winter Jacket"...' },
                { icon: 'fas fa-check-circle', text: 'Added 12 LSI keywords' },
                { icon: 'fas fa-chart-line', text: '+62% search visibility' }
            ]},
            { card: 'carbon-agent-collections', entries: [
                { icon: 'fas fa-lightbulb', text: 'Trend detected: "Cozy Home"' },
                { icon: 'fas fa-layer-group', text: 'Building collection...' },
                { icon: 'fas fa-fire', text: '47 products matched' }
            ]},
            { card: 'carbon-agent-search', entries: [
                { icon: 'fas fa-satellite-dish', text: 'Scanning SERPs...' },
                { icon: 'fas fa-trophy', text: '3 keywords hit #1' },
                { icon: 'fas fa-bullseye', text: '23 gaps identified' }
            ]}
        ];
        
        // Cycle through different activities
        setInterval(() => {
            const randomLog = activityLogs[Math.floor(Math.random() * activityLogs.length)];
            const card = document.getElementById(randomLog.card);
            if (card && card.classList.contains('active')) {
                const logContainer = card.querySelector('.ac-activity-log');
                if (logContainer) {
                    updateCarbonActivityLog(logContainer, randomLog.entries);
                }
            }
        }, 5000);
    }
    
    function updateCarbonActivityLog(container, newEntries) {
        // Fade out current entries
        const currentEntries = container.querySelectorAll('.ac-log-entry');
        currentEntries.forEach((entry, index) => {
            setTimeout(() => {
                entry.style.opacity = '0';
                entry.style.transform = 'translateX(-10px)';
            }, index * 100);
        });
        
        // Update with new entries
        setTimeout(() => {
            container.innerHTML = newEntries.map(entry => `
                <div class="ac-log-entry">
                    <i class="${entry.icon}"></i>
                    <span>${entry.text}</span>
                </div>
            `).join('');
            
            // Trigger reflow and animations
            container.offsetHeight;
            container.querySelectorAll('.ac-log-entry').forEach((entry, index) => {
                setTimeout(() => {
                    entry.style.opacity = '1';
                    entry.style.transform = 'translateX(0)';
                }, index * 100);
            });
        }, 500);
    }
}

// Initialize carbon light theme on DOM ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initCarbonLightAI);
} else {
    initCarbonLightAI();
}

// Rounded Light Theme AI Section
function initRoundedLightAI() {
    const roundedAgentCards = document.querySelectorAll('.ac-ai-agent-card-rounded');
    const roundedSection = document.querySelector('.ac-ai-rounded-light-section');
    
    if (!roundedSection) return;
    
    // Toggle functionality
    roundedAgentCards.forEach(card => {
        const toggle = card.querySelector('input[type="checkbox"]');
        if (toggle) {
            toggle.addEventListener('change', function() {
                if (this.checked) {
                    card.classList.add('active');
                } else {
                    card.classList.remove('active');
                }
                updateRoundedCommandPrompt();
            });
        }
    });
    
    // Update command prompt
    function updateRoundedCommandPrompt() {
        const activeAgents = document.querySelectorAll('.ac-ai-agent-card-rounded.active').length;
        const commandPrompt = document.querySelector('.ac-command-prompt-rounded span:nth-child(2)');
        if (commandPrompt) {
            commandPrompt.textContent = `${activeAgents} of 8 AI agents with rounded cards optimizing your store 24/7`;
        }
    }
    
    // Intersection Observer for rounded section
    const roundedObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // Activate agents sequentially with smooth toggle animations
                roundedAgentCards.forEach((card, index) => {
                    setTimeout(() => {
                        const toggle = card.querySelector('input[type="checkbox"]');
                        if (toggle && !toggle.checked) {
                            toggle.checked = true;
                            card.classList.add('active');
                        }
                        updateRoundedCommandPrompt();
                        
                        // Add pulse animation to feature icons (copied from automation section)
                        const featureIcons = card.querySelectorAll('.ac-automation-feature-icon');
                        featureIcons.forEach((icon, iconIndex) => {
                            setTimeout(() => {
                                icon.style.transform = 'scale(1.2)';
                                setTimeout(() => {
                                    icon.style.transform = 'scale(1)';
                                }, 200);
                            }, iconIndex * 100);
                        });
                    }, index * 600); // Stagger the toggle activations
                });
                
                // Start activity animations after all toggles are activated
                setTimeout(startRoundedActivityAnimations, 5000);
                
                roundedObserver.disconnect();
            }
        });
    }, { threshold: 0.3 });
    
    roundedObserver.observe(roundedSection);
    
    // Live strip animations for rounded theme
    function startRoundedActivityAnimations() {
        const liveUpdates = [
            { card: 'rounded-agent-products', messages: ['Analyzing 1,247 products...', 'Optimizing "Winter Jacket"...', 'Generated 89 descriptions', 'Keywords optimized +47%'] },
            { card: 'rounded-agent-collections', messages: ['Found trending opportunity', 'Creating "Valentine\'s Essentials"', 'Auto-categorizing 34 products', 'Collection performance +3.2x'] },
            { card: 'rounded-agent-search', messages: ['Monitoring 1,892 keywords', 'Scanning SERPs...', '3 keywords hit #1', '89 opportunities found'] },
            { card: 'rounded-agent-content', messages: ['Writing buying guide...', 'Optimizing content flow', 'Adding internal links', 'Publishing in 3 languages'] },
            { card: 'rounded-agent-technical', messages: ['Site health scan complete', 'Page speed optimized', 'Schema markup updated', 'Security checks passed'] },
            { card: 'rounded-agent-competitor', messages: ['Tracking 47 competitors', 'Price gaps detected', 'Market insights updated', '12 new opportunities'] },
            { card: 'rounded-agent-linkbuilding', messages: ['Outreach in progress...', 'Building partnerships', 'Domain authority +12', '3 new backlinks secured'] },
            { card: 'rounded-agent-translations', messages: ['Translating to Spanish...', 'French catalog complete', 'Cultural adaptation done', 'Reaching 2.1M users'] }
        ];
        
        // Cycle through different live updates
        setInterval(() => {
            liveUpdates.forEach(agent => {
                const card = document.getElementById(agent.card);
                if (card && card.classList.contains('active')) {
                    const liveStrip = card.querySelector('.ac-live-text');
                    if (liveStrip) {
                        const randomMessage = agent.messages[Math.floor(Math.random() * agent.messages.length)];
                        liveStrip.textContent = randomMessage;
                    }
                }
            });
        }, 4000);
    }
}

// Initialize rounded light theme on DOM ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initRoundedLightAI);
} else {
    initRoundedLightAI();
}

// Video testimonial functionality
function playTestimonialVideo() {
    const video = document.getElementById('testimonialVideo');
    const overlay = document.querySelector('.ac-video-overlay');
    
    if (video.muted) {
        // Unmute and show controls
        video.muted = false;
        overlay.style.opacity = '0';
        video.setAttribute('controls', 'true');
        // If video is paused, play it
        if (video.paused) {
            video.play();
        }
    } else {
        // Mute and hide controls
        video.muted = true;
        overlay.style.opacity = '1';
        video.removeAttribute('controls');
    }
}

// Reset video overlay when video ends
document.getElementById('testimonialVideo').addEventListener('ended', function() {
    const overlay = document.querySelector('.ac-video-overlay');
    overlay.style.opacity = '1';
    this.removeAttribute('controls');
    this.muted = true;
    this.currentTime = 0;
});

// Animate testimonial metrics on scroll
const testimonialObserver = new IntersectionObserver(function(entries) {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            const metrics = entry.target.querySelectorAll('.ac-testimonial-metric-value');
            metrics.forEach(metric => {
                const value = metric.textContent;
                if (value === '547%') {
                    animateCounter(metric, 0, 547, '', '%');
                } else if (value === '75K') {
                    animateCounter(metric, 0, 75, '', 'K');
                } else if (value === '90') {
                    animateCounter(metric, 0, 90);
                }
            });
            testimonialObserver.unobserve(entry.target);
        }
    });
}, { threshold: 0.5 });

const testimonialSection = document.querySelector('.ac-testimonial-section');
if (testimonialSection) testimonialObserver.observe(testimonialSection);

// AI Brain Status Cycling
function cycleBrainStatus() {
    const statusElement = document.getElementById('brain-status');
    if (!statusElement) return;
    
    const statuses = [
        'Orchestrating',
        'Analyzing SEO',
        'Optimizing Products',
        'Building Collections',
        'Creating Content',
        'Monitoring Rankings',
        'Processing Tasks',
        'Learning Patterns'
    ];
    
    let currentIndex = 0;
    
    setInterval(() => {
        currentIndex = (currentIndex + 1) % statuses.length;
        statusElement.style.opacity = '0';
        setTimeout(() => {
            statusElement.textContent = statuses[currentIndex];
            statusElement.style.opacity = '1';
        }, 300);
    }, 2500);
}

// Initialize brain status cycling
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', cycleBrainStatus);
} else {
    cycleBrainStatus();
}

// Brain to Dashboard Task Flow Animation
function initBrainDashboardAnimation() {
    const dashboard = document.querySelector('.ac-brain-dashboard');
    const taskIcon = document.querySelector('.ac-task-icon');
    const notificationsList = document.querySelector('.ac-notifications-list');
    if (!dashboard || !taskIcon || !notificationsList) return;
    
    // Define agent types that correspond to notifications
    const agents = [
        { 
            icon: 'fas fa-brain', 
            type: 'product',
            title: 'Product SEO Engine',
            messages: [
                'Optimized 24 product titles for search rankings',
                'Enhanced meta descriptions for 18 products',
                'Generated SEO-friendly URLs for 32 products',
                'Updated alt tags for improved accessibility'
            ]
        },
        { 
            icon: 'fas fa-network-wired', 
            type: 'collections',
            title: 'Smart Collections',
            messages: [
                'Created "Summer Essentials" smart collection',
                'Built "Trending Products" automated rules',
                'Updated "Best Sellers" collection criteria',
                'Generated "Winter Must-Haves" collection'
            ]
        },
        { 
            icon: 'fas fa-microscope', 
            type: 'search',
            title: 'Search Insights',
            messages: [
                'Tracked 150+ keywords, found 12 opportunities',
                'Identified 8 high-potential ranking gaps',
                'Monitored competitor movements on 25 terms',
                'Updated Search Console performance data'
            ]
        },
        { 
            icon: 'fas fa-feather-alt', 
            type: 'content',
            title: 'Content Machine',
            messages: [
                'Published weekly blog post on trending topics',
                'Generated buyer guides for top collections',
                'Created category landing page content',
                'Built internal link strategy for authority'
            ]
        },
        { 
            icon: 'fas fa-cog', 
            type: 'technical',
            title: 'Technical SEO',
            messages: [
                'Optimized site structure and navigation',
                'Generated XML sitemaps for new products',
                'Fixed 404 errors and redirect chains',
                'Enhanced schema markup for rich snippets'
            ]
        },
        { 
            icon: 'fas fa-user-secret', 
            type: 'competitor',
            title: 'Competitor Intel',
            messages: [
                'Analyzed competitor keyword strategies',
                'Identified content gaps to exploit',
                'Tracked competitor ranking changes',
                'Found new link building opportunities'
            ]
        },
        { 
            icon: 'fas fa-link', 
            type: 'linkbuilding',
            title: 'Link Building',
            messages: [
                'Built 47 internal links for page authority',
                'Connected 12 collections strategically',
                'Optimized navigation structure flow',
                'Enhanced breadcrumb link architecture'
            ]
        },
        { 
            icon: 'fas fa-globe', 
            type: 'translation',
            title: 'Translations',
            messages: [
                'Localized 18 products for Spanish market',
                'Translated descriptions for German audience',
                'Updated Italian content for 22 products',
                'Added Portuguese support for 16 items'
            ]
        }
    ];
    
    let currentAgentIndex = 0;
    let notificationCounter = 0;
    
    function createNewNotification(agent) {
        // Get a random message for this agent
        const message = agent.messages[Math.floor(Math.random() * agent.messages.length)];
        
        // Create notification element
        const notification = document.createElement('div');
        notification.className = 'ac-notification new';
        notification.innerHTML = `
            <div class="ac-notification-icon">
                <i class="${agent.icon}"></i>
            </div>
            <div class="ac-notification-content">
                <div class="ac-notification-title">${agent.title}</div>
                <div class="ac-notification-message">${message}</div>
            </div>
            <div class="ac-notification-time">Just now</div>
        `;
        
        // Insert at the top of the list
        notificationsList.insertBefore(notification, notificationsList.firstChild);
        
        // Keep only 5 notifications maximum
        while (notificationsList.children.length > 5) {
            notificationsList.removeChild(notificationsList.lastChild);
        }
        
        // Update timestamps for existing notifications
        setTimeout(() => {
            updateNotificationTimestamps();
        }, 100);
    }
    
    function updateNotificationTimestamps() {
        const notifications = notificationsList.querySelectorAll('.ac-notification');
        const timestamps = ['Just now', '2m', '5m', '8m', '12m'];
        
        notifications.forEach((notification, index) => {
            const timeElement = notification.querySelector('.ac-notification-time');
            if (timeElement && timestamps[index]) {
                timeElement.textContent = timestamps[index];
            }
        });
    }
    
    function sendTaskIcon() {
        const currentAgent = agents[currentAgentIndex];
        const scheduledIndicator = document.querySelector('.ac-task-scheduled-indicator');
        
        // Update the traveling icon
        taskIcon.innerHTML = `<i class="${currentAgent.icon}"></i>`;
        taskIcon.setAttribute('data-agent', currentAgent.type);
        
        // Reset and start animation
        taskIcon.classList.remove('traveling');
        taskIcon.offsetHeight; // Force reflow
        taskIcon.classList.add('traveling');
        
        // When icon reaches dashboard (at 85% of 1.5s = 1.275s)
        setTimeout(() => {
            // Add visual feedback - pink border for receiving
            dashboard.classList.add('task-received');
            
            // Show "TASK SCHEDULED" indicator
            if (scheduledIndicator) {
                scheduledIndicator.classList.add('show');
            }
            
            // Create transformation effect
            taskIcon.style.transform = 'scale(1.5)';
            taskIcon.style.opacity = '0';
            
            // After brief moment, show task scheduled confirmation
            setTimeout(() => {
                dashboard.classList.remove('task-received');
                dashboard.classList.add('task-scheduled');
                
                // Create new notification at the top
                createNewNotification(currentAgent);
                
                // Hide the scheduled indicator after showing it
                setTimeout(() => {
                    if (scheduledIndicator) {
                        scheduledIndicator.classList.remove('show');
                    }
                    dashboard.classList.remove('task-scheduled');
                }, 1500);
                
                // Reset task icon
                taskIcon.style.transform = '';
                taskIcon.style.opacity = '';
            }, 400);
        }, 1275);
        
        // Move to next agent
        currentAgentIndex = (currentAgentIndex + 1) % agents.length;
    }
    
    // Start first icon immediately
    sendTaskIcon();
    
    // Send new icon every 2.5 seconds (faster cycle)
    setInterval(sendTaskIcon, 2500);
}

// Initialize brain dashboard interaction
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initBrainDashboardAnimation);
} else {
    initBrainDashboardAnimation();
}

</script>

    
</body>

<!-- Mirrored from seogrove.ai/ by HTTrack Website Copier/3.x [XR&CO'2014], Wed, 09 Jul 2025 16:39:38 GMT -->
</html>