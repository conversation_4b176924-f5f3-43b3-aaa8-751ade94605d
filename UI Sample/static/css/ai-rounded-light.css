/* Rounded Carbon Copy Light Theme AI Automation Section */
.ac-ai-rounded-light-section {
    padding: 6rem 1rem;
    background: var(--ac-bg-light);
    color: var(--ac-text-primary);
    position: relative;
    overflow: hidden;
}

/* AI Sparkles for automation section */
.ac-ai-sparkle {
    position: absolute;
    pointer-events: none;
}

.ac-ai-sparkle svg {
    width: 24px;
    height: 24px;
    fill: #ef2b70;
    opacity: 0.6;
    animation: sparkPulse 3s ease-in-out infinite;
}

.ac-ai-sparkle.small svg {
    width: 16px;
    height: 16px;
}

.ac-ai-sparkle.large svg {
    width: 32px;
    height: 32px;
}

/* Position sparkles around console */
.ac-sparkle-1 {
    display: none;
}

.ac-sparkle-2 {
    top: 10%;
    right: 8%;
    animation-delay: 1s;
}

.ac-sparkle-3 {
    display: none;
}

.ac-sparkle-4 {
    display: none;
}

.ac-sparkle-5 {
    display: none;
}

.ac-sparkle-6 {
    top: 30%;
    right: 10%;
    animation-delay: 2.5s;
}

@keyframes sparkPulse {
    0%, 100% {
        opacity: 0.4;
        transform: scale(0.9);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.1);
    }
}

.ac-ai-rounded-light-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 80%, rgba(239, 43, 112, 0.02) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(34, 197, 94, 0.02) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(59, 130, 246, 0.01) 0%, transparent 50%);
    pointer-events: none;
}

.ac-ai-badge-rounded {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.05) 0%, rgba(34, 197, 94, 0.1) 100%);
    border: 1px solid rgba(34, 197, 94, 0.3);
    padding: 0.5rem 1rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    color: var(--ac-grove);
    margin-bottom: 1.5rem;
    position: relative;
}

.ac-ai-pulse-rounded {
    position: absolute;
    top: 50%;
    left: 0.5rem;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    background: var(--ac-grove);
    border-radius: 50%;
    animation: aiPulse 2s ease-in-out infinite;
}

.ac-ai-console-rounded {
    max-width: 1400px;
    margin: 2rem auto;
    background: var(--ac-bg-white);
    border: 1px solid rgba(30, 21, 65, 0.08);
    border-radius: var(--ac-radius-lg);
    overflow: hidden;
    box-shadow: var(--ac-shadow-lg), 
                0 0 40px rgba(34, 197, 94, 0.05),
                0 0 80px rgba(34, 197, 94, 0.03);
    position: relative;
}

/* Subtle AI glow animation on hover */
.ac-ai-console-rounded::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, 
        transparent, 
        var(--ac-grove), 
        transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.ac-ai-console-rounded:hover::after {
    opacity: 0.5;
    animation: aiGlowMove 3s linear infinite;
}

@keyframes aiGlowMove {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

@keyframes aiGlowRotate {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.ac-console-header-rounded {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    background: var(--ac-bg-light);
    border-bottom: 1px solid rgba(30, 21, 65, 0.06);
}

.ac-console-title {
    font-size: 0.875rem;
    color: var(--ac-text-secondary);
}

.ac-status-indicator-rounded {
    width: 8px;
    height: 8px;
    background: var(--ac-grove);
    border-radius: 50%;
    animation: statusBlink 2s ease-in-out infinite;
}

.ac-ai-grid-rounded {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1px;
    background: rgba(30, 21, 65, 0.04);
    padding: 1px;
}

/* Key difference: Rounded cards */
.ac-ai-agent-card-rounded {
    background: var(--ac-bg-white);
    padding: 1.5rem;
    transition: all 1.2s cubic-bezier(0.16, 1, 0.3, 1);
    position: relative;
    overflow: hidden;
    border-radius: var(--ac-radius);
    margin: 8px;
    border: 2px solid rgba(30, 21, 65, 0.05);
    will-change: transform, opacity, filter;
}

.ac-ai-agent-card-rounded:hover {
    background: var(--ac-bg-light);
    z-index: 1;
    box-shadow: var(--ac-shadow);
    transform: translateY(-4px);
    border-color: rgba(34, 197, 94, 0.15);
}

.ac-ai-agent-card-rounded.active {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.02) 0%, rgba(34, 197, 94, 0.05) 100%);
    border: 2px solid var(--ac-grove);
    box-shadow: var(--ac-shadow), 0 0 0 1px rgba(34, 197, 94, 0.1);
    transition: all 0.8s cubic-bezier(0.16, 1, 0.3, 1);
}

.ac-agent-avatar-rounded {
    width: 48px;
    height: 48px;
    background: var(--ac-bg-light);
    border: 1px solid rgba(30, 21, 65, 0.08);
    border-radius: var(--ac-radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.ac-agent-avatar-rounded i {
    font-size: 1.25rem;
    color: var(--ac-text-secondary);
    transition: color 0.3s ease;
}

.ac-ai-agent-card-rounded.active .ac-agent-avatar-rounded {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(34, 197, 94, 0.15) 100%);
    border-color: rgba(34, 197, 94, 0.3);
}

.ac-ai-agent-card-rounded.active .ac-agent-avatar-rounded i {
    color: var(--ac-grove);
}

.ac-agent-status-ring-rounded {
    position: absolute;
    top: -2px;
    right: -2px;
    width: 12px;
    height: 12px;
    background: #e5e7eb;
    border: 2px solid white;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.ac-ai-agent-card-rounded.active .ac-agent-status-ring-rounded {
    background: var(--ac-grove);
    animation: ringPulse 2s ease-in-out infinite;
}

/* Agent header layout - ensures icon, name, and toggle are properly positioned */
.ac-agent-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.ac-agent-info {
    flex: 1;
    min-width: 0;
}

.ac-agent-name {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--ac-text-primary);
    margin: 0 0 0.25rem;
}

.ac-agent-role {
    font-size: 0.75rem;
    color: var(--ac-text-muted);
    margin: 0;
}

/* Rounded theme toggle - copied from automation section */
.ac-agent-toggle-rounded {
    position: relative;
    width: 52px;
    height: 28px;
    cursor: pointer;
    flex-shrink: 0;
    box-sizing: content-box;
}

.ac-agent-toggle-rounded input {
    opacity: 0;
    width: 0;
    height: 0;
}

.ac-toggle-slider-rounded {
    position: absolute;
    top: 0;
    left: 0;
    width: 52px;
    height: 28px;
    background-color: #ccc;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 34px;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
}

.ac-toggle-slider-rounded:before {
    position: absolute;
    content: "";
    height: 22px;
    width: 22px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.ac-agent-toggle-rounded input:checked + .ac-toggle-slider-rounded {
    background: linear-gradient(135deg, var(--ac-grove) 0%, var(--ac-grove-dark) 100%);
    box-shadow: 0 2px 8px rgba(34, 197, 94, 0.3);
}

.ac-agent-toggle-rounded input:checked + .ac-toggle-slider-rounded:before {
    transform: translateX(calc(52px - 22px - 6px));
    box-shadow: 0 2px 8px rgba(34, 197, 94, 0.3);
}

/* Automation features in rounded cards - exact copy from automation section */
.ac-ai-agent-card-rounded .ac-automation-features {
    list-style: none;
    padding: 0;
    margin: 1rem 0;
}

.ac-ai-agent-card-rounded .ac-automation-feature {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--ac-bg-light);
    transition: all 0.3s ease;
    opacity: 0.6;
}

.ac-ai-agent-card-rounded .ac-automation-feature:last-child {
    border-bottom: none;
}

.ac-ai-agent-card-rounded.active .ac-automation-feature {
    opacity: 1;
    transform: translateX(4px);
}

.ac-ai-agent-card-rounded .ac-automation-feature-icon {
    width: 32px;
    height: 32px;
    background: var(--ac-bg-light);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: var(--ac-text-muted);
    transition: all 0.3s ease;
}

.ac-ai-agent-card-rounded.active .ac-automation-feature-icon {
    background: rgba(34, 197, 94, 0.1);
    color: var(--ac-grove);
}

.ac-ai-agent-card-rounded .ac-automation-feature-text {
    flex: 1;
    font-size: 0.95rem;
    color: var(--ac-text-secondary);
}


/* Live strip styles */
.ac-agent-live-strip {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.05) 0%, rgba(34, 197, 94, 0.08) 100%);
    border-top: 1px solid rgba(34, 197, 94, 0.1);
    margin: 1rem -1.5rem -1.5rem -1.5rem;
    border-radius: 0 0 var(--ac-radius) var(--ac-radius);
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--ac-text-primary);
    opacity: 0;
    transition: all 0.4s ease;
}

.ac-ai-agent-card-rounded.active .ac-agent-live-strip {
    opacity: 1;
}

.ac-live-indicator {
    width: 6px;
    height: 6px;
    background: var(--ac-grove);
    border-radius: 50%;
    animation: livePulse 2s ease-in-out infinite;
}

@keyframes livePulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.1);
        box-shadow: 0 0 0 6px rgba(34, 197, 94, 0);
    }
}


/* Command center rounded */
.ac-ai-command-center-rounded {
    padding: 1.5rem;
    background: var(--ac-bg-light);
    border-top: 1px solid rgba(30, 21, 65, 0.06);
    border-radius: 0 0 var(--ac-radius-lg) var(--ac-radius-lg);
}

.ac-command-prompt-rounded {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    color: var(--ac-text-secondary);
}

.ac-command-prompt-rounded i {
    font-size: 1rem;
    color: var(--ac-grove);
}

.ac-prompt-cursor {
    display: inline-block;
    width: 8px;
    height: 16px;
    background: var(--ac-grove);
    animation: cursorBlink 1s ease-in-out infinite;
    margin-left: 0.25rem;
}


/* Responsive design */
@media (max-width: 1200px) {
    .ac-ai-grid-rounded {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .ac-ai-grid-rounded {
        grid-template-columns: 1fr;
    }
    
    .ac-ai-agent-card-rounded {
        padding: 1rem;
        margin: 4px;
    }
    
    .ac-agent-toggle-rounded {
        width: 48px;
        height: 26px;
    }
    
    .ac-toggle-slider-rounded {
        width: 48px;
        height: 26px;
    }
    
    .ac-toggle-slider-rounded:before {
        width: 20px;
        height: 20px;
    }
    
    .ac-agent-toggle-rounded input:checked + .ac-toggle-slider-rounded:before {
        transform: translateX(calc(48px - 20px - 6px));
    }
}

/* Animation keyframes */
@keyframes ringPulse {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
    }
    50% {
        box-shadow: 0 0 0 6px rgba(34, 197, 94, 0);
    }
}

@keyframes statusBlink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes logFadeIn {
    from {
        opacity: 0;
        transform: translateX(-10px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes cursorBlink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

@keyframes aiPulse {
    0%, 100% {
        opacity: 1;
        transform: translateY(-50%) scale(1);
        box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
    }
    50% {
        opacity: 0.8;
        transform: translateY(-50%) scale(1.1);
        box-shadow: 0 0 0 10px rgba(34, 197, 94, 0);
    }
}