/* Grove Mega Menu System */

/* Reset and Base Styles */
.ac-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: var(--ac-bg-white);
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

/* Ensure only one nav is visible */
.ac-nav:not(:first-of-type),
nav:not(:first-of-type) {
    display: none !important;
}

.ac-nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
    font-family: var(--ac-font-sans);
}

/* Logo Styles */
.ac-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
    font-size: 1.5rem;
    font-weight: 800;
    color: var(--ac-text-primary);
    transition: all 0.3s ease;
}

.ac-logo:hover {
    color: var(--ac-grove);
    transform: translateY(-1px);
}

/* Logo image styles */
.ac-logo-img {
    height: 44px;
    width: auto;
    display: block;
    object-fit: contain;
}

.ac-logo-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--ac-grove) 0%, #16a34a 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    transition: all 0.3s ease;
}

.ac-logo:hover .ac-logo-icon {
    transform: rotate(-10deg) scale(1.05);
}

/* Main Navigation Menu */
.ac-nav-menu {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Navigation Links */
.ac-nav-link {
    color: var(--ac-text-primary);
    text-decoration: none !important;
    font-family: var(--ac-font-sans);
    font-weight: 500;
    font-size: 0.9375rem;
    letter-spacing: -0.01em;
    line-height: 1.5;
    padding: 0.625rem 1rem;
    position: relative;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    border-radius: var(--ac-radius-sm);
    white-space: nowrap;
}

.ac-nav-link:not(.ac-dropdown-toggle)::before,
.ac-nav-link:not(.ac-dropdown-toggle)::after {
    display: none !important;
}

.ac-nav-link:hover {
    color: var(--ac-grove);
    background: rgba(34, 197, 94, 0.05);
    text-decoration: none !important;
}

.ac-nav-dropdown.active > .ac-nav-link {
    color: var(--ac-grove);
    background: rgba(34, 197, 94, 0.08);
}

/* Mega Menu Dropdown */
.ac-nav-dropdown {
    position: relative;
}

.ac-dropdown-toggle {
    position: relative;
    padding-right: 2rem;
}

.ac-dropdown-toggle::after {
    content: '\f078';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    display: inline-block;
    font-size: 0.75rem;
    color: inherit;
    transition: transform 0.2s ease;
    position: absolute;
    right: 0.25rem;
    top: 50%;
    margin-top: -0.375rem;
    pointer-events: none;
    line-height: 1;
    transform-origin: center center;
}

.ac-nav-dropdown:hover .ac-dropdown-toggle::after,
.ac-nav-dropdown.active .ac-dropdown-toggle::after {
    transform: rotate(180deg);
}

/* Mega Menu Panel */
.ac-mega-menu {
    position: absolute;
    top: calc(100% + 0.5rem);
    left: 0;
    width: 900px;
    max-width: calc(100vw - 4rem);
    background: white;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    padding: 2rem;
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
    transition: all 0.2s ease;
    transform: translateY(-8px);
    z-index: 999;
    border: 1px solid rgba(0, 0, 0, 0.06);
}

/* Fix for main content positioning */
body {
    padding-top: 80px;
}

/* Ensure no duplicate elements appear */
.ac-nav + * {
    margin-top: 0;
}

/* Remove any additional navigation that might be injected */
.ac-nav ~ .ac-nav {
    display: none !important;
}

/* Ensure the hero section starts properly after nav */
.ac-hero {
    margin-top: 0 !important;
    padding-top: 4rem;
}

/* Absolutely hide any duplicate nav or button elements */
body > nav:not(:first-of-type),
body > .ac-nav:not(:first-of-type),
main .ac-nav,
main nav,
.ac-hero .ac-nav,
.ac-hero nav,
body > a.ac-btn,
body > button.ac-btn,
body > div > a.ac-btn:not(.ac-nav-cta .ac-btn),
body > div > button.ac-btn:not(.ac-nav-cta .ac-btn) {
    display: none !important;
    visibility: hidden !important;
    position: absolute !important;
    left: -9999px !important;
}

/* Remove any pseudo-elements creating lines on nav links */
.ac-nav-link *::before,
.ac-nav-link *::after {
    display: none !important;
}

/* Ensure proper main content positioning */
main {
    position: relative;
    z-index: 1;
}

/* Hide any floating elements that shouldn't be visible */
.ac-nav + .ac-btn,
.ac-nav + div > .ac-btn,
.ac-nav ~ .ac-nav,
.ac-nav ~ .ac-hero-nav,
.ac-nav ~ .ac-secondary-nav,
body > .ac-btn:not(.ac-nav .ac-btn),
main > .ac-nav,
main > div > .ac-nav {
    display: none !important;
}

.ac-nav-dropdown:hover .ac-mega-menu,
.ac-nav-dropdown.active .ac-mega-menu {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
    transform: translateY(0);
}

/* Dynamic positioning will be handled by JavaScript */
.ac-mega-menu.align-right {
    left: auto;
    right: 0;
}

.ac-mega-menu.align-center {
    left: 50%;
    transform: translateX(-50%);
}

.ac-nav-dropdown:hover .ac-mega-menu.align-center,
.ac-nav-dropdown.active .ac-mega-menu.align-center {
    transform: translateX(-50%) translateY(0);
}

/* Mega Menu Grid */
.ac-mega-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2.5rem;
    margin-bottom: 1.5rem;
}

/* Why Grove Features Grid */
.ac-mega-grid-features {
    grid-template-columns: 1fr 1fr 0.8fr;
    gap: 3rem;
}

/* Feature Columns */
.ac-mega-feature-column {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.ac-mega-feature {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    border-radius: 12px;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    color: var(--ac-text-primary);
    background: rgba(34, 197, 94, 0.02);
    border: 2px solid transparent;
}

.ac-mega-feature::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.05) 0%, transparent 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.ac-mega-feature:hover {
    background: white;
    border-color: var(--ac-grove);
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(34, 197, 94, 0.15);
}

.ac-mega-feature:hover::before {
    opacity: 1;
}

.ac-mega-feature-icon {
    width: 44px;
    height: 44px;
    background: rgba(34, 197, 94, 0.1);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: var(--ac-grove);
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.ac-mega-feature:hover .ac-mega-feature-icon {
    background: var(--ac-grove);
    color: white;
    transform: scale(1.1);
}

.ac-mega-feature-content {
    flex: 1;
}

.ac-mega-feature-title {
    font-family: var(--ac-font-sans);
    font-weight: 700;
    color: var(--ac-text-primary);
    margin-bottom: 0.25rem;
    font-size: 1rem;
    letter-spacing: -0.01em;
    line-height: 1.5;
    display: block;
}

.ac-mega-feature-desc {
    font-size: 0.813rem;
    color: var(--ac-text-secondary);
    line-height: 1.4;
}

.ac-mega-column {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.ac-mega-column-header {
    font-size: 0.813rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: var(--ac-text-muted);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--ac-bg-gray);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Mega Menu Items */
.ac-mega-item {
    display: flex;
    align-items: flex-start;
    gap: 0.875rem;
    padding: 0.625rem;
    border-radius: 8px;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    color: var(--ac-text-primary);
}

.ac-mega-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.05) 0%, transparent 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.ac-mega-item:hover {
    background: var(--ac-bg-light);
    transform: translateX(4px);
}

.ac-mega-item:hover::before {
    opacity: 1;
}

.ac-mega-icon {
    width: 36px;
    height: 36px;
    background: rgba(34, 197, 94, 0.1);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: var(--ac-grove);
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.ac-mega-item:hover .ac-mega-icon {
    background: var(--ac-grove);
    color: white;
    transform: scale(1.1);
}

.ac-mega-content {
    flex: 1;
}

.ac-mega-title {
    font-family: var(--ac-font-sans);
    font-weight: 600;
    color: var(--ac-text-primary);
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
    letter-spacing: -0.01em;
    line-height: 1.5;
    display: block;
}

.ac-mega-desc {
    font-size: 0.75rem;
    color: var(--ac-text-secondary);
    line-height: 1.4;
}

/* Featured Section */
.ac-mega-featured {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.05) 0%, rgba(34, 197, 94, 0.1) 100%);
    padding: 1.25rem;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 0.5rem;
}

.ac-mega-featured-content {
    flex: 1;
}

.ac-mega-featured-title {
    font-size: 1rem;
    font-weight: 700;
    color: var(--ac-text-primary);
    margin-bottom: 0.375rem;
}

.ac-mega-featured-desc {
    color: var(--ac-text-secondary);
    margin-bottom: 1rem;
}

.ac-mega-featured-links {
    display: flex;
    gap: 1.5rem;
}

.ac-mega-featured-link {
    color: var(--ac-grove);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.ac-mega-featured-link:hover {
    gap: 0.75rem;
    color: var(--ac-grove-dark);
}

/* Simple Dropdown Menu */
.ac-dropdown-menu {
    position: absolute;
    top: calc(100% + 0.5rem);
    right: 0;
    min-width: 200px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    padding: 0.5rem;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-8px);
    transition: all 0.2s ease;
    z-index: 1001;
    border: 1px solid rgba(0, 0, 0, 0.06);
    pointer-events: none;
}

.ac-nav-dropdown:hover .ac-dropdown-menu,
.ac-nav-dropdown.active .ac-dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
    pointer-events: auto;
}

/* For simple dropdowns, position relative */
.ac-nav-dropdown.simple {
    position: relative;
}

.ac-dropdown-item {
    display: block;
    padding: 0.75rem 1rem;
    color: var(--ac-text-primary);
    text-decoration: none;
    font-family: var(--ac-font-sans);
    font-size: 0.875rem;
    font-weight: 500;
    letter-spacing: -0.01em;
    line-height: 1.5;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.ac-dropdown-item:hover {
    background: var(--ac-bg-light);
    color: var(--ac-grove);
    padding-left: 1.25rem;
}

.ac-dropdown-header {
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: var(--ac-text-muted);
    padding: 0.5rem 1rem;
    margin-bottom: 0.25rem;
}

.ac-dropdown-divider {
    height: 1px;
    background: var(--ac-bg-gray);
    margin: 0.5rem 0;
}

/* Mobile Menu Button */
.ac-mobile-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--ac-text-primary);
    cursor: pointer;
    padding: 0.5rem;
}

/* Badge Styles */
.ac-badge-new {
    background: var(--ac-primary);
    color: white;
    font-size: 0.5625rem;
    font-weight: 700;
    padding: 0.125rem 0.375rem;
    border-radius: 4px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-left: 0.375rem;
}

.ac-badge-hot {
    background: #ff6b6b;
    color: white;
    font-size: 0.5625rem;
    font-weight: 700;
    padding: 0.125rem 0.375rem;
    border-radius: 4px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-left: 0.375rem;
}

/* CTA Buttons in Nav */
.ac-nav-cta {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-left: 1.5rem;
}

.ac-nav-cta .ac-btn {
    padding: 0.625rem 1.25rem;
    font-size: 0.9375rem;
    font-family: 'Lexend', var(--ac-font-sans);
    font-weight: 600;
    letter-spacing: -0.01em;
    line-height: 1.5;
    border-radius: var(--ac-radius);
    transition: all 0.2s ease;
    white-space: nowrap;
}

.ac-nav-cta .ac-btn-secondary {
    background: transparent;
    color: var(--ac-text-primary);
    border: 2px solid rgba(0, 0, 0, 0.1);
}

.ac-nav-cta .ac-btn-secondary:hover {
    border-color: var(--ac-grove);
    color: var(--ac-grove);
    background: rgba(34, 197, 94, 0.05);
}

.ac-nav-cta .ac-btn-primary {
    background: var(--ac-gradient-primary);
    color: white;
    border: none;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05), 
                0 4px 8px rgba(239, 43, 112, 0.15);
}

.ac-nav-cta .ac-btn-primary:hover {
    background: var(--ac-gradient-primary);
    transform: translateY(-2px);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05),
                0 6px 12px rgba(239, 43, 112, 0.2),
                0 12px 24px rgba(239, 43, 112, 0.25);
}

/* Store Types Grid */
.ac-store-types {
    padding-right: 1rem;
}

.ac-store-types-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.625rem;
    margin-top: 1rem;
}

.ac-store-type {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.375rem;
    padding: 0.875rem 0.5rem;
    background: var(--ac-bg-light);
    border-radius: var(--ac-radius-sm);
    text-decoration: none;
    color: var(--ac-text-primary);
    font-size: 0.75rem;
    font-weight: 500;
    transition: all 0.2s ease;
    border: 1px solid transparent;
    text-align: center;
    min-height: 72px;
}

.ac-store-type span {
    display: block;
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

.ac-store-type:hover {
    background: white;
    border-color: var(--ac-grove);
    color: var(--ac-grove);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.15);
}

.ac-store-type i {
    font-size: 1.125rem;
    color: var(--ac-grove);
    flex-shrink: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Enhanced Store Type Cards */
.ac-store-types-enhanced {
    min-width: 420px;
}

.ac-store-types-grid-enhanced {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
    margin-top: 1rem;
}

/* Solutions Mega Menu - Full Width Layout */
.ac-mega-grid-solutions {
    display: block;
    width: 100%;
}

.ac-mega-solutions-main {
    width: 100%;
}

.ac-store-types-grid-full {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.625rem;
    margin-top: 1rem;
    margin-bottom: 1.5rem;
}

/* Problem Cards Grid */
.ac-problem-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.625rem;
    margin-top: 1rem;
}

.ac-problem-card {
    display: flex;
    align-items: center;
    gap: 0.875rem;
    padding: 1rem;
    background: transparent;
    border-radius: var(--ac-radius);
    text-decoration: none;
    color: var(--ac-text-primary);
    transition: all 0.2s ease;
    border: 1px solid transparent;
    position: relative;
    overflow: hidden;
}

.ac-problem-card:hover {
    background: rgba(239, 43, 112, 0.05);
    border-color: transparent;
    transform: translateY(-2px);
}

.ac-problem-icon {
    width: 36px;
    height: 36px;
    background: rgba(239, 43, 112, 0.1);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: var(--ac-primary);
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.ac-problem-card:hover .ac-problem-icon {
    background: var(--ac-primary);
    color: white;
    transform: scale(1.1);
}

.ac-problem-content {
    display: flex;
    flex-direction: column;
    gap: 0.125rem;
    flex: 1;
}

.ac-problem-title {
    font-weight: 700;
    font-size: 0.875rem;
    color: var(--ac-text-primary);
    line-height: 1.2;
}

.ac-problem-desc {
    font-size: 0.75rem;
    color: var(--ac-text-secondary);
    line-height: 1.3;
}

.ac-problem-card:hover .ac-problem-title {
    color: var(--ac-primary);
}

.ac-store-type-card {
    display: flex;
    align-items: center;
    gap: 0.875rem;
    padding: 0.875rem 1rem;
    background: transparent;
    border-radius: var(--ac-radius);
    text-decoration: none;
    color: var(--ac-text-primary);
    transition: all 0.2s ease;
    border: 1px solid transparent;
    position: relative;
    overflow: hidden;
}

.ac-store-type-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(34, 197, 94, 0) 0%, rgba(34, 197, 94, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.ac-store-type-card:hover {
    background: rgba(34, 197, 94, 0.05);
    border-color: transparent;
    transform: translateY(-2px);
}

.ac-store-type-card:hover::before {
    opacity: 1;
}

.ac-store-type-icon {
    width: 40px;
    height: 40px;
    background: rgba(34, 197, 94, 0.1);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: var(--ac-grove);
    flex-shrink: 0;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

.ac-store-type-card:hover .ac-store-type-icon {
    background: var(--ac-grove);
    color: white;
    transform: scale(1.1) rotate(-5deg);
    box-shadow: 0 4px 8px rgba(34, 197, 94, 0.25);
}

.ac-store-type-content {
    display: flex;
    flex-direction: column;
    gap: 0.125rem;
    flex: 1;
    position: relative;
    z-index: 1;
}

.ac-store-type-title {
    font-weight: 700;
    font-size: 0.875rem;
    color: var(--ac-text-primary);
    line-height: 1.2;
}

.ac-store-type-desc {
    font-size: 0.75rem;
    color: var(--ac-text-secondary);
    line-height: 1.3;
}

.ac-store-type-card:hover .ac-store-type-title {
    color: var(--ac-grove);
}

/* Special styling for "View All" card */
.ac-store-type-more {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.05) 0%, rgba(34, 197, 94, 0.1) 100%);
    border-color: rgba(34, 197, 94, 0.2);
}

.ac-store-type-more .ac-store-type-icon {
    background: var(--ac-grove);
    color: white;
}

.ac-store-type-more:hover {
    background: var(--ac-grove);
    border-color: var(--ac-grove);
}

.ac-store-type-more:hover .ac-store-type-title,
.ac-store-type-more:hover .ac-store-type-desc {
    color: white;
}

.ac-store-type-more:hover .ac-store-type-icon {
    transform: translateX(4px);
}

/* Company Dropdown Styles */
.ac-company-menu {
    min-width: 240px;
    padding: 0.5rem;
    position: absolute;
    top: calc(100% + 0.5rem);
    left: 0;
    background: white;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    border: 1px solid rgba(0, 0, 0, 0.06);
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
    transition: all 0.2s ease;
    transform: translateY(-8px);
    z-index: 999;
}

/* Better hover trigger for Company dropdown */
.ac-nav-dropdown:hover .ac-company-menu,
.ac-company-menu:hover {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
    transform: translateY(0);
}

/* Add hover bridge to prevent dropdown from closing */
.ac-nav-dropdown:has(.ac-company-menu)::before {
    content: '';
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    height: 0.5rem;
    background: transparent;
    z-index: 998;
}

.ac-company-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    text-decoration: none;
    color: var(--ac-text-primary);
    border-radius: 8px;
    transition: all 0.2s ease;
}

.ac-company-item:hover {
    background: var(--ac-bg-light);
    transform: translateX(4px);
}

.ac-company-icon {
    width: 36px;
    height: 36px;
    background: rgba(34, 197, 94, 0.1);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: var(--ac-grove);
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.ac-company-item:hover .ac-company-icon {
    background: var(--ac-grove);
    color: white;
    transform: scale(1.1);
}

.ac-company-content {
    display: flex;
    flex-direction: column;
    gap: 0.125rem;
}

.ac-company-title {
    font-weight: 600;
    font-size: 0.875rem;
    color: var(--ac-text-primary);
    white-space: nowrap;
}

.ac-company-desc {
    font-size: 0.75rem;
    color: var(--ac-text-secondary);
    white-space: nowrap;
}

/* Blog Items */
.ac-blog-item {
    display: block;
    padding: 0.875rem 0;
    text-decoration: none;
    color: var(--ac-text-primary);
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    transition: all 0.2s ease;
}

.ac-blog-item:hover {
    color: var(--ac-grove);
    transform: translateX(4px);
}

.ac-blog-item:last-of-type {
    border-bottom: none;
}

.ac-blog-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 0.5rem;
    font-size: 0.75rem;
    color: var(--ac-text-muted);
}

.ac-blog-title {
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.375rem;
    line-height: 1.3;
    color: inherit;
}

.ac-blog-excerpt {
    font-size: 0.75rem;
    color: var(--ac-text-secondary);
    line-height: 1.4;
    margin: 0;
}

.ac-blog-view-all {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--ac-grove);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.875rem;
    margin-top: 1rem;
    transition: gap 0.3s ease;
}

.ac-blog-view-all:hover {
    gap: 0.75rem;
    color: var(--ac-grove-dark);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .ac-mega-menu {
        width: 90vw;
    }
    
    .ac-mega-grid,
    .ac-mega-grid-features {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }
    
    .ac-mega-featured {
        grid-column: span 2;
    }
    
    .ac-store-types-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .ac-mega-feature {
        padding: 0.875rem;
    }
    
    .ac-mega-feature-icon {
        width: 40px;
        height: 40px;
        font-size: 1.125rem;
    }
}

@media (max-width: 768px) {
    .ac-nav-menu {
        display: none;
    }
    
    .ac-mobile-toggle {
        display: block;
    }
    
    .ac-mega-grid,
    .ac-mega-grid-features {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .ac-mega-featured {
        grid-column: span 1;
    }
    
    .ac-store-types-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.375rem;
    }
    
    .ac-store-type {
        padding: 0.375rem 0.5rem;
        font-size: 0.75rem;
    }
    
    .ac-mega-feature {
        padding: 0.75rem;
        gap: 0.75rem;
    }
    
    .ac-mega-feature-icon {
        width: 36px;
        height: 36px;
        font-size: 1rem;
    }
    
    .ac-mega-feature-title {
        font-size: 0.875rem;
    }
    
    .ac-mega-feature-desc {
        font-size: 0.75rem;
    }
    
    /* Mobile Menu Overlay */
    .ac-mobile-menu {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: white;
        z-index: 2000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        overflow-y: auto;
    }
    
    .ac-mobile-menu.active {
        transform: translateX(0);
    }
    
    .ac-mobile-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 1.5rem;
        border-bottom: 1px solid var(--ac-bg-gray);
    }
    
    .ac-mobile-close {
        background: none;
        border: none;
        font-size: 1.5rem;
        color: var(--ac-text-primary);
        cursor: pointer;
    }
    
    .ac-mobile-nav {
        padding: 1.5rem;
    }
    
    .ac-mobile-item {
        display: block;
        padding: 1rem;
        color: var(--ac-text-primary);
        text-decoration: none;
        font-weight: 600;
        border-bottom: 1px solid var(--ac-bg-gray);
    }
    
    .ac-mobile-submenu {
        padding-left: 2rem;
        padding-top: 0.5rem;
    }
    
    .ac-mobile-subitem {
        display: block;
        padding: 0.75rem 0;
        color: var(--ac-text-secondary);
        text-decoration: none;
        font-size: 0.875rem;
    }
}

/* Scroll Effect */
.ac-nav.scrolled {
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    border-bottom-color: rgba(0, 0, 0, 0.08);
}

.ac-nav.scrolled .ac-nav-container {
    height: 72px;
}

/* Search in Nav */
.ac-nav-search {
    position: relative;
    margin-right: 1rem;
}

.ac-nav-search-input {
    background: var(--ac-bg-light);
    border: 2px solid transparent;
    border-radius: var(--ac-radius-full);
    padding: 0.5rem 2.5rem 0.5rem 1rem;
    font-size: 0.875rem;
    width: 200px;
    transition: all 0.3s ease;
}

.ac-nav-search-input:focus {
    border-color: var(--ac-grove);
    width: 250px;
    outline: none;
}

.ac-nav-search-icon {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--ac-text-muted);
    font-size: 0.875rem;
}