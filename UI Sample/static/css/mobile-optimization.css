/* Grove Mobile Optimization - Homepage Test
 * This file contains mobile-first optimizations for the public frontend
 * Desktop styles (1024px+) remain unchanged in original files
 * 
 * Breakpoints:
 * - Mobile: 320px - 767px
 * - Tablet: 768px - 1023px
 * - Desktop: 1024px+ (preserve existing perfect design)
 */

/* ===================================
   Mobile-First Base Styles
   =================================== */

/* Hide desktop-specific elements on mobile */
@media (max-width: 1023px) {
    /* Reset excessive padding/margins for mobile */
    body {
        overflow-x: hidden !important;
    }
    
    /* Ensure no horizontal scrolling */
    .ac-hero,
    .ac-section,
    .ac-features,
    .ac-testimonials,
    .ac-cta,
    [class*="container"] {
        overflow-x: hidden !important;
    }
}

/* ===================================
   Mobile Styles (320px - 767px)
   =================================== */
@media (max-width: 767px) {
    
    /* Typography Scaling */
    :root {
        --mobile-scale: 0.875;
    }
    
    h1 {
        font-size: 2rem !important;
        line-height: 1.2 !important;
    }
    
    h2 {
        font-size: 1.75rem !important;
        line-height: 1.3 !important;
    }
    
    h3 {
        font-size: 1.25rem !important;
        line-height: 1.4 !important;
    }
    
    p {
        font-size: 1rem !important;
        line-height: 1.6 !important;
    }
    
    /* Container Adjustments - Uniform padding */
    .ac-container,
    .ac-features-container,
    .ac-hero-container,
    .ac-cta-container,
    .ac-section-container,
    .ac-opportunity-container,
    .ac-automation-container,
    .ac-pricing-hero-container,
    .ac-comparison-container,
    .ac-plans-container,
    /* From index.html */
    .ac-brain-icon-container,
    .ac-slider-container,
    .ac-testimonial-container,
    .ac-video-container,
    /* From features.html */
    .ac-features-hero-container,
    .ac-tabs-container,
    .ac-category-container,
    .ac-demo-container,
    /* From about.html */
    .ac-about-hero-container,
    .ac-team-container,
    .ac-values-container,
    /* From contact.html */
    .ac-contact-hero-container,
    .ac-contact-container,
    .ac-form-container,
    .ac-response-container,
    .ac-why-contact-container,
    .ac-faq-container,
    /* From pre-registration.html */
    .pre-reg-container,
    .form-container,
    /* General catch-all */
    .ac-section > .container,
    [class*="-container"]:not(.ac-mission-container) {
        padding: 0 1.5rem !important;
        max-width: 100% !important;
    }
    
    /* Uniform section padding - STANDARDIZED */
    section,
    .ac-section,
    .ac-features,
    .ac-automation-section,
    .ac-opportunity-section,
    .ac-testimonials,
    .ac-pricing-section,
    .ac-cta,
    .ac-logos-section,
    /* From features.html */
    .ac-features-hero,
    .ac-feature-category,
    .ac-feature-demo,
    .ac-features-cta,
    /* From about.html */
    .ac-about-hero,
    .ac-mission-section,
    .ac-team-section,
    .ac-values-section,
    .ac-testimonial-section,
    /* From contact.html */
    .ac-contact-hero,
    .ac-contact-section,
    .ac-form-section,
    .ac-response-section,
    .ac-why-contact-section,
    .ac-faq-section,
    /* From pre-registration.html */
    .pre-reg-hero,
    .ac-what-you-get-section {
        padding: 2.5rem 0 !important;
    }
    
    /* First section after nav */
    .ac-features-hero,
    .ac-about-hero,
    .ac-contact-hero,
    .ac-pricing-hero {
        padding-top: 5rem !important;
    }
    
    /* About Page - Complete mobile optimization */
    .ac-about-hero {
        padding: 5rem 1.5rem 2.5rem 1.5rem !important;
        overflow: visible !important;
        box-sizing: border-box !important;
    }
    
    .ac-team-section,
    .ac-values-section,
    .ac-testimonial-section {
        padding: 2.5rem 1.5rem !important;
        box-sizing: border-box !important;
    }
    
    /* All About page text elements - ensure proper wrapping */
    .ac-about-hero h1,
    .ac-about-hero-subtitle,
    .ac-section-title,
    .ac-section-subtitle,
    .ac-value-title,
    .ac-value-desc,
    .ac-founder-name,
    .ac-founder-role,
    .ac-testimonial-text h2,
    .ac-testimonial-text p {
        max-width: 100% !important;
        word-wrap: break-word !important;
        overflow-wrap: break-word !important;
        hyphens: auto !important;
        padding: 0 !important;
    }
    
    /* About page containers - remove extra padding */
    .ac-about-hero-container,
    .ac-team-container,
    .ac-values-container,
    .ac-testimonial-container {
        padding: 0 !important;
        margin: 0 !important;
        width: 100% !important;
        max-width: 100% !important;
    }
    
    /* About hero specific adjustments */
    .ac-about-hero h1 {
        font-size: 2rem !important;
        line-height: 1.2 !important;
        margin-bottom: 1.5rem !important;
    }
    
    .ac-about-hero-subtitle {
        font-size: 1.125rem !important;
        line-height: 1.5 !important;
        margin-bottom: 2rem !important;
    }
    
    /* About page metrics */
    .ac-metrics {
        display: grid !important;
        grid-template-columns: 1fr !important;
        gap: 1rem !important;
        margin-top: 2rem !important;
    }
    
    .ac-metric {
        text-align: center !important;
        padding: 1rem !important;
        background: white !important;
        border-radius: 8px !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
    }
    
    .ac-metric-value {
        font-size: 1.5rem !important;
        font-weight: 700 !important;
        color: var(--ac-grove) !important;
    }
    
    .ac-metric-label {
        font-size: 0.875rem !important;
        color: var(--ac-text-secondary) !important;
    }
    
    /* Fix grid layouts on About page */
    .ac-mission-grid,
    .ac-founders-grid,
    .ac-values-grid {
        grid-template-columns: 1fr !important;
        gap: 1.5rem !important;
    }
    
    /* Fix Mission Section - Complete mobile responsive solution */
    .ac-mission-section {
        padding: 2.5rem 1.5rem !important; /* Match other sections */
        overflow: visible !important;
        width: 100% !important;
        box-sizing: border-box !important;
    }
    
    .ac-mission-section * {
        box-sizing: border-box !important;
    }
    
    .ac-mission-container {
        max-width: 100% !important;
        width: 100% !important;
        margin: 0 !important;
        padding: 0 !important; /* Remove container padding since section has it */
    }
    
    .ac-mission-grid {
        margin-top: 1.5rem !important;
        width: 100% !important;
        display: grid !important;
        grid-template-columns: 1fr !important;
        gap: 2rem !important;
    }
    
    .ac-mission-content {
        width: 100% !important;
        max-width: 100% !important;
        padding: 0 !important;
    }
    
    .ac-mission-content h2 {
        font-size: 1.75rem !important;
        margin: 0 0 1rem 0 !important;
        line-height: 1.2 !important;
        word-wrap: break-word !important;
    }
    
    .ac-mission-content p {
        margin: 0 0 1rem 0 !important;
        width: 100% !important;
        font-size: 1rem !important;
        line-height: 1.6 !important;
        word-wrap: break-word !important;
        overflow-wrap: break-word !important;
    }
    
    .ac-mission-stats {
        display: grid !important;
        grid-template-columns: 1fr 1fr !important;
        gap: 0.75rem !important;
        width: 100% !important;
        margin: 1.5rem 0 0 0 !important;
    }
    
    .ac-mission-stat {
        padding: 1rem 0.5rem !important;
        text-align: center !important;
        width: 100% !important;
        background: white !important;
        border-radius: 8px !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
    }
    
    .ac-mission-stat-value {
        font-size: 1.5rem !important;
        font-weight: 700 !important;
        color: var(--ac-grove) !important;
        margin-bottom: 0.25rem !important;
    }
    
    .ac-mission-stat-label {
        font-size: 0.75rem !important;
        color: var(--ac-text-secondary) !important;
        line-height: 1.2 !important;
    }
    
    /* Hide the mission visual on mobile to save space */
    .ac-mission-visual {
        display: none !important;
    }
    
    /* Fix founder cards */
    .ac-founder-card {
        max-width: 100% !important;
        margin: 0 auto 1.5rem !important;
        background: white !important;
        border-radius: 8px !important;
        overflow: hidden !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
    }
    
    .ac-founder-image-wrapper {
        height: 200px !important;
        overflow: hidden !important;
    }
    
    .ac-founder-image {
        width: 100% !important;
        height: 100% !important;
        object-fit: cover !important;
    }
    
    .ac-founder-info {
        padding: 1.5rem !important;
        text-align: center !important;
    }
    
    .ac-founder-name {
        font-size: 1.25rem !important;
        font-weight: 700 !important;
        margin-bottom: 0.5rem !important;
    }
    
    .ac-founder-role {
        font-size: 1rem !important;
        color: var(--ac-text-secondary) !important;
    }
    
    /* Values cards mobile optimization */
    .ac-values-grid {
        grid-template-columns: 1fr !important;
        gap: 1.5rem !important;
    }
    
    .ac-value-card {
        padding: 1.5rem !important;
        margin: 0 !important;
    }
    
    .ac-value-icon {
        width: 48px !important;
        height: 48px !important;
        font-size: 1.5rem !important;
        margin-bottom: 1rem !important;
    }
    
    .ac-value-title {
        font-size: 1.125rem !important;
        margin-bottom: 0.5rem !important;
    }
    
    .ac-value-desc {
        font-size: 0.875rem !important;
        line-height: 1.5 !important;
    }
    
    /* Testimonial section mobile optimization */
    .ac-testimonial-content {
        grid-template-columns: 1fr !important;
        gap: 2rem !important;
    }
    
    .ac-testimonial-text {
        padding: 1.5rem !important;
        margin: 0 !important;
    }
    
    .ac-testimonial-text h2 {
        font-size: 1.5rem !important;
        text-align: left !important;
        margin-bottom: 1rem !important;
    }
    
    .ac-company-logo-standalone {
        max-width: 150px !important;
        height: auto !important;
        margin-bottom: 1rem !important;
    }
    
    /* Hide video showcase on mobile */
    .ac-video-showcase {
        display: none !important;
    }
    
    /* Hero gets minimal padding and sits right below nav */
    .ac-hero {
        padding-top: 60px !important; /* Nav height */
        margin-top: 0 !important;
    }
    
    /* Pre-registration page specific - match homepage hero spacing */
    .pre-reg-hero {
        padding: 60px 0 1rem !important; /* Same as homepage hero */
        margin: 0 !important;
        min-height: auto !important;
    }
    
    /* Remove any extra padding from pre-reg containers */
    .pre-reg-hero .hero-content,
    .pre-reg-hero .container {
        padding-top: 0 !important;
        margin-top: 0 !important;
    }
    
    /* Hide automation cards on mobile pre-registration page */
    .automation-cards {
        display: none !important;
    }
    
    /* Hide comparison table on mobile pre-registration page */
    .ac-comparison-card {
        display: none !important;
    }
    
    /* Hide Agentic SEO for Shopify section on mobile */
    .ac-ai-brain-section {
        display: none !important;
    }
    
    /* Pre-registration hero padding - ensure extra space below form */
    .pre-reg-hero {
        padding-bottom: 5rem !important; /* Good spacing on mobile */
    }
    
    /* Hide specific pre-reg elements on mobile */
    /* 1. Hide green text below CTA button */
    .pre-reg-hero .cta-subtext,
    .pre-reg-hero .green-text,
    .pre-reg-hero p[style*="color: #22c55e"],
    .pre-reg-hero p[style*="color: var(--grove-green)"] {
        display: none !important;
    }
    
    /* 2. Hide the toggle card/automation graphic */
    .pre-reg-hero .ac-hero-dashboard,
    .pre-reg-hero .automation-preview,
    .pre-reg-hero .toggle-card,
    .pre-reg-hero .ac-browser-window,
    .pre-reg-hero .ac-toggle-grid,
    .pre-reg-hero .ac-hero-visual {
        display: none !important;
    }
    
    /* Hide homepage-specific elements on mobile */
    /* 1. Hide "Get access to world's first" text and arrows */
    .ac-shopify-hint {
        display: none !important;
    }
    
    /* 2. Hide the entire dashboard mockup on homepage */
    .ac-hero .ac-hero-visual {
        display: none !important;
    }
    
    /* Remove the red/pink radial gradient overlay on hero section */
    .ac-hero::before {
        display: none !important;
    }
    
    /* Standardized margins between elements */
    .ac-section-header,
    .ac-features-header {
        margin-bottom: 2rem !important;
        text-align: center !important;
    }
    
    /* Standardized card spacing */
    .ac-feature-card,
    .ac-pricing-card,
    .ac-testimonial-card,
    .ac-opp-card {
        margin-bottom: 1rem !important;
    }
    
    /* Remove last child margins */
    .ac-feature-card:last-child,
    .ac-pricing-card:last-child,
    .ac-testimonial-card:last-child,
    .ac-opp-card:last-child {
        margin-bottom: 0 !important;
    }
    
    /* ===================================
       Hero Section Mobile Optimization
       =================================== */
    
    /* Hero Layout - Tight spacing */
    section.ac-hero {
        padding: 60px 0 1rem !important; /* Just nav height + small bottom padding */
        margin: 0 !important;
        min-height: auto !important;
    }
    
    .ac-hero-content {
        text-align: center !important;
        max-width: 100% !important;
        margin: 0 auto !important;
        order: -1 !important; /* Move text content first */
        padding-top: 0 !important; /* Override inline style */
    }
    
    /* Move hero visual after content on mobile */
    .ac-hero-container {
        display: flex !important;
        flex-direction: column !important;
        padding-top: 0 !important;
        margin-top: 0 !important;
    }
    
    /* Remove any grid padding */
    .ac-hero-grid {
        padding-top: 0 !important;
        margin-top: 0 !important;
    }
    
    .ac-hero h1 {
        font-size: 2rem !important;
        margin-bottom: 1rem !important;
        padding: 0 1.5rem !important;
    }
    
    .ac-hero-subtitle {
        font-size: 1.125rem !important;
        margin-bottom: 2rem !important;
        padding: 0 1.5rem !important;
    }
    
    /* Hero CTA Buttons */
    .ac-hero-buttons {
        flex-direction: column !important;
        gap: 1rem !important;
        width: 100% !important;
        padding: 0 1.5rem !important;
    }
    
    .ac-btn-primary,
    .ac-btn-secondary {
        width: 100% !important;
        min-height: 48px !important;
        font-size: 1rem !important;
        padding: 0.875rem 1.5rem !important;
    }
    
    /* Simplify CTA input */
    .ac-cta-input-group,
    .ac-hero-input-group,
    .ac-url-input-group {
        flex-direction: column !important;
        gap: 0.75rem !important;
        width: 100% !important;
        max-width: 100% !important;
    }
    
    .ac-cta-input,
    .ac-hero-input,
    .ac-url-input,
    input[type="url"] {
        width: 100% !important;
        font-size: 16px !important;
        padding: 0.875rem 1rem !important;
        min-height: 48px !important;
    }
    
    .ac-cta-input::placeholder,
    .ac-hero-input::placeholder,
    .ac-url-input::placeholder {
        font-size: 0.875rem !important;
    }
    
    /* Keep original placeholder text */
    
    /* Hide blobs on mobile */
    .ac-blob,
    .ac-hero-blob,
    .morph-blob,
    [class*="blob"] {
        display: none !important;
    }
    
    /* Hero Dashboard - Simplified for Mobile */
    .ac-hero-visual {
        margin-top: 3rem !important;
    }
    
    .ac-hero-dashboard {
        display: block !important; /* Keep it visible but simplify */
        transform: none !important;
        max-width: 100% !important;
        margin: 0 !important;
    }
    
    .ac-browser-window {
        border-radius: 8px !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
    }
    
    .ac-browser-bar {
        padding: 0.625rem !important;
        height: auto !important;
    }
    
    .ac-browser-dots {
        gap: 0.375rem !important;
    }
    
    .ac-browser-dot {
        width: 8px !important;
        height: 8px !important;
    }
    
    .ac-browser-url {
        font-size: 0.75rem !important;
        padding: 0.25rem 0.75rem !important;
    }
    
    .ac-browser-content {
        padding: 1rem !important;
    }
    
    .ac-dashboard-header {
        margin-bottom: 1rem !important;
        padding-bottom: 0.75rem !important;
    }
    
    .ac-dashboard-title {
        font-size: 1rem !important;
    }
    
    .ac-dashboard-subtitle {
        font-size: 0.75rem !important;
    }
    
    .ac-toggle-grid {
        gap: 0.75rem !important;
    }
    
    .ac-toggle-card {
        padding: 0.875rem !important;
        min-height: auto !important;
    }
    
    .ac-toggle-header {
        margin-bottom: 0.5rem !important;
        gap: 0.5rem !important;
    }
    
    .ac-toggle-icon {
        width: 24px !important;
        height: 24px !important;
        font-size: 0.875rem !important;
    }
    
    .ac-toggle-info h4 {
        font-size: 0.875rem !important;
        margin-bottom: 0.125rem !important;
    }
    
    .ac-toggle-info p {
        font-size: 0.625rem !important;
    }
    
    .ac-toggle-switch {
        width: 36px !important;
        height: 20px !important;
    }
    
    .ac-toggle-handle {
        width: 14px !important;
        height: 14px !important;
    }
    
    /* ===================================
       Features Section Mobile
       =================================== */
    
    .ac-features {
        padding: 3rem 0 !important;
    }
    
    .ac-features-grid {
        grid-template-columns: 1fr !important;
        gap: 2rem !important;
        padding: 0 !important;
    }
    
    .ac-feature-card {
        padding: 1.5rem !important;
        text-align: center !important;
    }
    
    .ac-feature-icon {
        margin: 0 auto 1rem !important;
    }
    
    .ac-feature-title {
        font-size: 1.25rem !important;
        margin-bottom: 0.75rem !important;
    }
    
    .ac-feature-description {
        font-size: 0.875rem !important;
    }
    
    /* ===================================
       Pricing Section Mobile
       =================================== */
    
    /* Two column pricing layout */
    .ac-pricing-layout {
        grid-template-columns: 1fr !important;
        gap: 2rem !important;
        padding: 0 !important;
    }
    
    /* Founder's note box */
    .ac-waitlist-box {
        margin-bottom: 2rem !important;
        padding: 1.5rem !important;
    }
    
    .ac-waitlist-box h3 {
        font-size: 1.125rem !important;
    }
    
    .ac-waitlist-box p {
        font-size: 0.875rem !important;
        line-height: 1.5 !important;
    }
    
    /* Benefits icons grid */
    .ac-waitlist-box > div > div[style*="grid-template-columns: repeat(3, 1fr)"] {
        gap: 0.5rem !important;
        padding: 1rem 0 !important;
    }
    
    .ac-waitlist-box > div > div[style*="grid-template-columns: repeat(3, 1fr)"] > div {
        padding: 0 0.25rem !important;
    }
    
    .ac-waitlist-box > div > div[style*="grid-template-columns: repeat(3, 1fr)"] p {
        font-size: 0.625rem !important;
    }
    
    /* Founder signature */
    .ac-waitlist-box img[alt="Hamish"] {
        width: 40px !important;
        height: 40px !important;
        object-fit: cover !important;
    }
    
    .ac-pricing-grid {
        grid-template-columns: 1fr !important;
        gap: 1.5rem !important;
        padding: 0 !important;
        margin: 0 !important;
    }
    
    .ac-pricing-card {
        width: 100% !important;
        max-width: 100% !important;
        margin: 0 auto !important;
        padding: 1.5rem !important;
    }
    
    /* Price display */
    .ac-pricing-price {
        flex-wrap: wrap !important;
        justify-content: center !important;
        gap: 0.5rem !important;
        margin-bottom: 1rem !important;
    }
    
    .ac-pricing-price > span[style*="font-size: 2.5rem"] {
        font-size: 1.75rem !important;
        display: block !important;
        width: 100% !important;
        text-align: center !important;
        margin: 0 0 0.5rem 0 !important;
    }
    
    .ac-price-amount {
        font-size: 2.5rem !important;
    }
    
    /* ROI Calculator */
    .ac-roi-calculator {
        margin-top: 3rem !important;
        padding: 1.5rem !important;
    }
    
    .ac-roi-calculator h3 {
        font-size: 1.5rem !important;
    }
    
    .ac-roi-subtitle {
        font-size: 0.875rem !important;
    }
    
    /* ROI breakdown items */
    .ac-roi-breakdown {
        gap: 1rem !important;
    }
    
    .ac-roi-item {
        padding: 1rem !important;
    }
    
    .ac-roi-item-header {
        font-size: 0.875rem !important;
    }
    
    .ac-roi-item-value {
        font-size: 1.25rem !important;
    }
    
    .ac-roi-item-detail {
        font-size: 0.75rem !important;
    }
    
    /* ROI total */
    .ac-roi-total {
        padding: 1.5rem !important;
        margin-top: 1.5rem !important;
    }
    
    .ac-roi-total-value {
        font-size: 2rem !important;
    }
    
    .ac-roi-annual {
        font-size: 0.875rem !important;
    }
    
    /* Pricing badge text */
    .ac-pricing-badge {
        font-size: 0.75rem !important;
        padding: 0.375rem 0.75rem !important;
    }
    
    /* Remove bold text from features on mobile */
    .ac-pricing-features li strong {
        font-weight: normal !important;
        display: none !important;
    }
    
    .ac-pricing-features li {
        font-size: 0.875rem !important;
    }
    
    /* ===================================
       Footer Mobile
       =================================== */
    
    /* Hide all footer menu columns on mobile */
    .footer-columns {
        display: none !important;
    }
    
    /* Make footer logo smaller */
    .footer-brand img {
        max-width: 120px !important;
        height: auto !important;
    }
    
    /* Center footer brand content */
    .footer-brand {
        text-align: center !important;
        margin: 0 auto !important;
        max-width: 300px !important;
    }
    
    .footer-brand p {
        font-size: 0.875rem !important;
        margin-top: 1rem !important;
    }
    
    /* Footer container adjustments */
    .footer-container {
        padding: 2rem 1rem !important;
    }
    
    /* Footer bottom section */
    .footer-bottom {
        flex-direction: column !important;
        gap: 1rem !important;
        text-align: center !important;
        padding: 1.5rem 1rem !important;
    }
    
    .footer-links {
        flex-direction: column !important;
        gap: 0.5rem !important;
    }
    
    /* ===================================
       Pre-Registration Page Mobile
       =================================== */
    
    /* Hide "Why Stores Are Firing Their Agencies" comparison table on mobile */
    .pre-reg-hero ~ * .ac-comparison-card {
        display: none !important;
    }
    
    /* ===================================
       CTA Section Mobile
       =================================== */
    
    .ac-cta {
        padding: 3rem 0 !important;
    }
    
    .ac-cta-title {
        font-size: 1.75rem !important;
        margin-bottom: 1rem !important;
        padding: 0 1.5rem !important;
    }
    
    .ac-cta-subtitle {
        font-size: 1rem !important;
        margin-bottom: 2rem !important;
        padding: 0 1.5rem !important;
    }
    
    .ac-cta-buttons {
        flex-direction: column !important;
        width: 100% !important;
        padding: 0 1.5rem !important;
    }
    
    .ac-cta-stats {
        flex-direction: column !important;
        gap: 1rem !important;
        margin-top: 2rem !important;
    }
    
    .ac-cta-stat {
        text-align: center !important;
    }
    
    /* ===================================
       Testimonials Mobile
       =================================== */
    
    .ac-testimonials-grid {
        grid-template-columns: 1fr !important;
        gap: 1.5rem !important;
    }
    
    .ac-testimonial-card {
        padding: 1.5rem !important;
    }
    
    /* ===================================
       Navigation Mobile Enhancements
       =================================== */
    
    /* Ensure mobile menu works properly */
    .ac-nav-menu {
        display: none !important;
    }
    
    .ac-mobile-toggle {
        display: flex !important;
    }
    
    /* Mobile menu overlay improvements */
    .ac-mobile-menu {
        padding-top: 80px !important;
    }
    
    .ac-mobile-item {
        padding: 1rem 1.5rem !important;
        font-size: 1.125rem !important;
    }
    
    /* ===================================
       Form Elements Mobile
       =================================== */
    
    input[type="text"],
    input[type="email"],
    input[type="tel"],
    textarea,
    select {
        font-size: 16px !important; /* Prevents zoom on iOS */
        min-height: 48px !important;
        padding: 0.75rem 1rem !important;
    }
    
    /* ===================================
       Utility Classes Mobile
       =================================== */
    
    /* Hide on mobile */
    .desktop-only {
        display: none !important;
    }
    
    /* Stack elements vertically */
    .mobile-stack {
        flex-direction: column !important;
        align-items: stretch !important;
    }
    
    /* Full width on mobile */
    .mobile-full-width {
        width: 100% !important;
        max-width: 100% !important;
    }
    
    /* Responsive spacing */
    .mobile-spacing {
        padding: 1rem !important;
        margin: 1rem 0 !important;
    }
}

/* ===================================
   Tablet Styles (768px - 1023px)
   =================================== */
@media (min-width: 768px) and (max-width: 1023px) {
    
    /* Container widths for tablets */
    .ac-container,
    .ac-features-container,
    .ac-hero-container,
    .ac-cta-container {
        max-width: 720px !important;
        padding: 0 2rem !important;
    }
    
    /* Hero adjustments for tablets */
    .ac-hero {
        padding: 7rem 0 5rem !important;
    }
    
    .ac-hero h1 {
        font-size: 2.5rem !important;
    }
    
    /* Two-column grids on tablets */
    .ac-features-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 2rem !important;
    }
    
    .ac-pricing-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 2rem !important;
    }
    
    /* Keep dashboard hidden on tablets too */
    .ac-hero-dashboard {
        display: none !important;
    }
    
    /* Tablet-specific dashboard preview */
    .ac-hero::after {
        content: '';
        display: block;
        width: 100%;
        height: 400px;
        background: url('../images/dashboard-tablet-preview.html') no-repeat center;
        background-size: contain;
        margin-top: 3rem;
    }
}

/* ===================================
   Touch-Friendly Enhancements
   =================================== */
@media (hover: none) and (pointer: coarse) {
    /* Remove hover effects on touch devices */
    .ac-btn-primary:hover,
    .ac-btn-secondary:hover,
    .ac-feature-card:hover,
    .ac-pricing-card:hover {
        transform: none !important;
        box-shadow: none !important;
    }
    
    /* Increase tap targets */
    a, button {
        min-height: 44px;
        min-width: 44px;
    }
    
    /* Add touch feedback */
    a:active,
    button:active {
        opacity: 0.8;
        transform: scale(0.98);
    }
}

/* ===================================
   Performance Optimizations
   =================================== */
@media (max-width: 1023px) {
    /* Disable complex animations on mobile/tablet */
    * {
        animation-duration: 0.3s !important;
        transition-duration: 0.3s !important;
    }
    
    /* Reduce motion for performance */
    @media (prefers-reduced-motion: reduce) {
        * {
            animation: none !important;
            transition: none !important;
        }
    }
}

/* ===================================
   Mobile-Specific Homepage Components
   =================================== */
@media (max-width: 767px) {
    /* Social Proof Bar */
    .ac-social-proof {
        flex-direction: column !important;
        text-align: center !important;
        gap: 1.5rem !important;
        padding: 2rem 1rem !important;
    }
    
    .ac-social-proof-item {
        width: 100% !important;
    }
    
    /* Stats Section */
    .ac-stats-grid {
        grid-template-columns: 1fr !important;
        gap: 1.5rem !important;
    }
    
    .ac-stat-card {
        padding: 1.5rem !important;
        text-align: center !important;
    }
    
    /* Trust Badges */
    .ac-trust-badges {
        flex-wrap: wrap !important;
        justify-content: center !important;
        gap: 1rem !important;
    }
    
    .ac-trust-badge {
        width: calc(50% - 0.5rem) !important;
        min-width: 120px !important;
    }
    
    /* Analysis Popup Mobile */
    .ac-analysis-popup {
        padding: 1rem !important;
    }
    
    .ac-analysis-popup-content {
        width: 95% !important;
        max-width: 100% !important;
        max-height: 90vh !important;
        margin: 1rem auto !important;
        padding: 1.5rem !important;
    }
    
    .ac-popup-close {
        top: 0.5rem !important;
        right: 0.5rem !important;
    }
    
    /* Competitor Comparison Table */
    .ac-comparison-table {
        font-size: 0.875rem !important;
        overflow-x: auto !important;
    }
    
    .ac-comparison-row {
        display: grid !important;
        grid-template-columns: 2fr 1fr 1fr 1fr !important;
        gap: 0.5rem !important;
        padding: 0.5rem !important;
    }
    
    /* Automation Section Mobile */
    .ac-automation-section {
        padding: 3rem 0 !important;
        background: var(--ac-bg-light) !important; /* Keep light theme */
        color: var(--ac-text-primary) !important;
    }
    
    .ac-ai-sparkle {
        display: none !important; /* Hide decorative elements on mobile */
    }
    
    .ac-ai-console-rounded {
        padding: 0 !important;
        margin: 1rem 0 !important;
        background: white !important; /* Light background */
        border: 1px solid #e5e7eb !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
    }
    
    .ac-console-header-rounded {
        padding: 1rem !important;
        font-size: 0.875rem !important;
        background: var(--ac-bg-light) !important;
        border-bottom: 1px solid #e5e7eb !important;
    }
    
    .ac-console-dots {
        gap: 0.375rem !important;
        display: flex !important; /* Ensure dots container is visible */
    }
    
    .ac-dot {
        width: 10px !important;
        height: 10px !important;
        display: block !important; /* Ensure each dot is visible */
        border-radius: 50% !important;
    }
    
    /* Ensure dot colors are visible */
    .ac-dot-red {
        background-color: #ff5f57 !important;
    }
    
    .ac-dot-yellow {
        background-color: #ffbd2e !important;
    }
    
    .ac-dot-green {
        background-color: #28ca42 !important;
    }
    
    .ac-console-title {
        display: none !important; /* Hide grove.ai/automations on mobile */
    }
    
    .ac-console-status {
        font-size: 0.625rem !important;
        gap: 0.25rem !important;
        color: var(--ac-text-secondary) !important;
    }
    
    .ac-ai-grid-rounded {
        padding: 1rem !important;
        gap: 1rem !important;
    }
    
    .ac-ai-agent-card-rounded {
        padding: 1rem !important;
        margin-bottom: 0 !important;
        background: white !important;
        border: 1px solid #e5e7eb !important;
        color: var(--ac-text-primary) !important;
    }
    
    .ac-agent-header {
        gap: 0.75rem !important;
        margin-bottom: 1rem !important;
    }
    
    .ac-agent-avatar-rounded {
        width: 40px !important;
        height: 40px !important;
        font-size: 1.125rem !important;
    }
    
    .ac-agent-name {
        font-size: 1rem !important;
    }
    
    .ac-automation-features {
        gap: 0.625rem !important;
    }
    
    .ac-automation-feature {
        padding: 0.5rem 0 !important;
        gap: 0.625rem !important;
    }
    
    /* Hide all automation sub-features on mobile to make cards compact */
    .ac-automation-features {
        display: none !important;
    }
    
    .ac-automation-feature-icon {
        width: 24px !important;
        height: 24px !important;
        font-size: 0.75rem !important;
    }
    
    .ac-automation-feature-text {
        font-size: 0.813rem !important;
    }
    
    /* Remove this duplicate - handled below */
    
    /* Toggle Cards Mobile */
    .ac-toggle-grid {
        grid-template-columns: 1fr !important;
        gap: 1rem !important;
    }
    
    .ac-toggle-card {
        padding: 1rem !important;
    }
    
    /* Mobile Hero Visual */
    .desktop-only {
        display: none !important;
    }
    
    .mobile-only {
        display: block !important;
    }
    
    /* Logo Slider Mobile */
    .ac-logos-section {
        padding: 2rem 0 !important;
        overflow: hidden !important;
    }
    
    .ac-logos-title {
        font-size: 0.875rem !important;
        margin-bottom: 1rem !important;
    }
    
    .ac-logos-slider {
        animation: none !important; /* Disable for now, will be overridden by specific animation */
    }
    
    .ac-logo-item {
        padding: 0 0.5rem !important;
    }
    
    .ac-logo-item img {
        height: 60px !important; /* 24px * 2.5 = 60px */
        width: auto !important;
        opacity: 0.8 !important;
        object-fit: contain !important;
    }
    
    /* Opportunity Cards Mobile */
    .ac-opportunity-section {
        padding: 3rem 0 !important;
    }
    
    /* Hide Watch Your Traffic Explode on Autopilot section on mobile */
    .ac-results-section {
        display: none !important;
    }
    
    .ac-opp-grid {
        grid-template-columns: 1fr !important;
        gap: 1rem !important;
    }
    
    .ac-opp-card {
        padding: 1.5rem !important;
    }
    
    /* Dashboard Preview Mobile */
    .ac-browser-window {
        border-radius: 8px !important;
    }
    
    .ac-browser-bar {
        padding: 0.5rem !important;
    }
    
    .ac-browser-content {
        padding: 1rem !important;
    }
    
    /* Mobile Navigation Improvements */
    .ac-nav {
        height: 60px !important;
    }
    
    .ac-nav-container {
        height: 60px !important;
        padding: 0 1.5rem !important;
    }
    
    /* Logo adjustments for mobile */
    .ac-nav .ac-logo {
        font-size: 1rem !important;
        display: flex !important;
        align-items: center !important;
        height: 100% !important;
    }
    
    /* Force small logo size on mobile - target by class */
    .ac-nav .ac-logo-img {
        height: 32px !important;
        width: auto !important;
        max-width: 100px !important;
        object-fit: contain !important;
        display: block !important;
    }

/* ===================================
   Critical Mobile Fixes
   =================================== */
@media (max-width: 1023px) {
    /* Prevent horizontal scroll */
    html, body {
        overflow-x: hidden !important;
        width: 100% !important;
    }
    
    /* Fix any elements that might cause overflow */
    * {
        max-width: 100vw !important;
    }
    
    /* Ensure images don't break layout */
    img {
        max-width: 100% !important;
        height: auto !important;
    }
    
    /* Fix pre/code blocks */
    pre, code {
        overflow-x: auto !important;
        max-width: 100% !important;
        word-wrap: break-word !important;
    }
}

/* ===================================
   Mobile Animation Simplifications
   =================================== */
@media (max-width: 767px) {
    /* Disable decorative animations and sparkles */
    .ac-ai-sparkle,
    .ac-sparkle-1,
    .ac-sparkle-2,
    .ac-sparkle-3,
    .ac-sparkle-4,
    .ac-sparkle-5,
    .ac-sparkle-6,
    [class*="sparkle"],
    .sparkle {
        display: none !important;
    }
    
    /* Remove/simplify AI Automation Center */
    .ac-ai-automation-center,
    .ac-automation-center-animation {
        display: none !important;
    }
    
    /* Simple replacement for AI Automation Center */
    @media (max-width: 767px) {
        .ac-hero-dashboard-wrapper::after {
            content: '';
            display: block;
            background: var(--ac-bg-light);
            border: 2px solid var(--ac-grove);
            border-radius: 12px;
            padding: 2rem 1rem;
            margin-top: 2rem;
            text-align: center;
        }
        
        .ac-hero-dashboard-wrapper::after {
            content: 'AI Automation Dashboard';
            font-size: 1rem;
            font-weight: 600;
            color: var(--ac-text-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            height: 120px;
            background: linear-gradient(135deg, rgba(34, 197, 94, 0.05) 0%, rgba(34, 197, 94, 0.1) 100%);
        }
    }
    
    /* Simplify hero dashboard animations */
    .ac-hero-feed-item {
        animation: mobileFadeIn 0.3s ease-out !important;
    }
    
    .ac-hero-toggle-slider {
        transition: background-color 0.2s ease !important;
    }
    
    .ac-hero-toggle-handle {
        transition: transform 0.2s ease !important;
    }
    
    .ac-hero-metric {
        animation: none !important;
    }
    
    /* Simplify AI agent card animations */
    .ac-agent-status-ring-rounded,
    .ac-agent-status-ring {
        animation: mobilePulse 3s ease-in-out infinite !important;
    }
    
    .ac-automation-feature {
        animation: mobileFadeIn 0.3s ease-out !important;
        animation-fill-mode: both !important;
    }
    
    .ac-automation-feature:nth-child(1) { animation-delay: 0.1s !important; }
    .ac-automation-feature:nth-child(2) { animation-delay: 0.15s !important; }
    .ac-automation-feature:nth-child(3) { animation-delay: 0.2s !important; }
    .ac-automation-feature:nth-child(4) { animation-delay: 0.25s !important; }
    
    .ac-agent-live-strip {
        animation: none !important;
    }
    
    .ac-live-indicator {
        animation: mobilePulse 2s ease-in-out infinite !important;
    }
    
    /* Simplify notification animations */
    .ac-notification {
        animation: mobileFadeUp 0.3s ease-out !important;
    }
    
    /* Remove brain container but keep brain icon in badges */
    .ac-brain-container {
        display: none !important;
    }
    
    /* Allow brain icon in badges */
    .ac-badge .fa-brain,
    .ac-badge-icon.fa-brain {
        display: inline-block !important;
    }
    
    /* Keep original icons on mobile */
    .ac-agent-avatar-rounded i {
        display: flex !important;
        animation: none !important;
    }
    
    /* Disable metric counting animations */
    .ac-metric-value {
        animation: none !important;
    }
    
    /* Simplify toggle animations */
    .ac-toggle-slider-rounded {
        transition: all 0.2s ease !important;
    }
    
    /* Logo slider - make it simpler */
    .ac-logos-slider {
        animation: mobileSlide 20s linear infinite !important;
    }
}

/* Mobile-specific simplified animations */
@keyframes mobileFadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes mobileFadeUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes mobilePulse {
    0%, 100% {
        opacity: 0.6;
    }
    50% {
        opacity: 1;
    }
}

@keyframes mobileSlide {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-33.33%);
    }
}

/* Performance optimizations for mobile */
@media (max-width: 767px) {
    /* Reduce complexity of all animations */
    * {
        animation-timing-function: ease-out !important;
    }
    
    /* Disable box-shadow animations on hover */
    *:hover {
        transition: transform 0.2s ease, opacity 0.2s ease !important;
    }
    
    /* Keep functional shadows but remove animations */
    .ac-hero-toggle-card.active {
        box-shadow: 0 2px 8px rgba(34, 197, 94, 0.1) !important;
        border-color: var(--ac-grove) !important;
    }
    
    /* Simplify live feed animations */
    .ac-hero-feed {
        transition: opacity 0.3s ease, max-height 0.3s ease !important;
    }
    
    .ac-hero-feed.active {
        animation: none !important;
    }
    
    /* Ensure smooth scrolling */
    html {
        scroll-behavior: smooth;
        -webkit-overflow-scrolling: touch;
    }
    
    /* Optimize toggle switches */
    .ac-hero-toggle-switch input:checked + .ac-hero-toggle-slider {
        background-color: var(--ac-grove) !important;
    }
    
    .ac-hero-toggle-switch input:checked + .ac-hero-toggle-slider .ac-hero-toggle-handle {
        transform: translateX(20px) !important;
    }
    
    /* Fix notification positioning */
    .ac-notifications-dashboard {
        position: relative !important;
        transform: none !important;
    }
}

/* ===================================
   Additional Mobile Homepage Fixes
   =================================== */
@media (max-width: 767px) {
    /* Fix hero section spacing */
    section:first-of-type {
        padding-top: 80px !important; /* Account for fixed nav */
    }
    
    /* ROI Calculator Mobile Optimization */
    .ac-roi-calculator {
        padding: 1.5rem !important;
    }
    
    .ac-roi-header {
        margin-bottom: 1rem !important;
    }
    
    .ac-roi-title {
        font-size: 1.25rem !important;
    }
    
    .ac-roi-inputs {
        gap: 1rem !important;
        margin-bottom: 1rem !important;
    }
    
    .ac-roi-input-group {
        margin-bottom: 0.5rem !important;
    }
    
    .ac-roi-label {
        font-size: 0.875rem !important;
        margin-bottom: 0.25rem !important;
    }
    
    .ac-roi-slider {
        margin: 0.5rem 0 !important;
    }
    
    /* Move green total box right after slider */
    .ac-roi-total {
        order: -1 !important;
        padding: 1rem !important;
        margin: 0.75rem 0 1rem !important;
        background: var(--ac-grove) !important;
        color: white !important;
    }
    
    .ac-roi-total-value {
        font-size: 1.5rem !important;
        color: white !important;
    }
    
    .ac-roi-total-label {
        color: rgba(255, 255, 255, 0.9) !important;
    }
    
    .ac-roi-results {
        gap: 0.75rem !important;
        margin-bottom: 0.5rem !important;
        display: flex !important;
        flex-wrap: wrap !important;
    }
    
    .ac-roi-metric {
        padding: 0.75rem !important;
        flex: 1 1 48% !important;
        min-width: 140px !important;
    }
    
    .ac-roi-metric-value {
        font-size: 1rem !important;
    }
    
    .ac-roi-metric-label {
        font-size: 0.625rem !important;
    }
    
    /* Hide Watch Your Traffic Explode Section entirely on mobile */
    .ac-watch-traffic-section,
    .ac-traffic-explode-section,
    #watch-traffic-explode {
        display: none !important;
    }
    
    /* Hide AI Optimization Active pill */
    .ac-ai-badge,
    .ac-optimization-badge,
    .ac-traffic-badge {
        display: none !important;
    }
    
    /* Smooth scrolling on iOS */
    * {
        -webkit-overflow-scrolling: touch;
    }
    
    /* Better tap highlights */
    a, button {
        -webkit-tap-highlight-color: rgba(34, 197, 94, 0.1);
    }
    
    /* Improve readability */
    body {
        -webkit-text-size-adjust: 100%;
        text-size-adjust: 100%;
    }
    
    /* Better readability and prevent zoom */
    input, textarea, select {
        font-size: 16px !important;
    }
    
    /* Optimize animations for mobile */
    .ac-hero-feed-item,
    .ac-notification,
    .ac-toggle-card {
        animation-duration: 0.3s !important;
    }
    
    /* Disable parallax effects on mobile */
    [data-parallax],
    .parallax {
        transform: none !important;
        position: relative !important;
    }
    
    /* Fix z-index stacking issues */
    .ac-mobile-menu {
        z-index: 9999 !important;
    }
    
    .ac-analysis-popup {
        z-index: 10000 !important;
    }
    
    /* Ensure buttons are easily tappable */
    .ac-btn,
    button,
    [role="button"] {
        min-height: 44px !important;
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
    }
    
    /* Fix specific component issues */
    .ac-section {
        overflow: visible !important; /* Allow dropdowns to show */
    }
    
    /* Improve form elements */
    select {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23333' d='M10.293 3.293L6 7.586 1.707 3.293A1 1 0 00.293 4.707l5 5a1 1 0 001.414 0l5-5a1 1 0 10-1.414-1.414z'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: right 1rem center;
        padding-right: 2.5rem !important;
    }
    
    /* Ensure all text is properly colored in light sections */
    .ac-automation-section h2,
    .ac-automation-section h3,
    .ac-automation-section h4,
    .ac-automation-section p {
        color: var(--ac-text-primary) !important;
    }
    
    .ac-section-title {
        color: var(--ac-text-primary) !important;
        font-size: 1.75rem !important;
        line-height: 1.3 !important;
        margin-bottom: 0.75rem !important;
    }
    
    .ac-section-subtitle {
        color: var(--ac-text-secondary) !important;
        font-size: 1rem !important;
        line-height: 1.5 !important;
    }
    
    /* Final spacing standardization */
    .ac-grid,
    .ac-features-grid,
    .ac-pricing-grid,
    .ac-testimonials-grid {
        gap: 1rem !important;
    }
    
    /* Button spacing */
    .ac-btn + .ac-btn {
        margin-top: 0.75rem !important;
    }
    
    /* Consistent border radius */
    .ac-card,
    .ac-feature-card,
    .ac-pricing-card,
    .ac-ai-agent-card-rounded,
    .ac-browser-window {
        border-radius: 8px !important;
    }
    
    /* Fix any overflow issues */
    * {
        word-wrap: break-word !important;
        overflow-wrap: break-word !important;
    }
    
    /* Live strip styles for mobile automation */
    .ac-agent-live-strip {
        opacity: 0;
        transform: translateY(10px);
        transition: all 0.3s ease;
    }
    
    /* Fix automation card overflow on mobile - FINAL */
    .ac-ai-agent-card-rounded {
        overflow: hidden !important;
    }
    
    /* Hide features list on active state to make room */
    .ac-ai-agent-card-rounded.active .ac-automation-features {
        display: none !important;
    }
    
    /* Live strip for mobile - properly aligned */
    .ac-ai-agent-card-rounded .ac-agent-live-strip {
        /* Override all other styles */
        display: flex !important;
        align-items: center !important;
        gap: 0.5rem !important;
        padding: 0.75rem 1rem !important;
        background: linear-gradient(135deg, rgba(34, 197, 94, 0.05) 0%, rgba(34, 197, 94, 0.08) 100%) !important;
        border-top: 1px solid rgba(34, 197, 94, 0.1) !important;
        margin: 1rem -1rem -1rem -1rem !important; /* Adjusted for mobile padding of 1rem */
        border-radius: 0 0 12px 12px !important;
        font-size: 0.875rem !important;
        font-weight: 500 !important;
        color: var(--ac-text-primary) !important;
        opacity: 1 !important; /* Always visible on mobile when active */
        transform: translateY(0) !important; /* Override the translateY(10px) */
        transition: all 0.4s ease !important;
    }
    
    /* Ensure text is visible */
    .ac-ai-agent-card-rounded .ac-live-text {
        color: #059669 !important;
        font-weight: 500 !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        flex: 1 !important;
    }
    
    /* Features Section - Why Shopify Stores Are Firing Their SEO Agencies */
    .ac-features {
        display: block !important;
        padding: 3rem 0 !important;
    }
    
    .ac-features-grid {
        display: grid !important;
        grid-template-columns: 1fr !important;
        gap: 1rem !important;
        margin-top: 2rem !important;
    }
    
    .ac-feature-card {
        display: block !important;
        padding: 1.5rem !important;
        background: white !important;
        border: 1px solid #e5e7eb !important;
        border-radius: 8px !important;
        text-align: left !important;
    }
    
    .ac-feature-icon {
        width: 40px !important;
        height: 40px !important;
        margin-bottom: 1rem !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        background: rgba(239, 43, 112, 0.1) !important;
        border-radius: 8px !important;
        color: var(--ac-primary) !important;
    }
    
    .ac-feature-icon i {
        font-size: 1.25rem !important;
    }
    
    .ac-feature-title {
        font-size: 1.125rem !important;
        font-weight: 600 !important;
        margin-bottom: 0.5rem !important;
        color: var(--ac-text-primary) !important;
    }
    
    .ac-feature-description {
        font-size: 0.875rem !important;
        line-height: 1.5 !important;
        color: var(--ac-text-secondary) !important;
    }
    
    /* Comparison Section - Why We're Different */
    .ac-comparison-section {
        display: block !important;
        padding: 3rem 0 !important;
    }
    
    .ac-comparison-grid {
        display: grid !important;
        grid-template-columns: 1fr !important;
        gap: 1rem !important;
        margin-top: 2rem !important;
    }
    
    .ac-comparison-card {
        display: block !important;
        padding: 1.5rem !important;
        background: white !important;
        border: 2px solid #e5e7eb !important;
        border-radius: 12px !important;
        position: relative !important;
    }
    
    .ac-comparison-card.ac-comparison-bad {
        border-color: #fee2e2 !important;
        background: #fef2f2 !important;
    }
    
    .ac-comparison-card.ac-comparison-best {
        border-color: #dcfce7 !important;
        background: #f0fdf4 !important;
    }
    
    .ac-comparison-header {
        margin-bottom: 1rem !important;
    }
    
    .ac-comparison-header h3 {
        font-size: 1.25rem !important;
        font-weight: 600 !important;
        margin-bottom: 0.5rem !important;
        color: var(--ac-text-primary) !important;
    }
    
    .ac-comparison-price {
        font-size: 1rem !important;
        color: var(--ac-text-secondary) !important;
        font-weight: 500 !important;
    }
    
    .ac-comparison-verdict {
        display: flex !important;
        align-items: center !important;
        gap: 0.5rem !important;
        margin-bottom: 1rem !important;
        font-weight: 600 !important;
        font-size: 0.875rem !important;
    }
    
    .ac-comparison-verdict i {
        font-size: 1.25rem !important;
    }
    
    .ac-comparison-card.ac-comparison-bad .ac-comparison-verdict {
        color: #dc2626 !important;
    }
    
    .ac-comparison-card.ac-comparison-best .ac-comparison-verdict {
        color: #16a34a !important;
    }
    
    .ac-comparison-card:not(.ac-comparison-bad):not(.ac-comparison-best) .ac-comparison-verdict {
        color: #dc2626 !important;
    }
    
    .ac-comparison-list {
        list-style: none !important;
        padding: 0 !important;
        margin: 0 !important;
    }
    
    .ac-comparison-item {
        display: flex !important;
        align-items: flex-start !important;
        gap: 0.75rem !important;
        margin-bottom: 0.75rem !important;
        font-size: 0.875rem !important;
        line-height: 1.4 !important;
    }
    
    .ac-comparison-item i {
        flex-shrink: 0 !important;
        margin-top: 0.125rem !important;
        font-size: 1rem !important;
    }
    
    .ac-comparison-item.negative i {
        color: #dc2626 !important;
    }
    
    .ac-comparison-item.positive i {
        color: #16a34a !important;
    }
    
    .ac-comparison-cta {
        text-align: center !important;
        margin-top: 2rem !important;
    }
    
    .ac-comparison-cta p {
        margin-top: 0.75rem !important;
        font-size: 0.875rem !important;
        color: var(--ac-text-secondary) !important;
    }
    
    /* Pricing Section - Single Card Layout */
    .ac-pricing-grid {
        grid-template-columns: 1fr !important;
        max-width: 100% !important;
    }
    
    .ac-pricing-card {
        margin: 0 auto !important;
        max-width: 100% !important;
    }
    
    .ac-pricing-name {
        font-size: 1.25rem !important;
        display: flex !important;
        flex-wrap: wrap !important;
        align-items: center !important;
        justify-content: center !important;
        gap: 0.5rem !important;
    }
    
    .ac-pricing-name span {
        font-size: 0.625rem !important;
        padding: 0.2rem 0.5rem !important;
    }
    
    /* Waitlist benefits mobile */
    .ac-pricing-section > div:first-of-type > div {
        flex-direction: column !important;
        gap: 1rem !important;
    }
    
    /* Logo strip mobile */
    .ac-pricing-section > div:last-child img {
        height: 24px !important;
    }
    
    /* Pricing layout responsive */
    .ac-pricing-layout {
        grid-template-columns: 1fr !important;
        gap: 1.5rem !important;
    }
    
    /* Waitlist box mobile adjustments */
    .ac-waitlist-box {
        padding: 1.5rem !important;
    }
    
    .ac-waitlist-box h3 {
        font-size: 1.25rem !important;
    }
    
    .ac-waitlist-box > p {
        font-size: 0.875rem !important;
    }
    
    /* Stack benefits vertically on mobile */
    .ac-waitlist-box > div > div {
        gap: 0.75rem !important;
    }
    
    .ac-waitlist-box i {
        font-size: 1rem !important;
    }
}