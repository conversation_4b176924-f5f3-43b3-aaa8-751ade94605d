/* CLEAN UNIFORM THEME SYSTEM - NO MORE HACKS */

/* CSS Variables for consistent theming */
:root {
    /* Light theme colors (default) */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e9ecef;
    --text-primary: #212529;
    --text-secondary: #495057;
    --text-muted: #6c757d;
    --border-color: #dee2e6;
    --card-bg: #ffffff;
    --card-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --card-hover-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --input-bg: #ffffff;
    --input-border: #ced4da;
    --input-focus-border: #667eea;
    --table-bg: #ffffff;
    --table-header-bg: #f8f9fa;
    --table-hover-bg: #f8f9fa;
    --sidebar-bg: #ffffff;
    --sidebar-text: #212529;
    --sidebar-text-secondary: rgba(33, 37, 41, 0.7);
    --sidebar-hover: rgba(33, 37, 41, 0.1);
    --sidebar-active: rgba(33, 37, 41, 0.15);
    
    /* Override Bootstrap CSS variables */
    --bs-body-bg: #ffffff;
    --bs-body-color: #212529;
    --bs-card-bg: #ffffff;
    
    /* Status colors for light theme */
    --success-bg: #d1fae5;
    --info-bg: #dbeafe;
    --warning-bg: #fef3c7;
    --danger-bg: #fee2e2;
    --muted-bg: #f3f4f6;
    
    /* Activity icon colors */
    --activity-seo: #28a745;
    --activity-ads: #ffc107;
    --activity-email: #17a2b8;
    --activity-support: #dc3545;
    
    /* Additional backgrounds */
    --highlight-bg: #e0e7ff;
    --subtle-bg: #f9fafb;
    --border-subtle: #e5e7eb;
    --overlay-bg: rgba(255, 255, 255, 0.2);
}

/* Dark theme colors */
.theme-dark {
    --bg-primary: #212529;
    --bg-secondary: #343a40;
    --bg-tertiary: #495057;
    --text-primary: #f8f9fa;
    --text-secondary: #e9ecef;
    --text-muted: #adb5bd;
    --border-color: #495057;
    --card-bg: #343a40;
    --card-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.3);
    --card-hover-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.4);
    --input-bg: #495057;
    --input-border: #6c757d;
    --input-focus-border: #93bbfb;
    --table-bg: #343a40;
    --table-header-bg: #495057;
    --table-hover-bg: rgba(255, 255, 255, 0.05);
    --sidebar-bg: #212529;
    --sidebar-text: #ffffff;
    --sidebar-text-secondary: rgba(255, 255, 255, 0.7);
    --sidebar-hover: rgba(255, 255, 255, 0.1);
    --sidebar-active: rgba(255, 255, 255, 0.2);
    
    /* Override Bootstrap CSS variables for dark theme */
    --bs-body-bg: #212529;
    --bs-body-color: #f8f9fa;
    --bs-card-bg: #343a40;
    
    /* Status colors for dark theme */
    --success-bg: #064e3b;
    --info-bg: #1e3a8a;
    --warning-bg: #78350f;
    --danger-bg: #7f1d1d;
    --muted-bg: #374151;
    
    /* Activity icon colors (slightly adjusted for dark theme) */
    --activity-seo: #34d399;
    --activity-ads: #fbbf24;
    --activity-email: #22d3ee;
    --activity-support: #f87171;
    
    /* Additional backgrounds */
    --highlight-bg: #312e81;
    --subtle-bg: #1f2937;
    --border-subtle: #4b5563;
    --overlay-bg: rgba(0, 0, 0, 0.2);
}

/* Auto theme respects system preference */
@media (prefers-color-scheme: dark) {
    :root:not(.theme-light):not(.theme-dark) {
        --bg-primary: #212529;
        --bg-secondary: #343a40;
        --bg-tertiary: #495057;
        --text-primary: #f8f9fa;
        --text-secondary: #e9ecef;
        --text-muted: #adb5bd;
        --border-color: #495057;
        --card-bg: #343a40;
        --card-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.3);
        --card-hover-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.4);
        --input-bg: #495057;
        --input-border: #6c757d;
        --input-focus-border: #93bbfb;
        --table-bg: #343a40;
        --table-header-bg: #495057;
        --table-hover-bg: rgba(255, 255, 255, 0.05);
        --sidebar-bg: #212529;
        --sidebar-text: #ffffff;
        --sidebar-text-secondary: rgba(255, 255, 255, 0.7);
        --sidebar-hover: rgba(255, 255, 255, 0.1);
        --sidebar-active: rgba(255, 255, 255, 0.2);
        
        /* Override Bootstrap CSS variables for dark theme */
        --bs-body-bg: #212529;
        --bs-body-color: #f8f9fa;
        --bs-card-bg: #343a40;
        
        /* Status colors for dark theme */
        --success-bg: #064e3b;
        --info-bg: #1e3a8a;
        --warning-bg: #78350f;
        --danger-bg: #7f1d1d;
        --muted-bg: #374151;
        
        /* Activity icon colors (slightly adjusted for dark theme) */
        --activity-seo: #34d399;
        --activity-ads: #fbbf24;
        --activity-email: #22d3ee;
        --activity-support: #f87171;
        
        /* Additional backgrounds */
        --highlight-bg: #312e81;
        --subtle-bg: #1f2937;
        --border-subtle: #4b5563;
        --overlay-bg: rgba(0, 0, 0, 0.2);
    }
}

/* Base layout */
* {
    box-sizing: border-box;
}

body {
    min-height: 100vh;
    overflow-x: hidden;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Theme Toggle Button */
.theme-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    background: var(--card-bg);
    border: 2px solid var(--border-color);
    border-radius: 50px;
    padding: 8px 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--card-shadow);
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.theme-toggle:hover {
    transform: translateY(-2px);
    box-shadow: var(--card-hover-shadow);
}

.theme-toggle i {
    font-size: 16px;
}

/* Sidebar theme toggle specific */
.sidebar .theme-toggle {
    position: relative;
    top: auto;
    right: auto;
    margin-bottom: 20px;
    background: var(--sidebar-hover);
    border: 2px solid var(--sidebar-active);
    color: var(--sidebar-text);
    width: 100%;
    justify-content: center;
}

.sidebar .theme-toggle:hover {
    background: var(--sidebar-active);
    border-color: var(--sidebar-hover);
}

/* ALL TEXT ELEMENTS USE THEME VARIABLES */
h1, h2, h3, h4, h5, h6 {
    color: var(--text-primary) !important;
}

p, span, div, li, td, th {
    color: var(--text-primary);
}

.text-muted {
    color: var(--text-muted) !important;
}

.text-secondary {
    color: var(--text-secondary) !important;
}

small, .small {
    color: var(--text-muted);
}

/* Modern scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-tertiary);
}

::-webkit-scrollbar-thumb {
    background: var(--text-muted);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

/* Enhanced Sidebar */
.sidebar {
    width: 280px;
    min-height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 100;
    padding: 2rem 1rem;
    overflow-y: auto;
    background: var(--sidebar-bg);
    box-shadow: 4px 0 20px rgba(0,0,0,0.1);
}

.sidebar h4 {
    font-weight: 700;
    letter-spacing: -0.5px;
    margin-bottom: 0.5rem;
    color: var(--sidebar-text) !important;
}

.sidebar .nav-link {
    padding: 12px 20px;
    border-radius: 12px;
    transition: all 0.3s ease;
    color: var(--sidebar-text-secondary) !important;
    font-weight: 500;
    display: flex;
    align-items: center;
    margin-bottom: 4px;
}

.sidebar .nav-link:hover {
    background-color: var(--sidebar-hover);
    color: var(--sidebar-text) !important;
    transform: translateX(4px);
}

.sidebar .nav-link.active {
    background-color: var(--sidebar-active);
    color: var(--sidebar-text) !important;
    font-weight: 600;
}

.sidebar .nav-link i {
    width: 20px;
    text-align: center;
    margin-right: 12px;
}

.sidebar .badge {
    font-size: 0.7rem;
    padding: 4px 8px;
    border-radius: 20px;
    font-weight: 600;
    background-color: var(--sidebar-active);
    color: var(--sidebar-text) !important;
}

/* Store Selector Styles */
.store-selector .btn {
    background: var(--sidebar-hover);
    border: 1px solid var(--sidebar-active);
    color: var(--sidebar-text);
    transition: all 0.3s ease;
    text-align: left;
    font-size: 0.875rem;
}

.store-selector .btn:hover {
    background: var(--sidebar-active);
    border-color: var(--sidebar-hover);
}

.store-selector .dropdown-menu {
    max-height: 300px;
    overflow-y: auto;
    margin-top: 0.5rem;
    width: 100%;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
}

.store-selector .dropdown-item {
    padding: 0.75rem 1rem;
    transition: all 0.2s ease;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.store-selector .dropdown-item:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.store-selector .dropdown-item.active {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    font-weight: 600;
}

.store-selector .text-truncate {
    max-width: 180px;
}

.store-selector .fa-check {
    font-size: 0.75rem;
}

/* Store Selector Header (for non-sidebar pages) */
.store-selector-header {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1000;
}

.store-selector-header .btn {
    background: var(--card-bg);
    border: 2px solid var(--border-color);
    color: var(--text-primary);
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.store-selector-header .btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--card-hover-shadow);
}

.store-selector-header .dropdown-menu {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    margin-top: 0.5rem;
}

.store-selector-header .dropdown-item {
    color: var(--text-primary);
    padding: 0.5rem 1rem;
}

.store-selector-header .dropdown-item:hover {
    background: var(--bg-tertiary);
}

/* Main content area */
main {
    margin-left: 280px;
    min-height: 100vh;
    width: calc(100% - 280px);
    padding: 2rem;
    background-color: var(--bg-secondary);
    transition: background-color 0.3s ease;
}

/* ALL CARDS USE THEME VARIABLES - NO EXCEPTIONS */
.card {
    border: none;
    border-radius: 16px;
    box-shadow: var(--card-shadow);
    transition: all 0.3s ease;
    overflow: hidden;
    background: var(--card-bg) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--border-color);
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--card-hover-shadow);
}

.card-header {
    background: transparent !important;
    border-bottom: 1px solid var(--border-color);
    padding: 1.5rem;
    font-weight: 600;
    color: var(--text-primary) !important;
}

/* Override Bootstrap's bg-primary on card headers */
.card-header.bg-primary {
    background: var(--primary-gradient) !important;
    color: white !important;
}

.card-body {
    padding: 1.5rem;
    color: var(--text-primary) !important;
    background: transparent !important;
}

.card-footer {
    background: transparent !important;
    border-top: 1px solid var(--border-color);
    color: var(--text-primary) !important;
}

/* ALL BUTTONS KEEP THEIR INTENDED COLORS */
.btn {
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-family: 'Lexend', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    position: relative;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white !important;
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white !important;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107, #ff9800);
    color: #212529 !important;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545, #e91e63);
    color: white !important;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8, #007bff);
    color: white !important;
}

.btn-outline-primary {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;
}

.btn-outline-primary:hover {
    background: #667eea;
    color: white !important;
}

.btn-outline-secondary {
    background: transparent;
    color: var(--text-primary);
    border: 2px solid var(--border-color);
}

.btn-outline-secondary:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

/* ALL FORM CONTROLS USE THEME VARIABLES */
.form-control, .form-select {
    background-color: var(--input-bg) !important;
    border: 2px solid var(--input-border) !important;
    border-radius: 12px;
    padding: 0.75rem 1rem;
    font-family: 'Lexend', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    transition: all 0.3s ease;
    font-weight: 500;
    color: var(--text-primary) !important;
}

.form-control:focus, .form-select:focus {
    background-color: var(--input-bg) !important;
    border-color: var(--input-focus-border) !important;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.1);
    color: var(--text-primary) !important;
}

.form-control::placeholder {
    color: var(--text-muted);
    opacity: 0.7;
}

.form-label {
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.input-group-text {
    background: var(--bg-tertiary) !important;
    border: 2px solid var(--border-color) !important;
    color: var(--text-secondary) !important;
}

/* ALL TABLES USE THEME VARIABLES */
.table {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--card-shadow);
    background: var(--table-bg) !important;
    color: var(--text-primary) !important;
    border: none;
}

.table thead th {
    background: var(--table-header-bg) !important;
    border-bottom: 2px solid var(--border-color) !important;
    font-weight: 700;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.5px;
    color: var(--text-secondary) !important;
    padding: 1rem;
}

.table tbody td {
    padding: 1rem;
    vertical-align: middle;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
    background: transparent !important;
}

.table tbody tr:hover {
    background-color: var(--table-hover-bg) !important;
}

/* OVERRIDE PROBLEMATIC HARDCODED BACKGROUNDS ONLY */
[style*="background-color: white"],
[style*="background-color:#ffffff"],
[style*="background-color: #ffffff"],
[style*="background-color:#fff"],
[style*="background-color: #fff"],
[style*="background: white"],
[style*="background:#ffffff"],
[style*="background: #ffffff"],
[style*="background:#fff"],
[style*="background: #fff"] {
    background-color: var(--card-bg) !important;
}

[style*="background-color: #f8f9fa"],
[style*="background-color:#f8f9fa"],
[style*="background: #f8f9fa"],
[style*="background:#f8f9fa"] {
    background-color: var(--bg-tertiary) !important;
}

/* SPECIFIC FIXES FOR PROBLEM ELEMENTS */
.url-handle {
    background-color: var(--bg-tertiary) !important;
    color: var(--text-primary) !important;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    padding: 2px 6px;
    border-radius: 3px;
}

.stat-icon {
    background-color: var(--bg-tertiary) !important;
    border: 2px solid var(--border-color) !important;
}

.optimization-details,
.stat-item,
.tag-item,
.translation-item,
.progress-section,
.language-card,
.collection-card,
.product-card {
    background-color: var(--card-bg) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
}

/* ALERTS KEEP THEIR SEMANTIC COLORS BUT ADAPT TO THEME */
.alert {
    border-radius: 12px;
    border: none;
    padding: 1rem 1.5rem;
    font-weight: 500;
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    border: 1px solid rgba(40, 167, 69, 0.3);
    color: #155724;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    border: 1px solid rgba(220, 53, 69, 0.3);
    color: #721c24;
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.3);
    color: #856404;
}

.alert-info {
    background-color: rgba(23, 162, 184, 0.1);
    border: 1px solid rgba(23, 162, 184, 0.3);
    color: #0c5460;
}

.theme-dark .alert-success {
    background-color: rgba(40, 167, 69, 0.15);
    color: #75e900;
}

.theme-dark .alert-danger {
    background-color: rgba(220, 53, 69, 0.15);
    color: #ff6b6b;
}

.theme-dark .alert-warning {
    background-color: rgba(255, 193, 7, 0.15);
    color: #ffc107;
}

.theme-dark .alert-info {
    background-color: rgba(23, 162, 184, 0.15);
    color: #3dd5f3;
}

/* BADGES KEEP THEIR SEMANTIC COLORS */
.badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.75rem;
}

.badge.bg-primary {
    background: #667eea !important;
    color: #ffffff !important;
}

.badge.bg-success {
    background: #28a745 !important;
    color: #ffffff !important;
}

.badge.bg-warning {
    background: #ffc107 !important;
    color: #212529 !important;
}

.badge.bg-danger {
    background: #dc3545 !important;
    color: #ffffff !important;
}

.badge.bg-info {
    background: #17a2b8 !important;
    color: #ffffff !important;
}

.badge.bg-secondary {
    background: var(--text-muted) !important;
    color: #ffffff !important;
}

/* Progress bars */
.progress {
    height: 8px;
    border-radius: 50px;
    background-color: var(--bg-tertiary);
}

.progress-bar {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

/* Bootstrap overrides for dark mode compatibility */
.bg-white {
    background-color: var(--card-bg) !important;
}

.bg-light {
    background-color: var(--bg-secondary) !important;
}

.text-dark {
    color: var(--text-primary) !important;
}

.border {
    border-color: var(--border-color) !important;
}

/* Ensure all text in cards uses theme colors */
.card h1, .card h2, .card h3, .card h4, .card h5, .card h6,
.card p, .card span, .card div, .card label {
    color: var(--text-primary) !important;
}

.card .text-muted {
    color: var(--text-muted) !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.5s ease;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    main {
        margin-left: 0;
        width: 100%;
        padding: 1rem;
    }
    
    .theme-toggle {
        top: 10px;
        right: 10px;
        padding: 6px 12px;
        font-size: 12px;
    }
    
    .theme-toggle .theme-text {
        display: none;
    }
}

/* CLEAN BACKGROUND UTILITIES */
.bg-white {
    background-color: var(--card-bg) !important;
    color: var(--text-primary) !important;
}

.bg-light {
    background-color: var(--bg-tertiary) !important;
    color: var(--text-primary) !important;
}

.bg-dark {
    background-color: var(--bg-primary) !important;
    color: var(--text-primary) !important;
}

.bg-secondary {
    background-color: var(--bg-secondary) !important;
    color: var(--text-primary) !important;
}

/* FORCE CORRECT TEXT COLORS ON PROBLEMATIC ELEMENTS */
.card-header,
.card-body,
.card-footer,
.table,
.table td,
.table th,
.form-control,
.form-select,
.modal-content,
.modal-header,
.modal-body,
.modal-footer,
.breadcrumb,
.breadcrumb-item {
    color: var(--text-primary) !important;
}

/* FIX WHITE TEXT ON LIGHT BACKGROUNDS */
.text-white:not(.btn):not(.badge):not(.alert) {
    color: var(--text-primary) !important;
}

/* FIX SEO DASHBOARD STATS CARDS - TARGET THE ACTUAL ELEMENTS */
.card.theme-aware-bg {
    background: var(--card-bg) !important;
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
}

.card-body.theme-aware-bg {
    background: transparent !important;
    color: var(--text-primary) !important;
}

.stats-value {
    color: var(--text-primary) !important;
}

.stats-label {
    color: var(--text-secondary) !important;
}

.progress-percentage {
    color: var(--text-primary) !important;
}

.automation-card {
    background: var(--card-bg) !important;
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
}

/* OVERRIDE INLINE STYLES ON THEME-AWARE CARDS */
.theme-aware-bg[style*="background"] {
    background: var(--card-bg) !important;
}

.theme-aware-bg[style*="color"] {
    color: var(--text-primary) !important;
}

/* Global Lexend Font for All Input Elements */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="tel"],
input[type="url"],
input[type="search"],
input[type="date"],
input[type="time"],
input[type="datetime-local"],
textarea,
select {
    font-family: 'Lexend', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}