/* AdCreative.ai Exact Style Recreation */

/* Import Google Fonts - Must be at the top */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&amp;display=swap');
@import url('https://fonts.googleapis.com/css2?family=Lexend:wght@400;500;600;700;800;900&amp;display=swap');

:root {
    /* AdCreative Color Palette */
    --ac-primary: #ef2b70;
    --ac-primary-dark: #d91a5f;
    --ac-primary-light: #ff4085;
    --ac-secondary: #1e1541;
    --ac-purple-dark: #0f0b1a;
    --ac-purple-medium: #2a2250;
    --ac-purple-light: #3d3460;
    
    /* Grove Green Accent */
    --ac-grove: #22c55e;
    --ac-grove-light: #4ade80;
    --ac-grove-dark: #16a34a;
    
    /* Backgrounds */
    --ac-bg-white: #ffffff;
    --ac-bg-light: #f8f9fb;
    --ac-bg-gray: #f3f4f6;
    --ac-bg-purple-tint: #faf9fc;
    
    /* Text Colors */
    --ac-text-primary: #1e1541;
    --ac-text-secondary: #64607d;
    --ac-text-muted: #9691ad;
    --ac-text-white: #ffffff;
    
    /* Gradients */
    --ac-gradient-primary: linear-gradient(135deg, #ef2b70 0%, #ff4085 100%);
    --ac-gradient-purple: linear-gradient(135deg, #1e1541 0%, #3d3460 100%);
    --ac-gradient-light: linear-gradient(180deg, #ffffff 0%, #f8f9fb 100%);
    
    /* Shadows */
    --ac-shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.04);
    --ac-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    --ac-shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.12);
    --ac-shadow-xl: 0 16px 48px rgba(0, 0, 0, 0.16);
    
    /* Border Radius */
    --ac-radius: 16px;
    --ac-radius-sm: 8px;
    --ac-radius-lg: 24px;
    --ac-radius-full: 9999px;
    
    /* Typography */
    --ac-font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica', 'Arial', sans-serif;
    --ac-font-heading: 'Lexend', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica', 'Arial', sans-serif;
}

/* Global Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--ac-font-sans);
    font-size: 16px;
    line-height: 1.5;
    color: var(--ac-text-primary);
    background-color: var(--ac-bg-white);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Apply Lexend to all headings */
h1, h2, h3, h4, h5, h6,
.ac-hero-content h1,
.ac-section-title,
.ac-cta-title,
.ac-feature-title,
.ac-footer-brand h3,
.ac-footer-links h4 {
    font-family: var(--ac-font-heading) !important;
    font-weight: 700 !important;
    letter-spacing: -0.02em;
}

/* Navigation Bar */
/* Nav styling moved to mega-menu.css for consistency */

/* Nav container styling moved to mega-menu.css */

.ac-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
    font-weight: 700;
    font-size: 1.25rem;
    color: var(--ac-text-primary);
    font-family: var(--ac-font-heading);
}

.ac-logo-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--ac-grove) 0%, var(--ac-grove-dark) 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 2px 8px rgba(34, 197, 94, 0.2);
}

.ac-nav-menu {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.ac-nav-link {
    color: var(--ac-text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
    position: relative;
}

.ac-nav-link:hover {
    color: var(--ac-grove);
}

/* Green underline on hover */
/* Removed nav link underline animation - handled in mega-menu.css */

/* Dropdown Styles */
.ac-nav-dropdown {
    position: relative;
}

.ac-dropdown-toggle::after {
    content: '\f078';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    font-size: 0.75rem;
    margin-left: 0.5rem;
    transition: transform 0.3s ease;
}

.ac-nav-dropdown:hover .ac-dropdown-toggle::after {
    transform: rotate(180deg);
}

.ac-dropdown-menu {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    margin-top: 1rem;
    min-width: 280px;
    background: white;
    border-radius: var(--ac-radius);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07),
                0 10px 15px rgba(0, 0, 0, 0.1),
                0 20px 25px rgba(0, 0, 0, 0.1);
    padding: 1rem;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
}

.ac-nav-dropdown:hover .ac-dropdown-menu {
    opacity: 1;
    visibility: visible;
    margin-top: 0.5rem;
}

.ac-dropdown-header {
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: var(--ac-text-muted);
    margin: 0.5rem 0;
    padding: 0.5rem 1rem;
}

.ac-dropdown-item {
    display: block;
    padding: 0.75rem 1rem;
    color: var(--ac-text-secondary);
    text-decoration: none;
    border-radius: var(--ac-radius-sm);
    transition: all 0.2s ease;
    font-size: 0.9375rem;
}

.ac-dropdown-item:hover {
    background: var(--ac-bg-light);
    color: var(--ac-primary);
    transform: translateX(4px);
}

.ac-dropdown-divider {
    height: 1px;
    background: var(--ac-bg-gray);
    margin: 0.75rem 0;
}

/* Hero Section */
.ac-hero {
    padding: 4rem 2rem 4rem; /* Reduced top padding from 8rem to 4rem */
    min-height: 90vh;
    display: flex;
    align-items: flex-start; /* Changed from center to flex-start */
    background: var(--ac-gradient-light);
    position: relative;
    overflow: hidden;
}

.ac-hero::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -20%;
    width: 800px;
    height: 800px;
    background: radial-gradient(circle, rgba(239, 43, 112, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    animation: pulse 4s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 0.5; }
    50% { transform: scale(1.1); opacity: 0.3; }
}

.ac-hero-container {
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
    position: relative;
    z-index: 1;
}

.ac-hero-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start; /* Changed from center to start to prevent vertical shifting */
    position: relative;
}

.ac-hero-content h1 {
    font-size: clamp(2.5rem, 5vw, 3.5rem);
    font-weight: 800;
    line-height: 1.1;
    color: var(--ac-text-primary);
    margin-bottom: 1.5rem;
}

.ac-hero-highlight {
    color: var(--ac-primary);
}

.ac-hero-subtitle {
    font-size: 1.25rem;
    color: var(--ac-text-secondary);
    margin-bottom: 2rem;
    line-height: 1.6;
}

/* Trust Badges */
.ac-trust-badges {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 2rem;
    flex-wrap: nowrap;
}

.ac-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--ac-text-secondary);
    font-size: 0.875rem;
    white-space: nowrap;
}

.ac-badge-icon {
    color: var(--ac-grove);
}

/* CTA Buttons */
.ac-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border-radius: var(--ac-radius);
    font-family: var(--ac-font-heading);
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;
    font-size: 1rem;
}

.ac-btn-primary {
    background: var(--ac-gradient-primary);
    color: white;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05), 
                0 4px 8px rgba(239, 43, 112, 0.15),
                0 8px 16px rgba(239, 43, 112, 0.15);
    position: relative;
    overflow: hidden;
}

.ac-btn-primary::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.2);
    transform: translate(-50%, -50%) scale(0);
    border-radius: 50%;
    transition: transform 0.6s ease;
}

.ac-btn-primary:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05),
                0 6px 12px rgba(239, 43, 112, 0.2),
                0 12px 24px rgba(239, 43, 112, 0.25);
}

.ac-btn-primary:hover::after {
    transform: translate(-50%, -50%) scale(2);
}

/* Large button variant */
.ac-btn-lg {
    padding: 0.875rem 1.75rem;
    font-size: 1rem;
    font-weight: 700;
}

.ac-btn-secondary {
    background: white;
    color: var(--ac-text-primary);
    border: 2px solid var(--ac-bg-gray);
}

.ac-btn-secondary:hover {
    border-color: var(--ac-primary);
    color: var(--ac-primary);
}

.ac-btn-group {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Hero Visual */
.ac-hero-visual {
    position: relative;
    align-self: start; /* Changed from center to start */
    padding-top: 0; /* Removed padding */
    z-index: 1; /* Ensure visual content is above blob */
    overflow: visible;
}

/* Static Morphing Blob - Absolute Position */
.ac-hero-blob {
    position: absolute;
    top: -50px;
    left: calc(50% + 20px);
    width: 690px;
    height: 690px;
    background: linear-gradient(135deg, 
        rgba(239, 43, 112, 0.08) 0%, 
        rgba(239, 43, 112, 0.05) 50%,
        transparent 100%);
    border: 1px solid rgba(239, 43, 112, 0.1);
    border-radius: 40% 60% 60% 40% / 60% 40% 60% 40%;
    animation: morph-blob-hero-only 12s ease-in-out infinite;
    z-index: 0;
    filter: blur(0.5px);
    pointer-events: none;
    will-change: border-radius;
    transform-style: preserve-3d;
    backface-visibility: hidden;
}

@keyframes morph-blob-hero-stable {
    0%, 100% { 
        border-radius: 40% 60% 60% 40% / 60% 40% 60% 40%;
        transform: rotate(0deg);
    }
    25% { 
        border-radius: 60% 40% 40% 60% / 40% 60% 40% 60%;
        transform: rotate(90deg);
    }
    50% { 
        border-radius: 40% 60% 60% 40% / 40% 60% 60% 40%;
        transform: rotate(180deg);
    }
    75% { 
        border-radius: 60% 40% 60% 40% / 40% 60% 40% 60%;
        transform: rotate(270deg);
    }
}

@keyframes morph-blob-hero-only {
    0%, 100% { 
        border-radius: 40% 60% 60% 40% / 60% 40% 60% 40%;
    }
    25% { 
        border-radius: 60% 40% 40% 60% / 40% 60% 40% 60%;
    }
    50% { 
        border-radius: 40% 60% 60% 40% / 40% 60% 60% 40%;
    }
    75% { 
        border-radius: 60% 40% 60% 40% / 40% 60% 40% 60%;
    }
}

@keyframes morph-blob-hero {
    0%, 100% { 
        border-radius: 40% 60% 60% 40% / 60% 40% 60% 40%;
        transform: translate(-50%, -50%) rotate(0deg);
    }
    25% { 
        border-radius: 60% 40% 40% 60% / 40% 60% 40% 60%;
        transform: translate(-50%, -50%) rotate(90deg);
    }
    50% { 
        border-radius: 40% 60% 60% 40% / 40% 60% 60% 40%;
        transform: translate(-50%, -50%) rotate(180deg);
    }
    75% { 
        border-radius: 60% 40% 60% 40% / 40% 60% 40% 60%;
        transform: translate(-50%, -50%) rotate(270deg);
    }
}

.ac-browser-mockup {
    background: white;
    border-radius: var(--ac-radius);
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05),
                0 2px 4px rgba(0, 0, 0, 0.02),
                0 8px 16px rgba(0, 0, 0, 0.04),
                0 16px 32px rgba(0, 0, 0, 0.06),
                0 32px 64px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transform: perspective(1000px) rotateY(-10deg) scale(0.95);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(0, 0, 0, 0.06);
    position: relative;
    z-index: 2;
}

.ac-browser-mockup::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, transparent 60%);
    opacity: 0.5;
    pointer-events: none;
}

.ac-browser-mockup:hover {
    transform: perspective(1000px) rotateY(0deg) scale(1);
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05),
                0 4px 8px rgba(0, 0, 0, 0.03),
                0 12px 24px rgba(0, 0, 0, 0.05),
                0 24px 48px rgba(0, 0, 0, 0.07),
                0 48px 96px rgba(0, 0, 0, 0.1);
}

.ac-browser-header {
    background: var(--ac-bg-gray);
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.ac-browser-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #ddd;
}

.ac-browser-dot:first-child { background: #ff6058; }
.ac-browser-dot:nth-child(2) { background: #ffbd2e; }
.ac-browser-dot:nth-child(3) { background: #27c93f; }

.ac-browser-content {
    padding: 2rem;
    background: white;
}

.ac-demo-image {
    width: 100%;
    height: auto;
    border-radius: var(--ac-radius-sm);
}

/* Performance Metrics */
.ac-metrics {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin-top: 2rem;
}

.ac-metric {
    text-align: center;
    padding: 1.5rem;
    background: white;
    border-radius: var(--ac-radius);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(0, 0, 0, 0.04);
    position: relative;
    overflow: hidden;
}

.ac-metric::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--ac-grove);
    transform: scaleX(0);
    transition: transform 0.4s ease;
}

.ac-metric:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08),
                0 8px 24px rgba(0, 0, 0, 0.04);
    border-color: rgba(34, 197, 94, 0.2);
}

.ac-metric:hover::before {
    transform: scaleX(1);
}

.ac-metric-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--ac-primary);
}

.ac-metric-label {
    font-size: 0.875rem;
    color: var(--ac-text-muted);
    margin-top: 0.25rem;
}

/* Logo Slider Section */
.ac-logos-section {
    padding: 4rem 0;
    background: var(--ac-bg-light);
    overflow: hidden;
}

.ac-logos-title {
    text-align: center;
    font-size: 1rem;
    color: var(--ac-text-muted);
    margin-bottom: 2rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.ac-logos-slider {
    display: flex;
    animation: slide 45s linear infinite;
    white-space: nowrap;
}

@keyframes slide {
    0% { transform: translateX(0); }
    100% { transform: translateX(-50%); }
}

/* New pulse animations for mobile automation toggles */
@keyframes pulse-dot {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.5);
        opacity: 0.5;
    }
}

@keyframes pulse-ring {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    100% {
        transform: scale(1.2);
        opacity: 0;
    }
}

.ac-logo-item {
    flex-shrink: 0;
    margin-right: 2rem;
    opacity: 0.7;
    transition: opacity 0.3s ease;
    display: flex;
    align-items: center;
}

.ac-logo-item:hover {
    opacity: 1;
}

.ac-logo-item img {
    height: 40px;
    filter: grayscale(100%);
    transition: filter 0.3s ease;
}

.ac-logo-item:hover img {
    filter: grayscale(0%);
}

/* Features Section */
.ac-features {
    padding: 6rem 2rem;
}

.ac-features-container {
    max-width: 1200px;
    margin: 0 auto;
}

.ac-section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.ac-section-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--ac-text-primary);
    margin-bottom: 1rem;
}

.ac-section-subtitle {
    font-size: 1.25rem;
    color: var(--ac-text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

.ac-features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.ac-feature-card {
    background: white;
    border: 1px solid rgba(0, 0, 0, 0.06);
    border-radius: var(--ac-radius);
    padding: 2.5rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02),
                0 2px 4px rgba(0, 0, 0, 0.02);
}

.ac-feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--ac-gradient-primary);
    transform: translateX(-100%);
    transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.ac-feature-card::after {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, transparent 40%, rgba(239, 43, 112, 0.02) 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
}

.ac-feature-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 0 0 1px rgba(239, 43, 112, 0.1),
                0 4px 8px rgba(0, 0, 0, 0.04),
                0 12px 24px rgba(0, 0, 0, 0.06),
                0 24px 48px rgba(0, 0, 0, 0.08);
    border-color: rgba(239, 43, 112, 0.2);
}

.ac-feature-card:hover::before {
    transform: translateX(0);
}

.ac-feature-card:hover::after {
    opacity: 1;
}

.ac-feature-icon {
    width: 48px;
    height: 48px;
    background: var(--ac-bg-light);
    border-radius: var(--ac-radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    color: var(--ac-primary);
}

/* Alternate feature icons between pink and green */
.ac-feature-card:nth-child(odd) .ac-feature-icon {
    color: var(--ac-grove);
    background: rgba(34, 197, 94, 0.1);
}

.ac-feature-card:nth-child(even) .ac-feature-icon {
    color: var(--ac-primary);
    background: rgba(239, 43, 112, 0.1);
}

.ac-feature-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--ac-text-primary);
    margin-bottom: 0.75rem;
}

.ac-feature-description {
    color: var(--ac-text-secondary);
    line-height: 1.6;
}

/* CTA Section */
.ac-cta {
    padding: 6rem 2rem;
    background: var(--ac-gradient-purple);
    text-align: center;
}

.ac-cta-container {
    max-width: 800px;
    margin: 0 auto;
}

.ac-cta-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: white;
    margin-bottom: 1rem;
}

.ac-cta-subtitle {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 2rem;
}

.ac-cta .ac-btn-primary {
    background: white;
    color: var(--ac-primary);
    box-shadow: 0 4px 14px rgba(0, 0, 0, 0.1);
}

.ac-cta .ac-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* Footer */
.ac-footer {
    padding: 4rem 2rem 2rem;
    background: var(--ac-bg-light);
}

.ac-footer-container {
    max-width: 1200px;
    margin: 0 auto;
}

.ac-footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 3rem;
    margin-bottom: 3rem;
}

.ac-footer-brand h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--ac-text-primary);
}

.ac-footer-description {
    color: var(--ac-text-secondary);
    margin-bottom: 1.5rem;
}

.ac-footer-links h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--ac-text-primary);
    margin-bottom: 1rem;
}

.ac-footer-links ul {
    list-style: none;
}

.ac-footer-links li {
    margin-bottom: 0.75rem;
}

.ac-footer-links a {
    color: var(--ac-text-secondary);
    text-decoration: none;
    transition: color 0.2s ease;
}

.ac-footer-links a:hover {
    color: var(--ac-primary);
}

.ac-footer-bottom {
    padding-top: 2rem;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.ac-footer-copyright {
    color: var(--ac-text-muted);
    font-size: 0.875rem;
}

.ac-social-links {
    display: flex;
    gap: 1rem;
}

.ac-social-link {
    width: 40px;
    height: 40px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--ac-text-secondary);
    transition: all 0.3s ease;
}

.ac-social-link:hover {
    background: var(--ac-primary);
    color: white;
    transform: translateY(-2px);
}

/* Automation Section */
.ac-automation-section {
    padding: 6rem 2rem;
    background: var(--ac-bg-white);
    position: relative;
    overflow: hidden;
}


.ac-automation-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 200%;
    height: 100%;
    background: linear-gradient(90deg, transparent 0%, rgba(34, 197, 94, 0.03) 50%, transparent 100%);
    animation: shimmer 8s infinite;
    z-index: 1;
}

@keyframes shimmer {
    0% { transform: translateX(-50%); }
    100% { transform: translateX(50%); }
}

.ac-automation-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.ac-automation-card {
    background: white;
    border-radius: var(--ac-radius-lg);
    padding: 2rem;
    border: 1px solid rgba(0, 0, 0, 0.06);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.ac-automation-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--ac-grove);
    transform: scaleX(0);
    transition: transform 0.6s ease;
    transform-origin: left;
}

.ac-automation-card.active::before {
    transform: scaleX(1);
}

.ac-automation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.ac-automation-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--ac-text-primary);
    font-family: var(--ac-font-heading);
    flex: 1;
    margin-right: 1rem;
}

.ac-automation-toggle {
    position: relative;
    width: 52px;
    height: 28px;
    cursor: pointer;
    flex-shrink: 0;
    box-sizing: content-box;
}

.ac-automation-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.ac-toggle-slider {
    position: absolute;
    top: 0;
    left: 0;
    width: 52px;
    height: 28px;
    background-color: #ccc;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 34px;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
}

.ac-toggle-slider:before {
    position: absolute;
    content: "";
    height: 22px;
    width: 22px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.ac-automation-toggle input:checked + .ac-toggle-slider {
    background: linear-gradient(135deg, var(--ac-grove) 0%, var(--ac-grove-dark) 100%);
    box-shadow: 0 2px 8px rgba(34, 197, 94, 0.3);
}

.ac-automation-toggle input:checked + .ac-toggle-slider:before {
    transform: translateX(calc(52px - 22px - 6px));
    box-shadow: 0 2px 8px rgba(34, 197, 94, 0.3);
}

.ac-automation-features {
    list-style: none;
    padding: 0;
    margin: 0;
}

.ac-automation-feature {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--ac-bg-light);
    transition: all 0.3s ease;
    opacity: 0.6;
}

.ac-automation-feature:last-child {
    border-bottom: none;
}

.ac-automation-card.active .ac-automation-feature {
    opacity: 1;
    transform: translateX(4px);
}

.ac-automation-feature-icon {
    width: 32px;
    height: 32px;
    background: var(--ac-bg-light);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: var(--ac-text-muted);
    transition: all 0.3s ease;
}

.ac-automation-card.active .ac-automation-feature-icon {
    background: rgba(34, 197, 94, 0.1);
    color: var(--ac-grove);
}

.ac-automation-feature-text {
    flex: 1;
    font-size: 0.95rem;
    color: var(--ac-text-secondary);
}

.ac-automation-status {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: var(--ac-bg-light);
    border-radius: var(--ac-radius-full);
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--ac-text-muted);
    margin-top: 1.5rem;
    transition: all 0.3s ease;
}

.ac-automation-card.active .ac-automation-status {
    background: rgba(34, 197, 94, 0.1);
    color: var(--ac-grove);
}

.ac-automation-status-dot {
    width: 8px;
    height: 8px;
    background: var(--ac-text-muted);
    border-radius: 50%;
    transition: all 0.3s ease;
}

.ac-automation-card.active .ac-automation-status-dot {
    background: var(--ac-grove);
    box-shadow: 0 0 0 4px rgba(34, 197, 94, 0.2);
    animation: pulse-dot 2s infinite;
}

@keyframes pulse-dot {
    0% { box-shadow: 0 0 0 4px rgba(34, 197, 94, 0.2); }
    50% { box-shadow: 0 0 0 8px rgba(34, 197, 94, 0); }
    100% { box-shadow: 0 0 0 4px rgba(34, 197, 94, 0.2); }
}

/* Responsive breakpoints for automation grid */
@media (max-width: 1200px) {
    .ac-automation-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .ac-automation-grid {
        grid-template-columns: 1fr;
        gap: 1.25rem;
    }
    
    .ac-automation-card {
        padding: 1.5rem;
    }
    
    .ac-automation-title {
        font-size: 1.125rem;
    }
}

/* Shopify Badge */
.shopify-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    background: white;
    border: 2px solid #96bf48;
    border-radius: var(--ac-radius);
    font-weight: 600;
    color: var(--ac-text-primary);
    box-shadow: 0 2px 8px rgba(150, 191, 72, 0.15);
    transition: all 0.3s ease;
    text-decoration: none;
    margin-top: 1.5rem;
}

.shopify-badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(150, 191, 72, 0.25);
    border-color: #7fa938;
}

.shopify-badge i {
    font-size: 1.5rem;
    color: #96bf48;
}

/* Compact Automation Section */
.ac-automation-compact {
    padding: 3rem 2rem;
    background: linear-gradient(180deg, var(--ac-bg-light) 0%, var(--ac-bg-white) 100%);
    position: relative;
    overflow: hidden;
}

.ac-compact-automation-container {
    max-width: 720px;
    margin: 0 auto;
    position: relative;
    min-height: 400px;
}

.ac-compact-card {
    background: white;
    border-radius: var(--ac-radius);
    padding: 1rem 1.25rem;
    border: 1px solid rgba(0, 0, 0, 0.08);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0;
    transform: translateX(-60px);
    position: absolute;
    width: 100%;
    z-index: 1;
}

/* Tighter stacked positioning */
.ac-compact-card:nth-child(1) { top: 0; }
.ac-compact-card:nth-child(2) { top: 60px; }
.ac-compact-card:nth-child(3) { top: 120px; }
.ac-compact-card:nth-child(4) { top: 180px; }
.ac-compact-card:nth-child(5) { top: 240px; }
.ac-compact-card:nth-child(6) { top: 300px; }

.ac-compact-card.in-view {
    opacity: 1;
    transform: translateX(0) translateY(0);
}

/* Active state with elevation */
.ac-compact-card.active {
    box-shadow: 0 4px 16px rgba(34, 197, 94, 0.1);
    border-color: rgba(34, 197, 94, 0.2);
    z-index: 10;
    transform: translateX(0) translateY(-2px);
}

.ac-compact-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 3px;
    height: 100%;
    background: var(--ac-grove);
    transform: scaleY(0);
    transition: transform 0.6s ease;
    transform-origin: top;
}

.ac-compact-card.active::before {
    transform: scaleY(1);
}

.ac-compact-card-content {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.ac-compact-icon {
    width: 40px;
    height: 40px;
    background: var(--ac-bg-light);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.125rem;
    color: var(--ac-text-muted);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    flex-shrink: 0;
}

.ac-compact-card.active .ac-compact-icon {
    background: rgba(34, 197, 94, 0.1);
    color: var(--ac-grove);
    transform: scale(1.1);
}

.ac-compact-info {
    flex: 1;
    padding-right: 1rem;
}

.ac-compact-info h4 {
    font-size: 1rem;
    font-weight: 700;
    color: var(--ac-text-primary);
    margin-bottom: 0.125rem;
    font-family: var(--ac-font-heading);
}

.ac-compact-info p {
    font-size: 0.813rem;
    color: var(--ac-text-secondary);
    margin: 0;
    line-height: 1.4;
}

.ac-compact-toggle {
    position: absolute;
    right: 1.5rem;
    top: 50%;
    transform: translateY(-50%);
}

.ac-mini-toggle {
    position: relative;
    width: 64px;
    height: 36px;
    cursor: default;
    transition: all 0.3s ease;
}

.ac-mini-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.ac-mini-slider {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #e5e5e5;
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 36px;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.ac-mini-slider:before {
    position: absolute;
    content: "";
    height: 30px;
    width: 30px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* OFF text */
.ac-mini-slider::after {
    content: "OFF";
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 0.625rem;
    font-weight: 600;
    color: #999;
    transition: opacity 0.3s ease;
}

.ac-mini-toggle input:checked + .ac-mini-slider {
    background: var(--ac-grove);
    box-shadow: 0 2px 8px rgba(34, 197, 94, 0.3);
}

.ac-mini-toggle input:checked + .ac-mini-slider:before {
    transform: translateX(28px);
}

.ac-mini-toggle input:checked + .ac-mini-slider::after {
    content: "ON";
    left: 8px;
    right: auto;
    color: white;
    opacity: 1;
}

.ac-compact-stats {
    display: inline-flex;
    gap: 1rem;
    margin-left: 3.5rem;
    margin-top: 0.5rem;
}

.ac-stat-item {
    font-size: 0.75rem;
    color: var(--ac-text-muted);
    display: flex;
    align-items: center;
    gap: 0.375rem;
    transition: all 0.5s ease;
    opacity: 0;
    transform: translateY(10px);
}

.ac-compact-card.active .ac-stat-item {
    color: var(--ac-grove);
    opacity: 1;
    transform: translateY(0);
    font-weight: 600;
}

.ac-stat-item i {
    font-size: 0.75rem;
    color: var(--ac-grove);
}

/* Pulse attention animation for toggles */
.ac-mini-toggle.pulse-attention {
    animation: pulseGlow 0.8s ease-in-out infinite;
}

@keyframes pulseGlow {
    0%, 100% {
        transform: scale(1);
        filter: drop-shadow(0 0 0 rgba(34, 197, 94, 0));
    }
    50% {
        transform: scale(1.05);
        filter: drop-shadow(0 0 10px rgba(34, 197, 94, 0.6));
    }
}

/* Make stats invisible by default, visible when activated */
.ac-stat-item {
    opacity: 0;
    transform: translateY(10px);
}

.ac-stat-item.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Stacked card effect with shadows for depth */
.ac-compact-card:nth-child(1) { z-index: 6; }
.ac-compact-card:nth-child(2) { z-index: 5; transition-delay: 0.1s; }
.ac-compact-card:nth-child(3) { z-index: 4; transition-delay: 0.2s; }
.ac-compact-card:nth-child(4) { z-index: 3; transition-delay: 0.3s; }
.ac-compact-card:nth-child(5) { z-index: 2; transition-delay: 0.4s; }
.ac-compact-card:nth-child(6) { z-index: 1; transition-delay: 0.5s; }

/* Add depth shadow to cards below */
.ac-compact-card:not(:last-child)::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 10px;
    right: 10px;
    height: 10px;
    background: rgba(0, 0, 0, 0.05);
    filter: blur(5px);
    z-index: -1;
    transition: all 0.3s ease;
}

/* Hover effect for depth */
.ac-compact-card:hover {
    transform: translateY(-8px) scale(1.01);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
    z-index: 20 !important;
}

/* Results Dashboard Section */
.ac-results-section {
    padding: 6rem 2rem;
    background: linear-gradient(180deg, #ffffff 0%, #f5f5f5 100%);
    position: relative;
    overflow: hidden;
}

/* Results Section Background Blob */
.ac-results-section::before {
    content: '';
    position: absolute;
    top: 50%;
    left: -200px;
    transform: translateY(-50%);
    width: 1200px;
    height: 1200px;
    background: linear-gradient(135deg, 
        rgba(34, 197, 94, 0.08) 0%, 
        rgba(34, 197, 94, 0.05) 50%,
        transparent 100%);
    border: 1px solid rgba(34, 197, 94, 0.1);
    border-radius: 40% 60% 60% 40% / 60% 40% 60% 40%;
    animation: morph-blob-hero-only 12s ease-in-out infinite;
    z-index: 0;
    filter: blur(0.5px);
    pointer-events: none;
    display: none; /* Hidden for now */
}


.ac-results-dashboard {
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

/* Metric Cards */
.ac-metric-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.ac-metric-card {
    background: white;
    border-radius: var(--ac-radius);
    padding: 1.5rem;
    border: 1px solid rgba(0, 0, 0, 0.06);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    display: flex;
    align-items: center;
    gap: 1.5rem;
    transition: all 0.3s ease;
}

.ac-metric-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
}

.ac-metric-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(34, 197, 94, 0.05) 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: var(--ac-grove);
}

.ac-metric-content {
    flex: 1;
}

.ac-metric-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--ac-text-primary);
    font-family: var(--ac-font-heading);
    line-height: 1;
    margin-bottom: 0.25rem;
}

.ac-metric-label {
    font-size: 0.875rem;
    color: var(--ac-text-secondary);
    margin-bottom: 0.25rem;
}

.ac-metric-change {
    font-size: 0.813rem;
    color: var(--ac-grove);
    font-weight: 600;
}

/* Success Timeline */
.ac-timeline-container {
    background: white;
    border-radius: var(--ac-radius-lg);
    padding: 2.5rem;
    border: 1px solid rgba(0, 0, 0, 0.06);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
    position: relative;
    overflow: hidden;
}

.ac-timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.ac-timeline-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--ac-text-primary);
    font-family: var(--ac-font-heading);
}

.ac-timeline-selector {
    display: flex;
    gap: 0.5rem;
    background: var(--ac-bg-light);
    padding: 0.25rem;
    border-radius: var(--ac-radius);
}

.ac-timeline-btn {
    padding: 0.5rem 1rem;
    border: none;
    background: transparent;
    color: var(--ac-text-secondary);
    font-family: var(--ac-font-heading);
    font-weight: 500;
    font-size: 0.875rem;
    border-radius: var(--ac-radius-sm);
    cursor: pointer;
    transition: all 0.3s ease;
}

.ac-timeline-btn:hover {
    color: var(--ac-text-primary);
}

.ac-timeline-btn.active {
    background: white;
    color: var(--ac-grove);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

/* Timeline Track */
.ac-timeline-wrapper {
    position: relative;
    padding: 2rem 0;
}

.ac-timeline-track {
    position: absolute;
    top: 4rem;
    left: 2rem;
    right: 2rem;
    height: 4px;
    background: var(--ac-bg-light);
    border-radius: 2px;
}

.ac-timeline-progress {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: linear-gradient(90deg, var(--ac-grove) 0%, var(--ac-grove-light) 100%);
    border-radius: 2px;
    width: 0;
    transition: width 2s ease;
}

/* Timeline Content */
.ac-timeline-content {
    display: flex;
    justify-content: space-between;
    position: relative;
    padding: 0 2rem;
}

/* Main Chart */
.ac-chart-container {
    background: white;
    border-radius: var(--ac-radius-lg);
    padding: 2.5rem;
    border: 1px solid rgba(0, 0, 0, 0.06);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
    position: relative;
    overflow: hidden;
}

.ac-chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.ac-chart-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--ac-text-primary);
    font-family: var(--ac-font-heading);
}

.ac-chart-legend {
    display: flex;
    gap: 1.5rem;
    align-items: center;
}

.ac-legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--ac-text-secondary);
}

.ac-legend-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.ac-chart {
    position: relative;
}

.ac-chart-svg {
    width: 100%;
    height: auto;
}

/* X-axis labels */
.ac-chart-labels {
    display: flex;
    justify-content: space-between;
    padding: 0 60px; /* Match the SVG left/right padding */
    margin-top: 0.5rem;
    font-size: 0.813rem;
    color: var(--ac-text-muted);
}

.ac-chart-labels span {
    text-align: center;
    width: 0; /* Force equal spacing */
}

/* Chart line animations */
@keyframes drawLine {
    from {
        stroke-dasharray: 1000;
        stroke-dashoffset: 1000;
    }
    to {
        stroke-dasharray: 1000;
        stroke-dashoffset: 0;
    }
}

@keyframes fadeInDot {
    from { opacity: 0; transform: scale(0); }
    to { opacity: 1; transform: scale(1); }
}

@keyframes pulseDot {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

/* Milestones */
.ac-timeline-milestone {
    position: relative;
    flex: 1;
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.6s ease forwards;
}

.ac-timeline-milestone:nth-child(1) { animation-delay: 0.1s; }
.ac-timeline-milestone:nth-child(2) { animation-delay: 0.3s; }
.ac-timeline-milestone:nth-child(3) { animation-delay: 0.5s; }
.ac-timeline-milestone:nth-child(4) { animation-delay: 0.7s; }
.ac-timeline-milestone:nth-child(5) { animation-delay: 0.9s; }

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.ac-timeline-dot {
    width: 20px;
    height: 20px;
    background: white;
    border: 4px solid var(--ac-bg-gray);
    border-radius: 50%;
    position: absolute;
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 2;
    transition: all 0.3s ease;
}

.ac-timeline-milestone.active .ac-timeline-dot {
    border-color: var(--ac-grove);
    background: var(--ac-grove);
    box-shadow: 0 0 0 8px rgba(34, 197, 94, 0.1);
}

.ac-timeline-dot.success {
    width: 24px;
    height: 24px;
    border-color: var(--ac-grove);
    background: var(--ac-grove);
    box-shadow: 0 0 0 12px rgba(34, 197, 94, 0.1);
}

/* Timeline Cards */
.ac-timeline-card {
    background: var(--ac-bg-light);
    border-radius: var(--ac-radius);
    padding: 1.5rem;
    margin-top: 2rem;
    position: relative;
    transition: all 0.3s ease;
    cursor: pointer;
}

.ac-timeline-card::before {
    content: '';
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 2px;
    height: 20px;
    background: var(--ac-bg-gray);
}

.ac-timeline-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
}

.ac-timeline-card.success {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.05) 0%, rgba(34, 197, 94, 0.02) 100%);
    border: 1px solid rgba(34, 197, 94, 0.2);
}

.ac-timeline-day {
    font-size: 0.813rem;
    font-weight: 600;
    color: var(--ac-grove);
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.ac-timeline-card h4 {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--ac-text-primary);
    margin-bottom: 1rem;
    font-family: var(--ac-font-heading);
}

.ac-timeline-metrics {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.ac-metric-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 0.875rem;
    color: var(--ac-text-secondary);
}

.ac-metric-item i {
    width: 20px;
    color: var(--ac-text-muted);
}

.ac-metric-item.up {
    color: var(--ac-grove);
    font-weight: 600;
}

.ac-metric-item.up i {
    color: var(--ac-grove);
}

.ac-timeline-note {
    font-size: 0.813rem;
    color: var(--ac-text-muted);
    line-height: 1.5;
    margin: 0;
}

.ac-timeline-cta {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1rem;
    color: var(--ac-grove);
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
}

.ac-timeline-cta:hover {
    gap: 0.75rem;
    color: var(--ac-grove-dark);
}

/* Autopilot Badge */
.ac-autopilot-badge {
    background: linear-gradient(135deg, var(--ac-grove) 0%, var(--ac-grove-dark) 100%);
    color: white;
    padding: 1rem 2rem;
    border-radius: var(--ac-radius-full);
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 600;
    font-size: 0.938rem;
    box-shadow: 0 4px 16px rgba(34, 197, 94, 0.3);
    animation: float 4s ease-in-out infinite;
    margin: 2rem auto 0;
    position: relative;
    left: 50%;
    transform: translateX(-50%);
}

@keyframes float {
    0%, 100% { transform: translateX(-50%) translateY(0); }
    50% { transform: translateX(-50%) translateY(-10px); }
}

.ac-pulse-indicator {
    width: 8px;
    height: 8px;
    background: white;
    border-radius: 50%;
    animation: pulse 2s ease-out infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    }
}

/* Comparison Section */
.ac-comparison-section {
    padding: 6rem 2rem;
    background: #fafafa;
}

.ac-comparison-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.ac-comparison-card {
    background: white;
    border-radius: var(--ac-radius-lg);
    padding: 2rem;
    position: relative;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.ac-comparison-bad {
    border-color: rgba(234, 67, 53, 0.2);
}

.ac-comparison-card:nth-child(2) {
    border-color: rgba(234, 67, 53, 0.2);
}

.ac-comparison-best {
    border-color: var(--ac-grove);
    transform: scale(1.05);
    box-shadow: 0 8px 32px rgba(34, 197, 94, 0.15);
}

.ac-comparison-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    font-family: var(--ac-font-heading);
}

.ac-comparison-price {
    font-size: 1.125rem;
    color: var(--ac-text-secondary);
    margin-bottom: 1.5rem;
}

.ac-comparison-verdict {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    font-weight: 600;
}

.ac-comparison-bad .ac-comparison-verdict {
    color: #ea4335;
}

.ac-comparison-card:nth-child(2) .ac-comparison-verdict {
    color: #ea4335;
}

.ac-comparison-best .ac-comparison-verdict {
    color: var(--ac-grove);
}

.ac-comparison-verdict i {
    font-size: 1.25rem;
}

.ac-comparison-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.ac-comparison-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--ac-bg-light);
}

.ac-comparison-item:last-child {
    border-bottom: none;
}

.ac-comparison-item i {
    font-size: 1rem;
    margin-top: 0.125rem;
    flex-shrink: 0;
}

.ac-comparison-item.negative i {
    color: #ea4335;
}

.ac-comparison-item.positive i {
    color: var(--ac-grove);
}

.ac-comparison-item span {
    line-height: 1.5;
    color: var(--ac-text-secondary);
}

/* Comparison CTA */
.ac-comparison-cta {
    text-align: center;
    margin-top: 3rem;
    padding-top: 2rem;
}

.ac-comparison-cta p {
    font-size: 0.875rem;
    color: var(--ac-text-secondary);
    margin-top: 0.75rem;
}

/* Pricing Section */
.ac-pricing-section {
    padding: 6rem 2rem;
    background: white;
}

.ac-pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(340px, 1fr));
    gap: 3rem;
    max-width: 900px;
    margin: 0 auto 4rem;
}

.ac-pricing-card {
    background: white;
    border: 2px solid var(--ac-bg-gray);
    border-radius: var(--ac-radius-lg);
    padding: 2.5rem;
    position: relative;
    transition: all 0.3s ease;
    text-align: center;
}

.ac-pricing-featured {
    border-color: var(--ac-primary);
    transform: scale(1.05);
    box-shadow: 0 12px 48px rgba(239, 43, 112, 0.15);
}

.ac-pricing-badge {
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--ac-grove);
    color: white;
    padding: 0.375rem 1.25rem;
    border-radius: var(--ac-radius-full);
    font-size: 0.875rem;
    font-weight: 600;
}

.ac-pricing-featured .ac-pricing-badge {
    background: var(--ac-primary);
}

.ac-pricing-name {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    font-family: var(--ac-font-heading);
}

.ac-pricing-price {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: 0.25rem;
    margin-bottom: 0.5rem;
}

.ac-price-amount {
    font-size: 3rem;
    font-weight: 800;
    color: var(--ac-text-primary);
    font-family: var(--ac-font-heading);
}

.ac-price-period {
    font-size: 1.25rem;
    color: var(--ac-text-secondary);
}

.ac-pricing-description {
    color: var(--ac-text-secondary);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.ac-pricing-features {
    list-style: none;
    padding: 0;
    margin: 0 0 2rem 0;
    text-align: left;
}

.ac-pricing-features li {
    padding: 0.75rem 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--ac-text-secondary);
}

.ac-pricing-features i {
    color: var(--ac-grove);
    font-size: 1rem;
    flex-shrink: 0;
}

.ac-btn-block {
    width: 100%;
    padding: 1.25rem;
}

.ac-pricing-note {
    font-size: 0.875rem;
    color: var(--ac-text-muted);
    margin-top: 1rem;
}

/* Interactive ROI Calculator */
.ac-roi-calculator {
    background: var(--ac-bg-light);
    border-radius: var(--ac-radius-lg);
    padding: 3rem;
    max-width: 900px;
    margin: 0 auto;
}

.ac-roi-calculator h3 {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    font-family: var(--ac-font-heading);
    text-align: center;
}

.ac-roi-subtitle {
    text-align: center;
    color: var(--ac-text-secondary);
    margin-bottom: 2rem;
}

/* Input Controls */
.ac-roi-inputs {
    max-width: 500px;
    margin: 0 auto 3rem;
}

.ac-roi-input-group {
    text-align: left;
}

.ac-roi-input-group label {
    display: block;
    font-weight: 600;
    color: var(--ac-text-primary);
    margin-bottom: 1rem;
    font-size: 0.938rem;
}

/* Custom Slider */
.ac-slider-container {
    position: relative;
}

.ac-traffic-slider {
    width: 100%;
    height: 8px;
    border-radius: 4px;
    background: #e0e0e0;
    outline: none;
    -webkit-appearance: none;
    cursor: pointer;
}

.ac-traffic-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: var(--ac-grove);
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(34, 197, 94, 0.3);
    transition: all 0.2s ease;
}

.ac-traffic-slider::-webkit-slider-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.4);
}

.ac-traffic-slider::-moz-range-thumb {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: var(--ac-grove);
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 8px rgba(34, 197, 94, 0.3);
}

.ac-slider-value {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--ac-grove);
    margin-top: 0.5rem;
}

/* Custom Select */
.ac-roi-select {
    width: 100%;
    padding: 0.875rem 1.25rem;
    border: 2px solid var(--ac-bg-gray);
    border-radius: var(--ac-radius);
    background: white;
    font-family: var(--ac-font-heading);
    font-size: 0.938rem;
    color: var(--ac-text-primary);
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.ac-roi-select:hover {
    border-color: var(--ac-grove);
}

.ac-roi-select:focus {
    outline: none;
    border-color: var(--ac-grove);
    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

/* Results Display */
.ac-roi-results {
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.ac-roi-breakdown {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.ac-roi-item {
    background: white;
    border-radius: var(--ac-radius);
    padding: 1.5rem;
    border: 1px solid rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
}

.ac-roi-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
}

.ac-roi-item-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    font-weight: 600;
    color: var(--ac-text-primary);
    font-size: 0.938rem;
}

.ac-roi-item-header i {
    font-size: 1.25rem;
}

.ac-roi-revenue .ac-roi-item-header i {
    color: var(--ac-grove);
}

.ac-roi-cost .ac-roi-item-header i {
    color: var(--ac-primary);
}

.ac-roi-time .ac-roi-item-header i {
    color: #fbbc04;
}

.ac-roi-item-value {
    font-size: 1.75rem;
    font-weight: 800;
    color: var(--ac-text-primary);
    font-family: var(--ac-font-heading);
    margin-bottom: 0.25rem;
    transition: all 0.3s ease;
}

.ac-roi-item-detail {
    font-size: 0.813rem;
    color: var(--ac-text-muted);
    margin-bottom: 1rem;
}

/* Progress Bars */
.ac-roi-progress {
    width: 100%;
    height: 6px;
    background: var(--ac-bg-light);
    border-radius: 3px;
    overflow: hidden;
}

.ac-roi-progress-bar {
    height: 100%;
    border-radius: 3px;
    transition: width 0.6s ease;
}

.ac-roi-revenue .ac-roi-progress-bar {
    background: linear-gradient(90deg, var(--ac-grove) 0%, var(--ac-grove-light) 100%);
}

.ac-roi-cost .ac-roi-progress-bar {
    background: linear-gradient(90deg, var(--ac-primary) 0%, var(--ac-primary-light) 100%);
}

.ac-roi-time .ac-roi-progress-bar {
    background: linear-gradient(90deg, #fbbc04 0%, #ffd24d 100%);
}

/* Total Impact */
.ac-roi-total {
    background: linear-gradient(135deg, var(--ac-grove) 0%, var(--ac-grove-dark) 100%);
    color: white;
    padding: 2rem;
    border-radius: var(--ac-radius-lg);
    text-align: center;
    margin-bottom: 1.5rem;
    box-shadow: 0 8px 32px rgba(34, 197, 94, 0.3);
}

.ac-roi-total-label {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    opacity: 0.9;
}

.ac-roi-total-value {
    font-size: 3.5rem;
    font-weight: 900;
    font-family: var(--ac-font-heading);
    line-height: 1;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.ac-roi-annual {
    font-size: 1.125rem;
    opacity: 0.9;
}

/* ROI CTA */
.ac-roi-cta {
    animation: slideIn 0.5s ease;
}

@keyframes slideIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.ac-roi-cta p {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--ac-text-primary);
    margin-bottom: 1rem;
}

/* CTA Variations */
.ac-cta-gradient {
    background: linear-gradient(135deg, var(--ac-primary) 0%, var(--ac-primary-dark) 100%);
}

.ac-cta-buttons {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.ac-btn-white {
    background: white;
    color: var(--ac-primary);
}

.ac-btn-white:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.ac-btn-white-outline {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.ac-btn-white-outline:hover {
    background: white;
    color: var(--ac-primary);
}

.ac-cta-stats {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.ac-cta-stats span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.875rem;
}

.ac-cta-stats i {
    color: white;
}

/* URL Analysis Form */
.ac-url-analysis {
    margin-top: 2rem;
    margin-bottom: 2rem;
}

.ac-url-form {
    max-width: 700px;
}

.ac-url-input-group {
    display: flex;
    gap: 0.625rem;
    align-items: center;
    background: white;
    padding: 0.375rem;
    border-radius: var(--ac-radius);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.ac-url-input-group:hover {
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);
    border-color: rgba(34, 197, 94, 0.2);
    transform: translateY(-2px);
}

.ac-url-input {
    flex: 1;
    border: none;
    padding: 0.75rem 1.25rem;
    font-size: 1.0625rem;
    font-family: var(--ac-font-heading);
    color: var(--ac-text-primary);
    background: transparent;
    outline: none;
}

.ac-url-input::placeholder {
    color: var(--ac-text-muted);
}

/* Shopify Hint with Arrow */
.ac-shopify-hint {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 0.5rem;
    margin-top: 1rem;
    font-size: 0.875rem;
    color: #96bf48;
    font-weight: 600;
    position: relative;
}

.ac-shopify-hint i.fab.fa-shopify {
    font-size: 1.125rem;
}

.ac-hand-arrow {
    animation: handDraw 3s ease-in-out infinite;
    margin-right: 0.25rem;
    transform-origin: center bottom;
}

@keyframes handDraw {
    0%, 100% { 
        transform: translateY(0) rotate(0deg) scale(1); 
    }
    25% { 
        transform: translateY(-2px) rotate(-2deg) scale(1.02); 
    }
    50% { 
        transform: translateY(-4px) rotate(1deg) scale(1.05); 
    }
    75% { 
        transform: translateY(-2px) rotate(-1deg) scale(1.02); 
    }
}

/* Shopify Badge - Removed to prevent flashing */
/* Badge styles defined earlier in file if needed */

/* Adjust trust badges spacing */
.ac-trust-badges {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 2.5rem;
    flex-wrap: nowrap;
}

/* SEO Analysis Popup */
.ac-analysis-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    display: none;
    animation: fadeIn 0.3s ease;
}

.ac-popup-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(10px);
    cursor: pointer;
}

.ac-popup-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #f8f8f8;
    border-radius: var(--ac-radius);
    box-shadow: 0 20px 80px rgba(0, 0, 0, 0.25),
                0 0 0 1px rgba(0, 0, 0, 0.08);
    max-width: 850px;
    width: 95%;
    max-height: 95vh;
    overflow: hidden;
    animation: slideUp 0.4s ease;
    display: flex;
    flex-direction: column;
}

@keyframes slideUp {
    from {
        transform: translate(-50%, -40%);
        opacity: 0;
    }
    to {
        transform: translate(-50%, -50%);
        opacity: 1;
    }
}

.ac-popup-close {
    background: transparent;
    border: none;
    font-size: 1.5rem;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    padding: 0.25rem 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.3s ease;
    line-height: 1;
}

.ac-popup-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

/* PDF Report Header Bar */
.ac-pdf-header {
    background: #2c2c2c;
    color: white;
    padding: 0.75rem 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: var(--ac-radius) var(--ac-radius) 0 0;
    font-size: 0.875rem;
}

.ac-pdf-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 600;
}

.ac-pdf-title i {
    font-size: 1.125rem;
    color: #ea4335;
}

.ac-pdf-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.ac-pdf-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.813rem;
}

/* PDF Report Content Area */
.ac-pdf-document {
    background: white;
    margin: 1rem;
    border-radius: var(--ac-radius-sm);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
    flex: 1;
}

/* Analysis Header */
.ac-analysis-header {
    padding: 3rem 3rem 0;
    text-align: center;
    border-bottom: 2px solid #e0e0e0;
    margin: 0 3rem;
    padding-bottom: 2rem;
}

.ac-analysis-loading {
    animation: fadeIn 0.5s ease;
}

.ac-loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid var(--ac-bg-light);
    border-top-color: var(--ac-grove);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1.5rem;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.ac-analysis-loading h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--ac-text-primary);
    margin-bottom: 0.5rem;
    font-family: var(--ac-font-heading);
}

.ac-analysis-loading p {
    color: var(--ac-text-secondary);
}

/* Analysis Complete Header */
.ac-analysis-complete {
    animation: fadeIn 0.5s ease;
}

.ac-store-info {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.ac-store-info i {
    font-size: 2rem;
    color: #96bf48;
}

.ac-store-info h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--ac-text-primary);
    margin: 0;
    font-family: var(--ac-font-heading);
}

/* Score Meter */
.ac-score-meter {
    position: relative;
    margin-bottom: 2rem;
}

.ac-score-circle {
    position: relative;
    display: inline-block;
}

.ac-score-circle svg {
    transform: rotate(-90deg);
}

.ac-score-value {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 2.5rem;
    font-weight: 800;
    color: #ea4335;
    font-family: var(--ac-font-heading);
}

.ac-score-label {
    font-size: 1rem;
    color: var(--ac-text-secondary);
    margin-top: 0.5rem;
}

/* Analysis Body */
.ac-analysis-body {
    padding: 2rem 3rem 3rem;
    animation: fadeIn 0.5s ease 0.3s both;
}

/* Executive Summary */
.ac-executive-summary {
    background: #f0f8ff;
    border: 1px solid #4285f4;
    border-radius: var(--ac-radius-sm);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.ac-executive-summary h4 {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 1rem;
    font-family: var(--ac-font-heading);
    color: #1a73e8;
}

.ac-summary-content p {
    line-height: 1.6;
    margin-bottom: 0.75rem;
    color: var(--ac-text-secondary);
}

.ac-summary-content p:last-child {
    margin-bottom: 0;
}

.ac-summary-content strong {
    color: var(--ac-text-primary);
    font-weight: 600;
}

/* Performance Benchmarks */
.ac-benchmarks {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: var(--ac-radius-sm);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.ac-benchmarks h4 {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    font-family: var(--ac-font-heading);
}

.ac-benchmark-grid {
    display: grid;
    gap: 1.5rem;
}

.ac-benchmark-item {
    display: grid;
    gap: 0.5rem;
}

.ac-benchmark-label {
    font-weight: 600;
    font-size: 0.938rem;
    color: var(--ac-text-primary);
}

.ac-benchmark-bar {
    width: 100%;
    height: 8px;
    background: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
}

.ac-benchmark-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 1s ease;
}

.ac-benchmark-values {
    display: flex;
    justify-content: space-between;
    font-size: 0.813rem;
}

.ac-your-value {
    font-weight: 600;
    color: #ea4335;
}

.ac-industry-value {
    color: var(--ac-text-muted);
}

/* PDF Report Styling */
.ac-report-watermark {
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 0.75rem;
    color: #999;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    opacity: 0.5;
}

.ac-page-number {
    position: absolute;
    bottom: 1rem;
    right: 1.5rem;
    font-size: 0.75rem;
    color: #666;
}

/* Enhanced Loading State */
.ac-loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid var(--ac-bg-light);
    border-top-color: var(--ac-grove);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1.5rem;
}

/* Report Generation Progress */
.ac-generation-progress {
    width: 300px;
    height: 4px;
    background: #e0e0e0;
    border-radius: 2px;
    margin: 1rem auto;
    overflow: hidden;
}

.ac-progress-bar {
    height: 100%;
    background: var(--ac-grove);
    border-radius: 2px;
    width: 0%;
    animation: progressFill 2.5s ease-out forwards;
}

@keyframes progressFill {
    to { width: 100%; }
}

.ac-issues-found {
    margin-bottom: 2.5rem;
}

.ac-issues-found h4 {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    font-family: var(--ac-font-heading);
}

/* PDF Report Date */
.ac-report-date {
    font-size: 0.813rem;
    color: #666;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

/* Issue Items - PDF Style */
.ac-issue-item {
    display: flex;
    gap: 1rem;
    padding: 1.25rem;
    margin-bottom: 1rem;
    border-radius: var(--ac-radius-sm);
    background: #f9f9f9;
    border: 1px solid #e0e0e0;
    transition: all 0.3s ease;
    animation: slideInLeft 0.5s ease both;
    position: relative;
}

.ac-issue-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    border-radius: var(--ac-radius-sm) 0 0 var(--ac-radius-sm);
}

.ac-issue-item:nth-child(1) { animation-delay: 0.1s; }
.ac-issue-item:nth-child(2) { animation-delay: 0.2s; }
.ac-issue-item:nth-child(3) { animation-delay: 0.3s; }
.ac-issue-item:nth-child(4) { animation-delay: 0.4s; }
.ac-issue-item:nth-child(5) { animation-delay: 0.5s; }
.ac-issue-item:nth-child(6) { animation-delay: 0.6s; }

@keyframes slideInLeft {
    from {
        transform: translateX(-20px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.ac-issue-item.error::before {
    background: #ea4335;
}

.ac-issue-item.warning::before {
    background: #fbbc04;
}

.ac-issue-item.info::before {
    background: #4285f4;
}

.ac-issue-item > i {
    font-size: 1.25rem;
    flex-shrink: 0;
    margin-top: 0.125rem;
}

.ac-issue-item.error > i { color: #ea4335; }
.ac-issue-item.warning > i { color: #fbbc04; }
.ac-issue-item.info > i { color: #4285f4; }

.ac-issue-content strong {
    display: block;
    font-weight: 600;
    color: var(--ac-text-primary);
    margin-bottom: 0.25rem;
}

.ac-issue-content p {
    margin: 0;
    font-size: 0.875rem;
    color: var(--ac-text-secondary);
    line-height: 1.5;
}

/* Opportunity Summary */
.ac-opportunity-summary {
    margin-bottom: 2rem;
    animation: fadeIn 0.5s ease 0.7s both;
}

/* Competitor Comparison Table */
.ac-competitor-comparison {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: var(--ac-radius-sm);
    padding: 1.5rem;
    margin-bottom: 2rem;
    animation: fadeIn 0.5s ease 0.8s both;
}

.ac-competitor-comparison h4 {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 1rem;
    font-family: var(--ac-font-heading);
}

.ac-comparison-table {
    border: 1px solid #e0e0e0;
    border-radius: var(--ac-radius-sm);
    overflow: hidden;
    margin-bottom: 0.75rem;
}

.ac-comparison-row {
    display: grid;
    grid-template-columns: 2fr 1.5fr 1.5fr 1fr;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e0e0e0;
    font-size: 0.875rem;
}

.ac-comparison-row:last-child {
    border-bottom: none;
}

.ac-comparison-row.header {
    background: #f5f5f5;
    font-weight: 600;
    color: var(--ac-text-primary);
}

.ac-comp-metric {
    color: var(--ac-text-secondary);
}

.ac-comp-you {
    font-weight: 600;
    text-align: center;
}

.ac-comp-you.bad {
    color: #ea4335;
}

.ac-comp-avg {
    text-align: center;
    color: var(--ac-grove);
    font-weight: 600;
}

.ac-comp-gap {
    text-align: center;
    color: #ea4335;
    font-weight: 600;
}

.ac-comparison-note {
    font-size: 0.75rem;
    color: var(--ac-text-muted);
    font-style: italic;
    margin: 0;
}

.ac-opportunity-summary h4 {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 1rem;
    font-family: var(--ac-font-heading);
}

.ac-opportunity-box {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.05) 0%, rgba(34, 197, 94, 0.02) 100%);
    border: 2px solid rgba(34, 197, 94, 0.3);
    border-radius: var(--ac-radius);
    padding: 1.5rem;
    display: flex;
    justify-content: space-around;
    text-align: center;
}

.ac-opportunity-stat {
    flex: 1;
}

.ac-opp-value {
    font-size: 2rem;
    font-weight: 800;
    color: var(--ac-grove);
    font-family: var(--ac-font-heading);
    line-height: 1;
    margin-bottom: 0.25rem;
}

.ac-opp-label {
    font-size: 0.875rem;
    color: var(--ac-text-secondary);
}

/* Analysis CTA */
.ac-analysis-cta {
    text-align: center;
    padding: 2rem;
    background: var(--ac-bg-light);
    border-radius: var(--ac-radius);
    animation: fadeIn 0.5s ease 0.9s both;
}

.ac-analysis-cta h4 {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    font-family: var(--ac-font-heading);
}

.ac-analysis-cta p {
    color: var(--ac-text-secondary);
    margin-bottom: 1.5rem;
}

.ac-cta-note {
    font-size: 0.813rem;
    color: var(--ac-text-muted);
    margin-top: 0.75rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    /* PDF Report Mobile */
    .ac-popup-content {
        max-width: 100%;
        width: 100%;
        height: 100%;
        max-height: 100%;
        border-radius: 0;
    }
    
    .ac-pdf-document {
        margin: 0.5rem;
    }
    
    .ac-analysis-header {
        padding: 2rem 1.5rem 0;
        margin: 0 1.5rem;
    }
    
    .ac-analysis-body {
        padding: 1.5rem;
    }
    
    .ac-comparison-table {
        font-size: 0.75rem;
    }
    
    .ac-comparison-row {
        grid-template-columns: 2fr 1fr 1fr 0.75fr;
        padding: 0.5rem;
    }
    
    .ac-benchmark-grid {
        gap: 1rem;
    }
    .ac-hero-grid {
        grid-template-columns: 1fr;
        gap: 3rem;
    }
    
    .ac-compact-automation-container {
        min-height: 450px;
    }
    
    /* Adjust stacked positioning for mobile */
    .ac-compact-card:nth-child(1) { top: 0; }
    .ac-compact-card:nth-child(2) { top: 70px; }
    .ac-compact-card:nth-child(3) { top: 140px; }
    .ac-compact-card:nth-child(4) { top: 210px; }
    .ac-compact-card:nth-child(5) { top: 280px; }
    .ac-compact-card:nth-child(6) { top: 350px; }
    
    .ac-compact-card {
        padding: 0.875rem 1rem;
    }
    
    .ac-compact-info h4 {
        font-size: 0.938rem;
    }
    
    .ac-compact-info p {
        font-size: 0.75rem;
    }
    
    .ac-mini-toggle {
        width: 52px;
        height: 30px;
    }
    
    .ac-mini-slider:before {
        height: 24px;
        width: 24px;
    }
    
    .ac-mini-toggle input:checked + .ac-mini-slider:before {
        transform: translateX(22px);
    }
    
    .ac-hero-visual {
        order: -1;
    }
    
    /* Results section mobile */
    .ac-metric-cards {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
    
    .ac-metric-card {
        padding: 1rem;
    }
    
    .ac-metric-value {
        font-size: 1.5rem;
    }
    
    /* Timeline mobile */
    .ac-timeline-container {
        padding: 1.5rem;
    }
    
    .ac-timeline-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1.5rem;
    }
    
    .ac-timeline-selector {
        width: 100%;
        justify-content: center;
    }
    
    .ac-timeline-content {
        flex-direction: column;
        gap: 1.5rem;
        padding: 0;
    }
    
    .ac-timeline-track {
        display: none;
    }
    
    .ac-timeline-milestone {
        margin-bottom: 1.5rem;
    }
    
    .ac-timeline-dot {
        position: static;
        margin: 0 auto 1rem;
        transform: none;
    }
    
    .ac-timeline-card {
        margin-top: 0;
    }
    
    .ac-timeline-card::before {
        display: none;
    }
    
    .ac-timeline-metrics {
        gap: 0.5rem;
    }
    
    .ac-autopilot-badge {
        margin-top: 1.5rem;
        padding: 0.875rem 1.5rem;
        font-size: 0.875rem;
    }
    
    .ac-browser-mockup {
        transform: none;
    }
    
    .ac-metrics {
        grid-template-columns: 1fr;
    }
    
    .ac-features-grid {
        grid-template-columns: 1fr;
    }
    
    .ac-footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .ac-nav-menu {
        display: none;
    }
    
    /* Comparison section mobile */
    .ac-comparison-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .ac-comparison-card {
        padding: 1.5rem;
    }
    
    .ac-comparison-best {
        transform: scale(1);
        order: -1;
    }
    
    .ac-comparison-header h3 {
        font-size: 1.25rem;
    }
    
    /* Pricing section mobile */
    .ac-pricing-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .ac-pricing-featured {
        transform: scale(1);
        order: -1;
    }
    
    .ac-price-amount {
        font-size: 2.5rem;
    }
    
    /* ROI Calculator mobile */
    .ac-roi-calculator {
        padding: 2rem 1.5rem;
    }
    
    .ac-roi-inputs {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .ac-roi-breakdown {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .ac-roi-item-value {
        font-size: 1.5rem;
    }
    
    .ac-roi-total-value {
        font-size: 2.5rem;
    }
    
    .ac-traffic-slider {
        height: 10px;
    }
    
    .ac-roi-select {
        font-size: 0.875rem;
        padding: 0.75rem 1rem;
    }
    
    /* CTA sections mobile */
    .ac-cta-buttons {
        flex-direction: column;
        width: 100%;
    }
    
    .ac-btn {
        width: 100%;
        justify-content: center;
    }
    
    .ac-cta-stats {
        font-size: 0.813rem;
    }
    
    /* General mobile adjustments */
    .ac-section-title {
        font-size: 2rem;
    }
    
    .ac-section-subtitle {
        font-size: 1.125rem;
    }
    
    .ac-hero h1 {
        font-size: 2rem !important;
    }
    
    .ac-hero-subtitle {
        font-size: 1.125rem;
    }
    
    .ac-trust-badges {
        flex-wrap: wrap;
        gap: 1rem;
        justify-content: center;
    }
    
    .ac-badge {
        font-size: 0.813rem;
    }
    
    .ac-badge span {
        font-size: 0.75rem;
    }
    
    .ac-shopify-hint {
        font-size: 0.813rem;
    }
    
    .ac-hand-arrow {
        width: 35px;
        height: 40px;
    }
}

/* Global Lexend Font Application */
/* Ensure ALL form elements and buttons use Lexend font across the entire site */
input[type="text"],
input[type="email"],
input[type="url"],
input[type="password"],
input[type="search"],
input[type="tel"],
input[type="number"],
input[type="date"],
input[type="datetime-local"],
input[type="month"],
input[type="week"],
input[type="time"],
textarea,
select,
button,
.btn,
.form-control,
.form-select,
input.form-control,
select.form-select {
    font-family: var(--ac-font-heading) !important;
}
