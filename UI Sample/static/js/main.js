// Theme Management
const ThemeManager = {
    STORAGE_KEY: 'theme-preference',
    THEMES: {
        LIGHT: 'light',
        DARK: 'dark',
        AUTO: 'auto'
    },
    
    init() {
        this.currentTheme = this.getSavedTheme() || this.THEMES.AUTO;
        this.applyTheme();
        this.setupEventListeners();
    },
    
    getSavedTheme() {
        return localStorage.getItem(this.STORAGE_KEY);
    },
    
    saveTheme(theme) {
        localStorage.setItem(this.STORAGE_KEY, theme);
    },
    
    applyTheme() {
        const root = document.documentElement;
        root.classList.remove('theme-light', 'theme-dark');
        
        if (this.currentTheme === this.THEMES.LIGHT) {
            root.classList.add('theme-light');
        } else if (this.currentTheme === this.THEMES.DARK) {
            root.classList.add('theme-dark');
        }
        // AUTO mode uses system preference (no class needed)
        
        this.updateToggleButton();
    },
    
    updateToggleButton() {
        const toggleBtn = document.getElementById('theme-toggle');
        if (!toggleBtn) return;
        
        const icon = toggleBtn.querySelector('i');
        const text = toggleBtn.querySelector('.theme-text');
        
        if (this.currentTheme === this.THEMES.LIGHT) {
            icon.className = 'fas fa-sun';
            if (text) text.textContent = 'Light';
        } else if (this.currentTheme === this.THEMES.DARK) {
            icon.className = 'fas fa-moon';
            if (text) text.textContent = 'Dark';
        } else {
            icon.className = 'fas fa-adjust';
            if (text) text.textContent = 'Auto';
        }
    },
    
    toggleTheme() {
        // Cycle through: auto -> light -> dark -> auto
        if (this.currentTheme === this.THEMES.AUTO) {
            this.currentTheme = this.THEMES.LIGHT;
        } else if (this.currentTheme === this.THEMES.LIGHT) {
            this.currentTheme = this.THEMES.DARK;
        } else {
            this.currentTheme = this.THEMES.AUTO;
        }
        
        this.saveTheme(this.currentTheme);
        this.applyTheme();
    },
    
    setupEventListeners() {
        // Theme toggle button
        const toggleBtn = document.getElementById('theme-toggle');
        if (toggleBtn) {
            toggleBtn.addEventListener('click', () => this.toggleTheme());
        }
        
        // Listen for system theme changes
        if (window.matchMedia) {
            window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', () => {
                if (this.currentTheme === this.THEMES.AUTO) {
                    this.applyTheme();
                }
            });
        }
    }
};

// Main JavaScript file
document.addEventListener('DOMContentLoaded', function() {
    // Initialize theme manager
    ThemeManager.init();
    
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });
    
    // Clean theme-aware optimization
    function fixDarkModeOptimization() {
        // Remove all forced inline styles
        const elementsWithForcedStyles = document.querySelectorAll('[style*="color"], [style*="background"]');
        elementsWithForcedStyles.forEach(el => {
            // Only remove our forced styles, not original inline styles
            if (el.hasAttribute('data-theme-fixed')) {
                el.removeAttribute('style');
                el.removeAttribute('data-theme-fixed');
            }
        });
        
        // Apply theme-aware fixes using CSS classes instead of inline styles
        const problematicElements = document.querySelectorAll(
            '.optimization-details, .stat-item, .tag-item, .translation-item, ' +
            '.progress-section, .tags-available, td, th, .stat-card, .metric-card, ' +
            '.tech-card, .card-body'
        );
        
        problematicElements.forEach(el => {
            el.classList.add('theme-aware-bg');
        });
        
        // Fix inline style backgrounds
        const inlineBackgrounds = document.querySelectorAll('[style*="background"]');
        inlineBackgrounds.forEach(el => {
            if (el.style.backgroundColor || el.style.background) {
                el.setAttribute('data-original-bg', el.style.background || el.style.backgroundColor);
                el.classList.add('theme-aware-bg');
            }
        });
        
        // Specific fix for search console table cells
        const isDarkMode = document.documentElement.classList.contains('theme-dark');
        if (isDarkMode) {
            const searchConsoleTable = document.querySelector('.search-console-table');
            if (searchConsoleTable) {
                // Force all cells to have transparent background
                const allCells = searchConsoleTable.querySelectorAll('td, th');
                allCells.forEach(cell => {
                    cell.style.setProperty('background-color', 'transparent', 'important');
                    cell.style.setProperty('background', 'transparent', 'important');
                    cell.style.setProperty('color', 'var(--text-primary)', 'important');
                });
            }
        }
    }
    
    // No longer needed - using CSS classes instead
    function resetLightModeStyles() {
        // Placeholder - theme switching is now handled by CSS
    }
    
    // Run fix on page load
    fixDarkModeOptimization();
    
    // Run fix after any dynamic content loads
    const observer = new MutationObserver(fixDarkModeOptimization);
    observer.observe(document.body, { childList: true, subtree: true });
    
    // Also run when theme changes
    const originalToggle = ThemeManager.toggleTheme;
    ThemeManager.toggleTheme = function() {
        originalToggle.call(ThemeManager);
        setTimeout(fixDarkModeOptimization, 100);
    };
    
    // Also run when theme is applied
    const originalApply = ThemeManager.applyTheme;
    ThemeManager.applyTheme = function() {
        originalApply.call(ThemeManager);
        setTimeout(fixDarkModeOptimization, 100);
    };
});