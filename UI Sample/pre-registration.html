<!DOCTYPE html>
<html lang="en">

<!-- Mirrored from seogrove.ai/pre-registration by HTTrack Website Copier/3.x [XR&CO'2014], Wed, 09 Jul 2025 16:39:39 GMT -->
<!-- Added by HTTrack --><meta http-equiv="content-type" content="text/html;charset=utf-8" /><!-- /Added by HTTrack -->
<head>
    <!-- Google Tag Manager -->
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    '../www.googletagmanager.com/gtm5445.html?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-5CMNG3PG');</script>
    <!-- End Google Tag Manager -->
    
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Get Early Access - Shopify AI SEO Automation | Grove - AI-Powered Shopify SEO</title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="Join the waitlist for early access to Grove&#39;s AI-powered Shopify SEO automation. Be among the first to transform your store with intelligent optimization.">
    <meta name="keywords" content="shopify seo early access, ai seo automation, grove pre-registration, shopify optimization waitlist">
    <meta name="author" content="Grove">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="pre-registration.html">
    
    <!-- Open Graph Tags -->
    <meta property="og:type" content="website">
    <meta property="og:title" content="Get Early Access - Shopify AI SEO Automation | Grove">
    <meta property="og:description" content="Join the waitlist for early access to Grove&#39;s AI-powered Shopify SEO automation. Be among the first to transform your store with intelligent optimization.">
    <meta property="og:image" content="https://seogrove.ai/static/images/og-grove.jpg">
    <meta property="og:url" content="https://seogrove.ai/pre-registration">
    <meta property="og:site_name" content="Grove">
    <meta property="og:locale" content="en_US">
    
    <!-- Twitter Card Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Get Early Access - Shopify AI SEO Automation | Grove">
    <meta name="twitter:description" content="Join the waitlist for early access to Grove&#39;s AI-powered Shopify SEO automation. Be among the first to transform your store with intelligent optimization.">
    <meta name="twitter:image" content="https://seogrove.ai/static/images/og-grove.jpg">
    <meta name="twitter:site" content="@Grove">
    
    <!-- Additional SEO Tags -->
    <meta name="theme-color" content="#ef2b70">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    
    <!-- Preconnect to External Resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com/">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin>
    <link rel="preconnect" href="https://cdn.jsdelivr.net/">
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="32x32" href="static/favicon-32x32.html">
    <link rel="icon" type="image/png" sizes="16x16" href="static/favicon-16x16.html">
    <link rel="apple-touch-icon" href="static/apple-touch-icon.html">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com/">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lexend:wght@400;500;600;700;800;900&amp;family=Inter:wght@400;500;600;700;800&amp;display=swap" rel="stylesheet">
    
    <!-- CSS Resources -->
    <link href="../cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="static/css/adcreative-style.css" rel="stylesheet">
    <link href="static/css/mega-menu.css" rel="stylesheet">
    <link href="static/css/mobile-optimization.css" rel="stylesheet">
    
    <!-- Logo styles -->
    <style>
        .ac-logo {
            display: flex;
            align-items: center;
        }
        .ac-logo img {
            display: block;
            object-fit: contain;
        }
        
        /* Simple Footer Styles */
        .footer {
            background: #f8f9fa;
            color: #1e293b;
            padding: 4rem 0 2rem;
            border-top: 1px solid #e2e8f0;
        }
        
        .footer-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }
        
        .footer-brand {
            text-align: center;
            margin-bottom: 3rem;
            padding-bottom: 3rem;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .footer-brand img {
            height: 44px;
            margin-bottom: 1rem;
        }
        
        .footer-brand p {
            color: #64748b;
            font-size: 1.125rem;
            margin: 0;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .footer-columns {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 3rem;
            margin-bottom: 3rem;
            padding-bottom: 3rem;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .footer-column h4 {
            color: #1e293b;
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin: 0 0 1.5rem;
        }
        
        .footer-column ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .footer-column li {
            margin-bottom: 0.75rem;
        }
        
        .footer-column a {
            color: #64748b;
            text-decoration: none;
            transition: color 0.2s;
        }
        
        .footer-column a:hover {
            color: #ef2b70;
        }
        
        .footer-login {
            color: #22c55e !important;
            font-weight: 500;
        }
        
        .footer-social {
            display: flex;
            gap: 0.75rem;
            margin-top: 1rem;
        }
        
        .social-link {
            width: 36px;
            height: 36px;
            background: #ffffff;
            border: 1px solid #e2e8f0;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #64748b;
            transition: all 0.2s;
        }
        
        .social-link:hover {
            background: #ef2b70;
            border-color: #ef2b70;
            color: #fff;
        }
        
        .footer-bottom {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .footer-copyright {
            color: #64748b;
            font-size: 0.875rem;
            margin: 0;
        }
        
        .footer-tagline {
            color: #64748b;
            font-size: 0.875rem;
            margin: 0;
            font-style: italic;
        }
        
        .footer-tagline i {
            color: #22c55e;
            margin-right: 0.5rem;
        }
        
        @media (max-width: 768px) {
            .footer-columns {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
            .footer-bottom {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }
        }
    </style>
    
    <!-- Schema.org JSON-LD -->
    
    
    
<link href="static/css/adcreative-style.css" rel="stylesheet">
<link href="static/css/ai-rounded-light.css" rel="stylesheet">
<style>
/* Import ONLY the console dot styles from ai-automation-styles.css */
.ac-console-dots {
    display: flex;
    gap: 0.5rem;
}

.ac-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.ac-dot-red {
    background: #ff5f57;
}

.ac-dot-yellow {
    background: #ffbd2e;
}

.ac-dot-green {
    background: #28ca42;
}

/* Keep the dark text for agent names */
.ac-agent-name {
    color: #1f2937 !important;
}
</style>
<style>
/* Import brand colors and fonts */
:root {
    --grove-pink: #ef2b70;
    --grove-pink-dark: #d91a5f;
    --grove-pink-light: #ff4085;
    --grove-green: #22c55e;
    --grove-green-dark: #16a34a;
    --grove-green-light: #4ade80;
    --ac-primary: #ef2b70;  /* Pink accent color */
    --ac-font-heading: 'Lexend', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica', 'Arial', sans-serif;
    --ac-bg-white: #ffffff;
}

/* Sticky Navigation Override */
body {
    padding-top: 80px;
}

.ac-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: var(--ac-bg-white);
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.ac-nav.scrolled {
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    border-bottom-color: rgba(0, 0, 0, 0.08);
}

.ac-nav.scrolled .ac-nav-container {
    height: 72px;
}

/* Hero Section - Matching Homepage Layout */
.pre-reg-hero {
    padding: 2rem 2rem;
    min-height: calc(100vh - 80px);
    display: flex;
    align-items: flex-start;
    background: linear-gradient(180deg, #ffffff 0%, #f8f9fb 100%);
    position: relative;
    overflow: hidden;
}

/* Hero Blob - Matching Homepage */
.pre-reg-blob {
    position: absolute;
    top: -300px;
    right: -100px;
    width: 690px;
    height: 690px;
    background: linear-gradient(135deg, 
        rgba(239, 43, 112, 0.08) 0%, 
        rgba(239, 43, 112, 0.05) 50%,
        transparent 100%);
    border: 1px solid rgba(239, 43, 112, 0.1);
    border-radius: 40% 60% 60% 40% / 60% 40% 60% 40%;
    animation: morph-blob-hero 12s ease-in-out infinite;
    z-index: 0;
    filter: blur(0.5px);
    pointer-events: none;
    will-change: border-radius;
    transform-style: preserve-3d;
    backface-visibility: hidden;
}

@keyframes morph-blob-hero {
    0%, 100% { 
        border-radius: 40% 60% 60% 40% / 60% 40% 60% 40%;
    }
    25% { 
        border-radius: 60% 40% 40% 60% / 40% 60% 40% 60%;
    }
    50% { 
        border-radius: 40% 60% 60% 40% / 40% 60% 60% 40%;
    }
    75% { 
        border-radius: 60% 40% 60% 40% / 40% 60% 40% 60%;
    }
}

/* Hero Container */
.pre-reg-container {
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
    position: relative;
    z-index: 1;
}

.pre-reg-grid {
    display: grid;
    grid-template-columns: 1.2fr 1fr;
    gap: 5rem;
    align-items: start;
    padding-top: 2rem;
}

/* Left Content */
.pre-reg-content {
    padding-right: 2rem;
}

.pre-reg-content h1 {
    font-family: 'Lexend', sans-serif;
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    line-height: 1.1;
    color: var(--ac-text-primary);
    margin-bottom: 1.5rem;
}

.pink-highlight {
    color: var(--grove-pink);
}

.pre-reg-subtitle {
    font-size: 1.375rem;
    color: var(--ac-text-secondary);
    margin-bottom: 2.5rem;
    line-height: 1.7;
}

.pre-reg-subtitle strong {
    color: var(--ac-primary);
    font-weight: 700;
}

/* Trust Badges */
.trust-badges {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 2rem;
    flex-wrap: nowrap;
}

.trust-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--ac-text-secondary);
    font-size: 0.875rem;
    white-space: nowrap;
}

.trust-badge i {
    color: var(--grove-green);
}

/* Animated Automation Cards - Compact Tiles */
.automation-cards {
    margin-top: 2rem;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.automation-card {
    background: white;
    border-radius: 12px;
    padding: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.06);
}

.automation-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--grove-pink) 0%, var(--grove-pink-light) 100%);
    transform: translateX(-100%);
    transition: transform 0.5s ease;
}

.automation-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 20px rgba(239, 43, 112, 0.12);
    border-color: var(--grove-pink);
}

.automation-card:hover::before {
    transform: translateX(0);
}

.automation-header {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.automation-title {
    font-family: 'Lexend', sans-serif;
    font-size: 0.875rem;
    font-weight: 700;
    color: var(--ac-text-primary);
    display: flex;
    align-items: center;
    gap: 0.375rem;
}

.automation-title i {
    color: var(--grove-pink);
    font-size: 0.875rem;
}

.automation-status {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    background: rgba(34, 197, 94, 0.1);
    padding: 0.125rem 0.5rem;
    border-radius: 9999px;
    color: var(--grove-green);
    font-size: 0.625rem;
    font-weight: 600;
    align-self: flex-start;
}

.automation-status i {
    font-size: 0.625rem;
    animation: spin 2s linear infinite;
}

.automation-card:nth-child(1) { animation-delay: 0s; }
.automation-card:nth-child(2) { animation-delay: 0.2s; }
.automation-card:nth-child(3) { animation-delay: 0.4s; }

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.automation-description {
    color: var(--ac-text-secondary);
    font-size: 0.75rem;
    line-height: 1.4;
    margin: 0;
}

.automation-metric {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    margin-top: 0.5rem;
    color: var(--grove-pink);
    font-size: 0.75rem;
    font-weight: 600;
}

/* Right Form Container */
.form-panel {
    background: white;
    border-radius: 24px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    position: relative;
    overflow: hidden;
    width: 100%;
}

.form-panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--grove-pink);
}

.form-container {
    padding: 2.5rem 3rem;
}

/* Progress Steps */
.progress-steps {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2rem;
    position: relative;
}

.progress-steps::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--ac-bg-gray);
    z-index: 0;
}

.step {
    position: relative;
    z-index: 1;
    text-align: center;
    flex: 1;
}

.step-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--ac-bg-gray);
    margin: 0 auto 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    transition: all 0.3s ease;
}

.step.active .step-circle {
    background: linear-gradient(135deg, var(--grove-pink) 0%, var(--grove-pink-dark) 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(239, 43, 112, 0.3);
}

.step.completed .step-circle {
    background: var(--ac-grove-light);
    color: white;
}

.step-label {
    font-size: 0.875rem;
    color: var(--ac-text-muted);
    font-weight: 500;
}

.step.active .step-label {
    color: var(--ac-text-primary);
}

/* Form Elements */
.form-step {
    display: none;
}

.form-step.active {
    display: block;
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.form-group {
    margin-bottom: 1.25rem;
}

.form-label {
    display: block;
    color: var(--ac-text-primary);
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-family: 'Lexend', sans-serif;
}

.form-input {
    width: 100%;
    padding: 0.875rem 1.25rem;
    font-size: 1rem;
    font-family: 'Inter', sans-serif;
    border: 2px solid var(--ac-bg-gray);
    border-radius: 12px;
    transition: all 0.3s ease;
    background: white;
}

.form-input:focus {
    outline: none;
    border-color: var(--grove-pink);
    box-shadow: 0 0 0 4px rgba(239, 43, 112, 0.1);
}

.form-input::placeholder {
    color: var(--ac-text-muted);
}

.form-select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg width='12' height='8' viewBox='0 0 12 8' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1 1L6 6L11 1' stroke='%239691ad' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 1rem center;
    padding-right: 2.5rem;
}

/* Stripe Card Element Styling */
#card-element {
    transition: all 0.3s ease;
}

#card-element.StripeElement--focus {
    border-color: var(--grove-pink);
    box-shadow: 0 0 0 4px rgba(239, 43, 112, 0.1);
}

#card-element.StripeElement--invalid {
    border-color: #ef4444;
}

#card-element.StripeElement--complete {
    border-color: #22c55e;
}

/* Buttons */
.btn-group {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

.btn {
    padding: 0.875rem 2rem;
    font-size: 1rem;
    font-weight: 600;
    font-family: 'Lexend', sans-serif;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: linear-gradient(135deg, var(--grove-pink) 0%, var(--grove-pink-dark) 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(239, 43, 112, 0.3);
    position: relative;
    overflow: hidden;
}

.btn-primary::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.2);
    transform: translate(-50%, -50%) scale(0);
    border-radius: 50%;
    transition: transform 0.6s ease;
}

.btn-primary:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 6px 20px rgba(239, 43, 112, 0.4);
}

.btn-primary:hover::after {
    transform: translate(-50%, -50%) scale(2);
}

.btn-secondary {
    background: white;
    color: var(--ac-text-secondary);
    border: 2px solid var(--ac-bg-gray);
}

.btn-secondary:hover {
    border-color: var(--ac-grove);
    color: var(--ac-grove);
}

/* Benefits List */
.benefits-card {
    background: var(--ac-bg-light);
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.benefits-title {
    font-family: 'Lexend', sans-serif;
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--ac-text-primary);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.benefits-title i {
    color: var(--ac-primary);
}

.benefits-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.benefit-item {
    display: flex;
    align-items: start;
    gap: 1rem;
    margin-bottom: 1rem;
    color: var(--ac-text-secondary);
}

.benefit-icon {
    flex-shrink: 0;
    width: 24px;
    height: 24px;
    background: var(--ac-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.75rem;
}

/* Urgency Banner */
.urgency-banner {
    background: linear-gradient(135deg, rgba(239, 43, 112, 0.1) 0%, rgba(239, 43, 112, 0.05) 100%);
    border: 1px solid rgba(239, 43, 112, 0.2);
    border-radius: 12px;
    padding: 1rem;
    margin: 1.5rem 0;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    animation: urgencyPulse 2s ease-in-out infinite;
}

@keyframes urgencyPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
}

.urgency-banner i {
    color: var(--grove-pink);
    font-size: 1.25rem;
}

.urgency-text {
    color: var(--grove-pink-dark);
    font-size: 0.875rem;
    font-weight: 600;
}

/* Success State */
.success-container {
    text-align: center;
    padding: 3rem;
    display: none;
}

.success-container.active {
    display: block;
    animation: scaleIn 0.5s ease;
}

@keyframes scaleIn {
    from { transform: scale(0.9); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

.success-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--ac-grove) 0%, var(--ac-grove-dark) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    font-size: 2.5rem;
    color: white;
    box-shadow: 0 8px 24px rgba(34, 197, 94, 0.3);
}

.referral-card {
    background: var(--ac-bg-light);
    border-radius: 16px;
    padding: 2rem;
    margin: 2rem 0;
}

.referral-code {
    font-family: 'Lexend', sans-serif;
    font-size: 2rem;
    font-weight: 700;
    color: var(--ac-grove);
    margin: 1rem 0;
    letter-spacing: 2px;
}

.share-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

.share-btn {
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-size: 0.875rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.share-twitter {
    background: #1DA1F2;
    color: white;
}

.share-linkedin {
    background: #0077B5;
    color: white;
}

.share-email {
    background: var(--ac-text-secondary);
    color: white;
}

.share-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

/* Section Container - Matches homepage */
.ac-section-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* What You Get Section */
.ac-what-you-get-section {
    padding: 5rem 0;
    background: linear-gradient(180deg, #ffffff 0%, #f8f9fb 100%);
    position: relative;
    overflow: hidden;
}

.ac-what-you-get-section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -20%;
    width: 600px;
    height: 600px;
    background: radial-gradient(circle, rgba(34, 197, 94, 0.08) 0%, transparent 70%);
    border-radius: 50%;
}

.ac-section-header {
    text-align: center;
    margin-bottom: 3rem;
    position: relative;
    z-index: 1;
}

.ac-section-header h2 {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 0.75rem;
}

.ac-section-header .ac-subtitle {
    font-size: 1.25rem;
    color: var(--ac-text-secondary);
}

.ac-features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
    position: relative;
    z-index: 1;
}

.ac-feature-card {
    background: white;
    border-radius: 20px;
    padding: 2.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(0, 0, 0, 0.04);
    position: relative;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.ac-feature-card-hover:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
    border-color: var(--grove-pink);
}

.ac-feature-icon {
    width: 56px !important;
    height: 56px !important;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    background: transparent !important;
    border: none !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    overflow: visible;
    flex-shrink: 0;
}

/* Blob background - matching dashboard style */
.ac-feature-icon::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, 
        currentColor 0%, 
        transparent 60%);
    border: 1px solid currentColor;
    opacity: 0;
    border-radius: 40% 60% 60% 40% / 60% 40% 60% 40%;
    filter: blur(0.5px);
    transform: translate(-50%, -50%) scale(0.8);
    animation: morph-blob 8s ease-in-out infinite;
    transition: opacity 0.3s ease, transform 0.3s ease, border-color 0.3s ease;
    z-index: -1;
}

/* Hover state - show blob */
.ac-feature-card:hover .ac-feature-icon::before {
    opacity: 0.15;
    transform: translate(-50%, -50%) scale(1);
}

/* Enhance icon on hover */
.ac-feature-card:hover .ac-feature-icon {
    transform: scale(1.05);
}

.ac-feature-icon i {
    position: relative;
    z-index: 2;
    font-size: 1.25rem;
    transition: transform 0.3s ease;
    color: inherit;
}

.ac-feature-card:hover .ac-feature-icon i {
    transform: scale(1.1);
}

/* Icon color classes */
.ac-icon-gradient-pink {
    color: var(--grove-pink);
}

.ac-icon-gradient-green {
    color: var(--grove-green);
}

.ac-icon-gradient-purple {
    color: #8b5cf6;
}

/* Blob morph animation */
@keyframes morph-blob {
    0%, 100% {
        border-radius: 40% 60% 60% 40% / 60% 40% 60% 40%;
    }
    25% {
        border-radius: 60% 40% 40% 60% / 40% 60% 60% 40%;
    }
    50% {
        border-radius: 40% 60% 60% 40% / 40% 60% 60% 40%;
    }
    75% {
        border-radius: 60% 40% 40% 60% / 60% 40% 40% 60%;
    }
}

.ac-feature-title {
    font-family: 'Lexend', sans-serif;
    font-size: 1.375rem;
    font-weight: 700;
    color: var(--ac-text-primary);
    margin-bottom: 1rem;
}

.ac-worker-list,
.ac-perk-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.ac-worker-list li,
.ac-perk-list li {
    font-size: 0.875rem;
    color: var(--ac-text-secondary);
    margin-bottom: 0.75rem;
    line-height: 1.6;
}

.ac-worker-list li strong {
    color: var(--ac-text-primary);
    font-weight: 600;
}

.ac-perk-list li {
    display: flex;
    align-items: start;
    gap: 0.75rem;
}

.ac-perk-list li i {
    color: var(--grove-green);
    font-size: 0.875rem;
    margin-top: 0.125rem;
    flex-shrink: 0;
}

.ac-metrics-list {
    display: grid;
    gap: 0.875rem;
}

.ac-metric-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.875rem;
    background: var(--ac-bg-light);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.ac-metric-item:hover {
    background: var(--ac-bg-gray);
    transform: translateX(4px);
}

.ac-metric-label {
    color: var(--ac-text-secondary);
    font-size: 0.875rem;
}

.ac-metric-value {
    font-weight: 700;
    font-size: 1rem;
    color: var(--ac-text-primary);
}

.ac-text-green {
    color: var(--grove-green) !important;
}

.ac-text-pink {
    color: var(--grove-pink) !important;
}

.ac-feature-badge {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    background: rgba(239, 43, 112, 0.1);
    color: var(--grove-pink);
    padding: 0.375rem 0.875rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.375rem;
}

.ac-badge-green {
    background: rgba(34, 197, 94, 0.1);
    color: var(--grove-green);
}

.ac-badge-purple {
    background: rgba(139, 92, 246, 0.1);
    color: #8b5cf6;
}

/* Comparison Table */
.ac-comparison-card {
    background: white;
    border-radius: 24px;
    padding: 3rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.04);
    margin-top: 3rem;
    position: relative;
    overflow: hidden;
}

.ac-comparison-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--grove-pink);
}

.ac-comparison-title {
    font-family: 'Lexend', sans-serif;
    font-size: 1.75rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 2.5rem;
    color: var(--ac-text-primary);
}

.ac-table-container {
    overflow-x: auto;
    margin: 0 -1rem;
    padding: 0 1rem;
}

.ac-comparison-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9375rem;
}

.ac-comparison-table thead th {
    padding: 1.25rem;
    text-align: center;
    font-weight: 600;
    font-family: 'Lexend', sans-serif;
    border-bottom: 2px solid var(--ac-bg-gray);
}

.ac-table-header-agency {
    color: var(--ac-text-secondary);
}

.ac-table-header-grove {
    color: var(--grove-pink);
    background: linear-gradient(180deg, rgba(239, 43, 112, 0.05) 0%, transparent 100%);
}

.ac-comparison-table tbody tr {
    border-bottom: 1px solid var(--ac-bg-gray);
    transition: background 0.3s ease;
}

.ac-comparison-table tbody tr:hover {
    background: var(--ac-bg-light);
}

.ac-comparison-table tbody tr:last-child {
    border-bottom: none;
}

.ac-table-feature {
    padding: 1.25rem;
    font-weight: 600;
    color: var(--ac-text-primary);
}

.ac-table-agency,
.ac-table-grove {
    padding: 1.25rem;
    text-align: center;
    color: var(--ac-text-secondary);
}

.ac-table-grove {
    font-weight: 700;
    color: var(--ac-text-primary);
    position: relative;
}

.ac-cost-bad {
    color: #ef4444;
    font-weight: 600;
}

.ac-cost-good {
    color: var(--grove-green);
    font-size: 1.25rem;
    font-weight: 800;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
}

.ac-cost-original {
    font-size: 0.875rem;
    color: var(--ac-text-muted);
    text-decoration: line-through;
    font-weight: 400;
}

.ac-savings-badge {
    display: inline-block;
    background: var(--grove-green);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-left: 0.5rem;
}

.ac-multiplier {
    display: block;
    font-size: 0.75rem;
    color: var(--grove-green);
    font-weight: 600;
    margin-top: 0.25rem;
}

.ac-infinity-icon,
.ac-speed-icon,
.ac-shield-icon,
.ac-check-icon {
    color: var(--grove-green);
    margin-left: 0.5rem;
}

/* How It Works Section */
.ac-how-it-works-section {
    padding: 5rem 0;
    background: white;
    position: relative;
}

.ac-process-timeline {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    margin-bottom: 4rem;
    position: relative;
}

.ac-process-step {
    text-align: center;
    position: relative;
}

.ac-step-number {
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, var(--grove-pink) 0%, var(--grove-pink-light) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    position: relative;
    box-shadow: 0 8px 24px rgba(239, 43, 112, 0.3);
    transition: all 0.3s ease;
}

.ac-process-step:hover .ac-step-number {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 12px 32px rgba(239, 43, 112, 0.4);
}

.ac-step-number span {
    color: white;
    font-size: 2.5rem;
    font-weight: 800;
    font-family: 'Lexend', sans-serif;
}

.ac-step-time {
    position: absolute;
    bottom: -12px;
    right: -12px;
    background: var(--grove-green);
    color: white;
    padding: 0.375rem 0.875rem;
    border-radius: 16px;
    font-size: 0.75rem;
    font-weight: 700;
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

.ac-time-continuous {
    background: linear-gradient(135deg, var(--grove-green) 0%, var(--grove-green-dark) 100%);
    animation: badge-pulse 2s ease-in-out infinite;
}

@keyframes badge-pulse {
    0%, 100% { 
        transform: scale(1);
        box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
    }
    50% { 
        transform: scale(1.05);
        box-shadow: 0 6px 16px rgba(34, 197, 94, 0.4);
    }
}

.ac-step-connector {
    position: absolute;
    top: 50px;
    left: 60%;
    right: -60%;
    height: 2px;
    background: linear-gradient(90deg, var(--grove-pink) 0%, transparent 100%);
    opacity: 0.3;
    z-index: -1;
}

.ac-process-step:last-child .ac-step-connector {
    display: none;
}

.ac-step-content {
    max-width: 240px;
    margin: 0 auto;
}

.ac-step-title {
    font-family: 'Lexend', sans-serif;
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--ac-text-primary);
    margin-bottom: 0.5rem;
}

.ac-step-description {
    font-size: 0.875rem;
    color: var(--ac-text-secondary);
    line-height: 1.5;
}

/* Demo Showcase */
.ac-demo-showcase {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border-radius: 24px;
    padding: 4rem;
    text-align: center;
    color: white;
    position: relative;
    overflow: hidden;
}

.ac-demo-showcase::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -30%;
    width: 600px;
    height: 600px;
    background: radial-gradient(circle, rgba(239, 43, 112, 0.2) 0%, transparent 70%);
    border-radius: 50%;
}

.ac-demo-content {
    position: relative;
    z-index: 1;
}

.ac-demo-title {
    font-family: 'Lexend', sans-serif;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.ac-demo-subtitle {
    font-size: 1.25rem;
    opacity: 0.9;
    margin-bottom: 3rem;
}

.ac-video-placeholder {
    background: #000;
    border-radius: 16px;
    padding: 4rem;
    margin-bottom: 3rem;
    position: relative;
    overflow: hidden;
}

.ac-play-button-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.ac-play-button {
    width: 100px;
    height: 100px;
    background: var(--grove-pink);
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 24px rgba(239, 43, 112, 0.4);
}

.ac-play-button:hover {
    transform: scale(1.1);
    box-shadow: 0 12px 32px rgba(239, 43, 112, 0.5);
}

.ac-play-button i {
    color: white;
    font-size: 2rem;
    margin-left: 0.25rem;
}

.ac-video-overlay {
    position: absolute;
    bottom: 1rem;
    left: 50%;
    transform: translateX(-50%);
}

.ac-demo-label {
    font-size: 0.875rem;
    opacity: 0.7;
}

.ac-btn-glow {
    animation: glow 3s ease-in-out infinite;
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 4px 12px rgba(239, 43, 112, 0.3);
    }
    50% {
        box-shadow: 0 8px 24px rgba(239, 43, 112, 0.5);
    }
}

/* Social Proof Section */
.ac-social-proof-section {
    padding: 5rem 0;
    background: var(--ac-bg-light);
    position: relative;
    overflow: hidden;
}

.ac-testimonials-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    margin-bottom: 4rem;
}

.ac-testimonial-card {
    background: white;
    border-radius: 20px;
    padding: 2.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(0, 0, 0, 0.04);
    position: relative;
    transition: all 0.3s ease;
}

.ac-testimonial-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.ac-testimonial-stars {
    display: flex;
    gap: 0.25rem;
    margin-bottom: 1.5rem;
}

.ac-testimonial-stars i {
    color: #fbbf24;
    font-size: 1rem;
}

.ac-testimonial-quote {
    font-size: 1.125rem;
    line-height: 1.7;
    color: var(--ac-text-primary);
    margin-bottom: 2rem;
    font-style: italic;
}

.ac-testimonial-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.ac-testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.ac-author-avatar {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--grove-green) 0%, var(--grove-green-light) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 1rem;
}

.ac-avatar-purple {
    background: linear-gradient(135deg, #8b5cf6 0%, #a78bfa 100%);
}

.ac-avatar-pink {
    background: linear-gradient(135deg, var(--grove-pink) 0%, var(--grove-pink-light) 100%);
}

.ac-author-info {
    text-align: left;
}

.ac-author-name {
    font-family: 'Lexend', sans-serif;
    font-size: 1rem;
    font-weight: 700;
    color: var(--ac-text-primary);
    margin: 0;
}

.ac-author-title {
    font-size: 0.875rem;
    color: var(--ac-text-secondary);
    margin: 0;
}

.ac-testimonial-metric {
    text-align: right;
}

.ac-testimonial-metric .ac-metric-label {
    display: block;
    font-size: 0.75rem;
    color: var(--ac-text-muted);
    margin-bottom: 0.25rem;
}

.ac-testimonial-metric .ac-metric-value {
    display: block;
    font-size: 1.25rem;
    font-weight: 800;
    color: var(--grove-green);
}

.ac-testimonial-badge {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    background: rgba(34, 197, 94, 0.1);
    color: var(--grove-green);
    padding: 0.375rem 0.875rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.375rem;
}

.ac-testimonial-badge.ac-badge-purple {
    background: rgba(139, 92, 246, 0.1);
    color: #8b5cf6;
}

.ac-testimonial-badge.ac-badge-pink {
    background: rgba(239, 43, 112, 0.1);
    color: var(--grove-pink);
}

/* Trust Indicators */
.ac-trust-indicators {
    display: flex;
    justify-content: center;
    gap: 3rem;
    flex-wrap: wrap;
    padding: 2rem;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
}

.ac-trust-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1rem;
    color: var(--ac-text-primary);
    font-weight: 600;
}

.ac-trust-item i {
    font-size: 1.25rem;
    color: var(--grove-green);
}

/* Automation Section Styles */
.ac-automation-section {
    padding: 5rem 0;
    background: linear-gradient(180deg, #f8f9fb 0%, #ffffff 100%);
    position: relative;
    overflow: hidden;
}

.ac-automation-section::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -20%;
    width: 800px;
    height: 800px;
    background: radial-gradient(circle, rgba(239, 43, 112, 0.08) 0%, transparent 70%);
    border-radius: 50%;
}

.ac-features-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    position: relative;
    z-index: 1;
}

.ac-automation-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 3rem;
}

.ac-automation-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(0, 0, 0, 0.04);
    position: relative;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    cursor: pointer;
}

.ac-automation-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
}

.ac-automation-card.active {
    border-color: var(--grove-green);
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.02) 0%, transparent 100%);
}

.ac-automation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.ac-automation-title {
    font-family: 'Lexend', sans-serif;
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--ac-text-primary);
}

/* Toggle Switch */
.ac-automation-toggle {
    position: relative;
    display: inline-block;
    width: 56px;
    height: 32px;
}

.ac-automation-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.ac-toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #e5e7eb;
    transition: .4s;
    border-radius: 34px;
}

.ac-toggle-slider:before {
    position: absolute;
    content: "";
    height: 24px;
    width: 24px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

.ac-automation-toggle input:checked + .ac-toggle-slider {
    background: linear-gradient(135deg, var(--grove-green) 0%, var(--grove-green-light) 100%);
}

.ac-automation-toggle input:checked + .ac-toggle-slider:before {
    transform: translateX(24px);
}

.ac-automation-features {
    list-style: none;
    padding: 0;
    margin: 0 0 1.5rem 0;
}

.ac-automation-feature {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.875rem;
    opacity: 0.8;
    transition: all 0.3s ease;
}

.ac-automation-card.active .ac-automation-feature {
    opacity: 1;
}

.ac-automation-feature-icon {
    width: 36px;
    height: 36px;
    background: var(--ac-bg-light);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--ac-text-secondary);
    transition: all 0.3s ease;
}

.ac-automation-card.active .ac-automation-feature-icon {
    background: rgba(34, 197, 94, 0.1);
    color: var(--grove-green);
}

.ac-automation-feature-icon i {
    font-size: 0.875rem;
}

.ac-automation-feature-text {
    color: var(--ac-text-secondary);
    font-size: 0.875rem;
    line-height: 1.4;
}

.ac-automation-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--ac-text-muted);
}

.ac-automation-status-dot {
    width: 8px;
    height: 8px;
    background: #dc2626;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.ac-automation-card.active .ac-automation-status {
    color: var(--grove-green);
}

.ac-automation-card.active .ac-automation-status-dot {
    background: var(--grove-green);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(34, 197, 94, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
    }
}

/* FAQ Section */
.ac-faq-section {
    padding: 5rem 0;
    background: linear-gradient(180deg, #ffffff 0%, #f5f5f5 100%);
    position: relative;
}

.ac-faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto 4rem;
}

.ac-faq-card {
    background: var(--ac-bg-light);
    padding: 2rem;
    border-radius: 16px;
    border: 1px solid transparent;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.ac-faq-card:hover {
    background: white;
    border-color: var(--grove-pink);
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
}

.ac-faq-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--grove-pink) 0%, var(--grove-pink-light) 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-bottom: 1rem;
    font-size: 1.125rem;
}

.ac-faq-question {
    font-family: 'Lexend', sans-serif;
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--ac-text-primary);
    margin-bottom: 1rem;
}

.ac-faq-answer {
    color: var(--ac-text-secondary);
    line-height: 1.6;
    font-size: 0.9375rem;
}

/* Founding Member Benefits Tiles */
.ac-benefits-tiles {
    margin-top: 3rem;
    padding-top: 3rem;
    border-top: 1px solid rgba(0, 0, 0, 0.06);
}

.ac-tiles-title {
    font-family: 'Lexend', sans-serif;
    font-size: 1.75rem;
    font-weight: 700;
    text-align: center;
    color: var(--ac-text-primary);
    margin-bottom: 2rem;
}

.ac-tiles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.ac-benefit-tile {
    background: var(--ac-bg-light);
    border-radius: 16px;
    padding: 1.5rem;
    border: 1px solid rgba(0, 0, 0, 0.04);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.ac-benefit-tile:hover {
    background: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    border-color: var(--grove-pink);
}

.ac-benefit-tile::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 3px;
    height: 100%;
    background: var(--grove-pink);
    transform: scaleY(0);
    transition: transform 0.3s ease;
    transform-origin: top;
}

.ac-benefit-tile:hover::before {
    transform: scaleY(1);
}

.ac-tile-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.ac-tile-emoji {
    font-size: 1.5rem;
    display: inline-block;
}

.ac-tile-title {
    font-family: 'Lexend', sans-serif;
    font-size: 1rem;
    font-weight: 700;
    color: var(--ac-text-primary);
    margin: 0;
}

.ac-tile-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.ac-tile-list li {
    font-size: 0.875rem;
    color: var(--ac-text-secondary);
    margin-bottom: 0.5rem;
    padding-left: 1.25rem;
    position: relative;
    line-height: 1.4;
}

.ac-tile-list li:last-child {
    margin-bottom: 0;
}

.ac-tile-list li::before {
    content: '\2022';
    position: absolute;
    left: 0;
    color: var(--grove-green);
    font-weight: 700;
    font-size: 1.25rem;
    line-height: 1;
    top: 0;
}

/* Final CTA */
.ac-final-cta {
    text-align: center;
    padding: 4rem;
    background: linear-gradient(135deg, var(--grove-pink) 0%, var(--grove-pink-dark) 100%);
    border-radius: 24px;
    color: white;
    position: relative;
    overflow: hidden;
}

.ac-final-cta::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -30%;
    width: 600px;
    height: 600px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
}

.ac-cta-title {
    font-family: 'Lexend', sans-serif;
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
    position: relative;
    z-index: 1;
}

.ac-cta-subtitle {
    font-size: 1.25rem;
    opacity: 0.95;
    margin-bottom: 2rem;
    position: relative;
    z-index: 1;
}

.ac-btn-xl {
    padding: 1.25rem 3rem;
    font-size: 1.125rem;
}

.ac-cta-disclaimer {
    font-size: 0.875rem;
    opacity: 0.9;
    margin-top: 1rem;
    position: relative;
    z-index: 1;
}

/* Animations */
@keyframes slideInDown {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .ac-features-grid {
        grid-template-columns: 1fr;
        max-width: 600px;
        margin: 0 auto 3rem;
    }
    
    .ac-process-timeline {
        grid-template-columns: repeat(4, 1fr);
        gap: 1.5rem;
    }
    
    .ac-step-number {
        width: 80px;
        height: 80px;
    }
    
    .ac-step-number span {
        font-size: 2rem;
    }
    
    .ac-testimonials-grid {
        grid-template-columns: 1fr;
        max-width: 600px;
        margin: 0 auto 4rem;
    }
    
    .automation-cards {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .ac-automation-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
}

@media (max-width: 968px) {
    .pre-reg-hero {
        padding: 3rem 1rem;
        min-height: auto;
    }
    
    .pre-reg-grid {
        grid-template-columns: 1fr;
        gap: 3rem;
    }
    
    .pre-reg-content {
        padding-right: 0;
        text-align: center;
    }
    
    .trust-badges {
        justify-content: center;
    }
    
    .form-panel {
        max-width: 100%;
        margin: 0 auto;
    }
    
    .automation-cards {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
    
    .ac-comparison-card {
        padding: 2rem 1rem;
    }
    
    .ac-comparison-table {
        font-size: 0.875rem;
    }
    
    .ac-process-timeline {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
    
    .ac-step-connector {
        display: none;
    }
    
    .ac-demo-showcase {
        padding: 3rem 2rem;
    }
    
    .ac-faq-grid {
        grid-template-columns: 1fr;
    }
    
    .ac-automation-grid {
        grid-template-columns: 1fr;
        gap: 1.25rem;
    }
}

@media (max-width: 768px) {
    .ac-process-timeline {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.25rem;
    }
    
    .ac-step-number {
        width: 70px;
        height: 70px;
    }
    
    .ac-step-number span {
        font-size: 1.75rem;
    }
    
    .ac-step-time {
        font-size: 0.625rem;
        padding: 0.25rem 0.5rem;
    }
    
    .ac-step-title {
        font-size: 1rem;
    }
    
    .ac-step-description {
        font-size: 0.813rem;
    }

@media (max-width: 768px) {
    body {
        padding-top: 72px;
    }
    
    .form-container {
        padding: 2rem;
    }
    
    .trust-badges {
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .pre-reg-content h1 {
        font-size: 2rem;
    }
    
    .pre-reg-subtitle {
        font-size: 1.125rem;
    }
    
    .ac-section-title {
        font-size: 2rem;
    }
    
    .ac-feature-card {
        padding: 2rem;
    }
    
    .ac-comparison-title {
        font-size: 1.25rem;
    }
    
    .ac-table-container {
        margin: 0 -2rem;
        padding: 0;
    }
    
    .ac-demo-title {
        font-size: 1.5rem;
    }
    
    .ac-demo-subtitle {
        font-size: 1rem;
    }
    
    .ac-video-placeholder {
        padding: 3rem;
    }
    
    .ac-play-button {
        width: 80px;
        height: 80px;
    }
    
    .ac-testimonial-card {
        padding: 2rem;
    }
    
    .ac-trust-indicators {
        gap: 1.5rem;
        padding: 1.5rem;
    }
    
    .ac-trust-item {
        font-size: 0.875rem;
    }
    
    .ac-final-cta {
        padding: 3rem 2rem;
    }
    
    .ac-cta-title {
        font-size: 1.75rem;
    }
    
    .ac-cta-subtitle {
        font-size: 1rem;
    }
    
    .ac-btn-xl {
        padding: 1rem 2rem;
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .ac-process-timeline {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .ac-step-number {
        width: 80px;
        height: 80px;
    }
    
    .ac-step-number span {
        font-size: 2rem;
    }
    
    .ac-section-header {
        margin-bottom: 3rem;
    }
    
    .ac-features-grid,
    .ac-testimonials-grid,
    .ac-faq-grid {
        gap: 1.5rem;
    }
    
    .ac-feature-card {
        padding: 1.5rem;
    }
    
    .ac-step-number {
        width: 80px;
        height: 80px;
    }
    
    .ac-step-number span {
        font-size: 2rem;
    }
    
    .ac-comparison-table thead th {
        font-size: 0.75rem;
        padding: 1rem 0.5rem;
    }
    
    .ac-comparison-table tbody td {
        font-size: 0.75rem;
        padding: 1rem 0.5rem;
    }
    
    .ac-testimonial-footer {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .ac-testimonial-metric {
        text-align: left;
    }
}

/* Billing Transparency Button */
#billing-info-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(34, 197, 94, 0.5);
    background: linear-gradient(135deg, #16a34a 0%, #059669 100%);
}

#billing-info-btn:active {
    transform: translateY(0);
}


</style>

    
    
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=AW-17160853988"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'AW-17160853988');
    </script>
    
    <!-- Event snippet for Google Shopping App Purchase conversion page -->
    <script>
    function gtag_report_conversion(url) {
      var callback = function () {
        if (typeof(url) != 'undefined') {
          window.location = url;
        }
      };
      gtag('event', 'conversion', {
          'send_to': 'AW-17160853988/uVkCCKrgnNQaEOSz9_Y_',
          'value': 1.0,
          'currency': 'GBP',
          'transaction_id': '',
          'event_callback': callback
      });
      return false;
    }
    </script>
    
    <!-- Mobile Logo Switch -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            function updateLogo() {
                const logo = document.querySelector('.ac-logo-img');
                if (logo && window.innerWidth <= 767) {
                    logo.src = 'static/images/Grovelogosmall.png';
                } else if (logo && window.innerWidth > 767) {
                    logo.src = 'static/images/grove-logo.png';
                }
            }
            
            // Run on load
            updateLogo();
            
            // Run on resize
            let resizeTimer;
            window.addEventListener('resize', function() {
                clearTimeout(resizeTimer);
                resizeTimer = setTimeout(updateLogo, 250);
            });
        });
    </script>
</head>
<body>
    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5CMNG3PG"
    height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->
    
    <!-- Navigation -->
    <nav class="ac-nav">
        <div class="ac-nav-container">
            <a href="index.html" class="ac-logo">
                <img src="static/images/grove-logo.png" alt="Grove" class="ac-logo-img">
            </a>
            
            <div class="ac-nav-menu">
                <!-- Why Grove Mega Menu -->
                <div class="ac-nav-dropdown">
                    <a href="#" class="ac-nav-link ac-dropdown-toggle">Why Grove</a>
                    <div class="ac-mega-menu">
                        <div class="ac-mega-menu-inner">
                            <div class="ac-mega-grid ac-mega-grid-features">
                            <!-- Features Column 1 -->
                            <div class="ac-mega-column ac-mega-feature-column">
                                <a href="features.html#product-seo-engine" class="ac-mega-feature">
                                    <div class="ac-mega-feature-icon">
                                        <i class="fas fa-brain"></i>
                                    </div>
                                    <div class="ac-mega-feature-content">
                                        <span class="ac-mega-feature-title">Product SEO Engine</span>
                                        <span class="ac-mega-feature-desc">AI-powered descriptions, meta tags & smart alt tags</span>
                                    </div>
                                </a>
                                <a href="features.html#smart-collections" class="ac-mega-feature">
                                    <div class="ac-mega-feature-icon">
                                        <i class="fas fa-network-wired"></i>
                                    </div>
                                    <div class="ac-mega-feature-content">
                                        <span class="ac-mega-feature-title">Smart Collections</span>
                                        <span class="ac-mega-feature-desc">Trend-based & seasonal collection creation</span>
                                    </div>
                                </a>
                                <a href="features.html#technical-seo" class="ac-mega-feature">
                                    <div class="ac-mega-feature-icon">
                                        <i class="fas fa-cog"></i>
                                    </div>
                                    <div class="ac-mega-feature-content">
                                        <span class="ac-mega-feature-title">Technical SEO</span>
                                        <span class="ac-mega-feature-desc">Sitemaps, speed monitoring & error fixes</span>
                                    </div>
                                </a>
                            </div>
                            
                            <!-- Features Column 2 -->
                            <div class="ac-mega-column ac-mega-feature-column">
                                <a href="features.html#search-insights" class="ac-mega-feature">
                                    <div class="ac-mega-feature-icon">
                                        <i class="fas fa-microscope"></i>
                                    </div>
                                    <div class="ac-mega-feature-content">
                                        <span class="ac-mega-feature-title">Search Insights</span>
                                        <span class="ac-mega-feature-desc">Google Search Console analytics & keyword tracking</span>
                                    </div>
                                </a>
                                <a href="features.html#content-machine" class="ac-mega-feature">
                                    <div class="ac-mega-feature-icon">
                                        <i class="fas fa-feather-alt"></i>
                                    </div>
                                    <div class="ac-mega-feature-content">
                                        <span class="ac-mega-feature-title">Content Machine</span>
                                        <span class="ac-mega-feature-desc">Blog posts, guides & internal linking automation</span>
                                    </div>
                                </a>
                                <a href="features.html#competitor-intel" class="ac-mega-feature">
                                    <div class="ac-mega-feature-icon">
                                        <i class="fas fa-user-secret"></i>
                                    </div>
                                    <div class="ac-mega-feature-content">
                                        <span class="ac-mega-feature-title">Competitor Intel</span>
                                        <span class="ac-mega-feature-desc">Monitor competitor keywords & strategies</span>
                                    </div>
                                </a>
                            </div>
                            
                            <!-- Features Column 3 -->
                            <div class="ac-mega-column ac-mega-feature-column">
                                <a href="features.html#link-building" class="ac-mega-feature">
                                    <div class="ac-mega-feature-icon">
                                        <i class="fas fa-link"></i>
                                    </div>
                                    <div class="ac-mega-feature-content">
                                        <span class="ac-mega-feature-title">Link Building</span>
                                        <span class="ac-mega-feature-desc">Automated outreach & partnership opportunities</span>
                                    </div>
                                </a>
                                <a href="features.html#translations" class="ac-mega-feature">
                                    <div class="ac-mega-feature-icon">
                                        <i class="fas fa-globe"></i>
                                    </div>
                                    <div class="ac-mega-feature-content">
                                        <span class="ac-mega-feature-title">Translations</span>
                                        <span class="ac-mega-feature-desc">Multi-language SEO optimization</span>
                                    </div>
                                </a>
                            </div>
                            </div>
                            
                            <!-- Featured Section -->
                            <div class="ac-mega-featured">
                                <div class="ac-mega-featured-content">
                                    <h4 class="ac-mega-featured-title">🚀 See All Features in Action</h4>
                                    <p class="ac-mega-featured-desc">
                                        Join 7,064 Shopify stores already dominating their competition
                                    </p>
                                    <div class="ac-mega-featured-links">
                                        <a href="features.html" class="ac-mega-featured-link">
                                            View All Features
                                            <i class="fas fa-arrow-right"></i>
                                        </a>
                                        <a href="pre-registration.html" class="ac-mega-featured-link">
                                            Join Waitlist
                                            <i class="fas fa-arrow-right"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Solutions Mega Menu -->
                <div class="ac-nav-dropdown">
                    <a href="#" class="ac-nav-link ac-dropdown-toggle">Solutions</a>
                    <div class="ac-mega-menu">
                        <div class="ac-mega-menu-inner">
                            <div class="ac-mega-grid-solutions">
                            <!-- By Store Type (Full Width) -->
                            <div class="ac-mega-solutions-main">
                                <h3 class="ac-mega-column-header">
                                    <i class="fas fa-store"></i>
                                    Solutions By Store Type
                                </h3>
                                <div class="ac-store-types-grid-full">
                                    <a href="dropshipping.html" class="ac-store-type-card">
                                        <div class="ac-store-type-icon">
                                            <i class="fas fa-box"></i>
                                        </div>
                                        <div class="ac-store-type-content">
                                            <span class="ac-store-type-title">Dropshipping</span>
                                            <span class="ac-store-type-desc">Bulk optimize unlimited SKUs</span>
                                        </div>
                                    </a>
                                    <a href="fashion.html" class="ac-store-type-card">
                                        <div class="ac-store-type-icon">
                                            <i class="fas fa-tshirt"></i>
                                        </div>
                                        <div class="ac-store-type-content">
                                            <span class="ac-store-type-title">Fashion & Apparel</span>
                                            <span class="ac-store-type-desc">Style-aware descriptions</span>
                                        </div>
                                    </a>
                                    <a href="beauty.html" class="ac-store-type-card">
                                        <div class="ac-store-type-icon">
                                            <i class="fas fa-palette"></i>
                                        </div>
                                        <div class="ac-store-type-content">
                                            <span class="ac-store-type-title">Beauty & Cosmetics</span>
                                            <span class="ac-store-type-desc">Ingredient-focused SEO</span>
                                        </div>
                                    </a>
                                    <a href="tech.html" class="ac-store-type-card">
                                        <div class="ac-store-type-icon">
                                            <i class="fas fa-laptop"></i>
                                        </div>
                                        <div class="ac-store-type-content">
                                            <span class="ac-store-type-title">Electronics & Tech</span>
                                            <span class="ac-store-type-desc">Spec-optimized content</span>
                                        </div>
                                    </a>
                                    <a href="home-decor.html" class="ac-store-type-card">
                                        <div class="ac-store-type-icon">
                                            <i class="fas fa-home"></i>
                                        </div>
                                        <div class="ac-store-type-content">
                                            <span class="ac-store-type-title">Home & Decor</span>
                                            <span class="ac-store-type-desc">Room & style targeting</span>
                                        </div>
                                    </a>
                                    <a href="jewelry.html" class="ac-store-type-card">
                                        <div class="ac-store-type-icon">
                                            <i class="fas fa-gem"></i>
                                        </div>
                                        <div class="ac-store-type-content">
                                            <span class="ac-store-type-title">Jewelry & Accessories</span>
                                            <span class="ac-store-type-desc">Luxury keyword focus</span>
                                        </div>
                                    </a>
                                    <a href="fitness.html" class="ac-store-type-card">
                                        <div class="ac-store-type-icon">
                                            <i class="fas fa-dumbbell"></i>
                                        </div>
                                        <div class="ac-store-type-content">
                                            <span class="ac-store-type-title">Sports & Fitness</span>
                                            <span class="ac-store-type-desc">Performance keywords</span>
                                        </div>
                                    </a>
                                    <a href="pets.html" class="ac-store-type-card">
                                        <div class="ac-store-type-icon">
                                            <i class="fas fa-paw"></i>
                                        </div>
                                        <div class="ac-store-type-content">
                                            <span class="ac-store-type-title">Pet Supplies</span>
                                            <span class="ac-store-type-desc">Pet parent targeting</span>
                                        </div>
                                    </a>
                                    <a href="food.html" class="ac-store-type-card">
                                        <div class="ac-store-type-icon">
                                            <i class="fas fa-utensils"></i>
                                        </div>
                                        <div class="ac-store-type-content">
                                            <span class="ac-store-type-title">Food & Beverage</span>
                                            <span class="ac-store-type-desc">Taste & dietary SEO</span>
                                        </div>
                                    </a>
                                    <a href="gaming.html" class="ac-store-type-card">
                                        <div class="ac-store-type-icon">
                                            <i class="fas fa-gamepad"></i>
                                        </div>
                                        <div class="ac-store-type-content">
                                            <span class="ac-store-type-title">Gaming & Hobbies</span>
                                            <span class="ac-store-type-desc">Enthusiast targeting</span>
                                        </div>
                                    </a>
                                    <a href="baby.html" class="ac-store-type-card">
                                        <div class="ac-store-type-icon">
                                            <i class="fas fa-baby"></i>
                                        </div>
                                        <div class="ac-store-type-content">
                                            <span class="ac-store-type-title">Baby & Kids</span>
                                            <span class="ac-store-type-desc">Parent-focused SEO</span>
                                        </div>
                                    </a>
                                    <a href="eco.html" class="ac-store-type-card">
                                        <div class="ac-store-type-icon">
                                            <i class="fas fa-leaf"></i>
                                        </div>
                                        <div class="ac-store-type-content">
                                            <span class="ac-store-type-title">Eco-Friendly</span>
                                            <span class="ac-store-type-desc">Sustainability keywords</span>
                                        </div>
                                    </a>
                                </div>
                                
                                <!-- By Problem Section -->
                                <h3 class="ac-mega-column-header" style="margin-top: 2rem;">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    Solutions By Problem
                                </h3>
                                <div class="ac-problem-grid">
                                    <a href="shopify-seo-problems.html" class="ac-problem-card">
                                        <div class="ac-problem-icon">
                                            <i class="fas fa-times-circle"></i>
                                        </div>
                                        <div class="ac-problem-content">
                                            <span class="ac-problem-title">No Organic Traffic</span>
                                            <span class="ac-problem-desc">Fix visibility issues & start ranking</span>
                                        </div>
                                    </a>
                                    <a href="shopify-seo-for-beginners.html" class="ac-problem-card">
                                        <div class="ac-problem-icon">
                                            <i class="fas fa-clock"></i>
                                        </div>
                                        <div class="ac-problem-content">
                                            <span class="ac-problem-title">No Time for SEO</span>
                                            <span class="ac-problem-desc">24/7 automation handles everything</span>
                                        </div>
                                    </a>
                                    <a href="shopify-seo-guide.html" class="ac-problem-card">
                                        <div class="ac-problem-icon">
                                            <i class="fas fa-question-circle"></i>
                                        </div>
                                        <div class="ac-problem-content">
                                            <span class="ac-problem-title">Don't Know SEO</span>
                                            <span class="ac-problem-desc">We handle everything automatically</span>
                                        </div>
                                    </a>
                                </div>
                            </div>
                            
                            </div>
                            
                            <!-- Featured Section -->
                            <div class="ac-mega-featured">
                                <div class="ac-mega-featured-content">
                                    <h4 class="ac-mega-featured-title">📊 Get Your Free SEO Audit</h4>
                                    <p class="ac-mega-featured-desc">
                                        See exactly what's holding your store back and how to fix it
                                    </p>
                                    <div class="ac-mega-featured-links">
                                        <a href="pre-registration.html" class="ac-mega-featured-link">
                                            Get Early Access
                                            <i class="fas fa-arrow-right"></i>
                                        </a>
                                        <a href="shopify-dropshipping-case-study.html" class="ac-mega-featured-link">
                                            Success Stories
                                            <i class="fas fa-arrow-right"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Resources Dropdown -->
                <div class="ac-nav-dropdown">
                    <a href="#" class="ac-nav-link ac-dropdown-toggle">Resources</a>
                    <div class="ac-mega-menu">
                        <div class="ac-mega-menu-inner">
                            <div class="ac-mega-grid">
                            <!-- Learn SEO -->
                            <div class="ac-mega-column">
                                <h3 class="ac-mega-column-header">
                                    <i class="fas fa-graduation-cap"></i>
                                    Learn SEO
                                </h3>
                                <a href="shopify-seo-guide.html" class="ac-mega-item">
                                    <div class="ac-mega-icon">
                                        <i class="fas fa-book"></i>
                                    </div>
                                    <div class="ac-mega-content">
                                        <span class="ac-mega-title">Ultimate Guide</span>
                                        <span class="ac-mega-desc">Everything you need</span>
                                    </div>
                                </a>
                                <a href="shopify-seo-for-beginners.html" class="ac-mega-item">
                                    <div class="ac-mega-icon">
                                        <i class="fas fa-user-graduate"></i>
                                    </div>
                                    <div class="ac-mega-content">
                                        <span class="ac-mega-title">Beginners Course</span>
                                        <span class="ac-mega-desc">Start from scratch</span>
                                    </div>
                                </a>
                                <a href="shopify-seo-checklist.html" class="ac-mega-item">
                                    <div class="ac-mega-icon">
                                        <i class="fas fa-check-square"></i>
                                    </div>
                                    <div class="ac-mega-content">
                                        <span class="ac-mega-title">SEO Checklist</span>
                                        <span class="ac-mega-desc">Step-by-step guide</span>
                                    </div>
                                </a>
                                <a href="shopify-seo-problems.html" class="ac-mega-item">
                                    <div class="ac-mega-icon">
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </div>
                                    <div class="ac-mega-content">
                                        <span class="ac-mega-title">Common Problems</span>
                                        <span class="ac-mega-desc">Quick fixes</span>
                                    </div>
                                </a>
                            </div>
                            
                            <!-- From the Blog -->
                            <div class="ac-mega-column">
                                <h3 class="ac-mega-column-header">
                                    <i class="fas fa-rss"></i>
                                    From the Blog
                                </h3>
                                <a href="blog/how-ai-is-revolutionizing-shopify-seo-2025.html" class="ac-blog-item">
                                    <div class="ac-blog-meta">
                                        <span class="ac-blog-date">Dec 18, 2024</span>
                                        <span class="ac-blog-read">8 min read</span>
                                    </div>
                                    <h4 class="ac-blog-title">How AI is Revolutionizing Shopify SEO in 2025</h4>
                                    <p class="ac-blog-excerpt">Discover how artificial intelligence is transforming e-commerce SEO...</p>
                                </a>
                                <a href="blog/10-shopify-seo-mistakes-costing-you-sales.html" class="ac-blog-item">
                                    <div class="ac-blog-meta">
                                        <span class="ac-blog-date">Dec 17, 2024</span>
                                        <span class="ac-blog-read">10 min read</span>
                                    </div>
                                    <h4 class="ac-blog-title">10 Shopify SEO Mistakes That Cost You Sales</h4>
                                    <p class="ac-blog-excerpt">Avoid these common pitfalls that prevent your store from ranking...</p>
                                </a>
                                <a href="blog/welcome-to-grove-ai.html" class="ac-blog-item">
                                    <div class="ac-blog-meta">
                                        <span class="ac-blog-date">Dec 19, 2024</span>
                                        <span class="ac-blog-read">3 min read</span>
                                    </div>
                                    <h4 class="ac-blog-title">Welcome to Grove AI</h4>
                                    <p class="ac-blog-excerpt">A note from our founder on revolutionizing Shopify SEO...</p>
                                </a>
                                <a href="blog.html" class="ac-blog-view-all">
                                    View All Articles
                                    <i class="fas fa-arrow-right"></i>
                                </a>
                            </div>
                            
                            <!-- Tools -->
                            <div class="ac-mega-column">
                                <h3 class="ac-mega-column-header">
                                    <i class="fas fa-tools"></i>
                                    Tools
                                </h3>
                                <a href="shopify-product-description-seo.html" class="ac-mega-item">
                                    <div class="ac-mega-icon">
                                        <i class="fas fa-pen"></i>
                                    </div>
                                    <div class="ac-mega-content">
                                        <span class="ac-mega-title">Product SEO Guide</span>
                                        <span class="ac-mega-desc">Optimize descriptions</span>
                                    </div>
                                </a>
                                <a href="shopify-technical-seo.html" class="ac-mega-item">
                                    <div class="ac-mega-icon">
                                        <i class="fas fa-cog"></i>
                                    </div>
                                    <div class="ac-mega-content">
                                        <span class="ac-mega-title">Technical SEO</span>
                                        <span class="ac-mega-desc">Site health check</span>
                                    </div>
                                </a>
                            </div>
                            </div>
                            
                            <!-- Featured Section -->
                            <div class="ac-mega-featured">
                                <div class="ac-mega-featured-content">
                                    <h4 class="ac-mega-featured-title">📚 Master Shopify SEO</h4>
                                    <p class="ac-mega-featured-desc">
                                        Get our complete library of guides, tools, and insights
                                    </p>
                                    <div class="ac-mega-featured-links">
                                        <a href="shopify-seo-guide.html" class="ac-mega-featured-link">
                                            Download Free Guide
                                            <i class="fas fa-arrow-right"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Pricing Link -->
                <a href="pricing.html" class="ac-nav-link">Pricing</a>
                
                <!-- Company Dropdown -->
                <div class="ac-nav-dropdown">
                    <a href="about.html" class="ac-nav-link ac-dropdown-toggle">Company</a>
                    <div class="ac-company-menu">
                        <a href="about.html" class="ac-company-item">
                            <div class="ac-company-icon">
                                <i class="fas fa-building"></i>
                            </div>
                            <div class="ac-company-content">
                                <span class="ac-company-title">About Grove</span>
                                <span class="ac-company-desc">Our story, mission & values</span>
                            </div>
                        </a>
                        <a href="contact.html" class="ac-company-item">
                            <div class="ac-company-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="ac-company-content">
                                <span class="ac-company-title">Contact Us</span>
                                <span class="ac-company-desc">Get help or say hello</span>
                            </div>
                        </a>
                    </div>
                </div>
                
                <!-- CTAs -->
                <div class="ac-nav-cta">
                    <a href="auth/login.html" class="ac-btn ac-btn-secondary">Login</a>
                    <a href="pre-registration.html" class="ac-btn ac-btn-primary">Join Waitlist</a>
                </div>
            </div>
            
            <!-- Mobile Menu Toggle -->
            <button class="ac-mobile-toggle" aria-label="Toggle menu">
                <i class="fas fa-bars"></i>
            </button>
        </div>
    </nav>
    
    <!-- Mobile Menu Overlay -->
    <div class="ac-mobile-menu" style="display: none;">
        <div class="ac-mobile-header">
            <a href="index.html" class="ac-logo">
                <img src="static/images/grove-logo.png" alt="Grove" style="height: 36px; width: auto;">
            </a>
            <button class="ac-mobile-close">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="ac-mobile-nav">
            <a href="index.html" class="ac-mobile-item">Home</a>
            <a href="features.html" class="ac-mobile-item">Features</a>
            <a href="pricing.html" class="ac-mobile-item">Pricing</a>
            <a href="about.html" class="ac-mobile-item">About</a>
            <a href="contact.html" class="ac-mobile-item">Contact</a>
            <div style="padding: 1rem 0;">
                <a href="pre-registration.html" class="ac-btn ac-btn-primary ac-btn-block">Join Waitlist</a>
            </div>
        </div>
    </div>
    
    <!-- Main Content -->
    <main>
        
<!-- Hero Section with Split Layout -->
<section class="pre-reg-hero">
    <!-- Hero Blob -->
    <div class="pre-reg-blob"></div>
    <div class="pre-reg-container">
        <div class="pre-reg-grid">
            <!-- Left Content -->
            <div class="pre-reg-content">
                <h1>
                    SEO Agencies Hate This: <span class="pink-highlight">AI That Does Their Job</span> 
                    for 10X Less Money
                </h1>
                <p class="pre-reg-subtitle">
                    Stop paying agencies that deliver Excel sheets instead of results. Grove 
                    optimizes your entire Shopify catalog 24/7, creating content that ranks and converts.
                    <strong>Get 80% Off - Reserve for $1</strong>
                </p>
                
                <!-- Trust Badges -->
                <div class="trust-badges">
                    <div class="trust-badge">
                        <i class="fab fa-shopify"></i>
                        <span>Made for Shopify</span>
                    </div>
                    <div class="trust-badge">
                        <i class="fas fa-dollar-sign"></i>
                        <span>Save $60k+/year</span>
                    </div>
                    <div class="trust-badge">
                        <i class="fas fa-brain"></i>
                        <span>Fully Agentic</span>
                    </div>
                </div>

                <!-- Animated Automation Cards - Compact -->
                <div class="automation-cards">
                    <div class="automation-card">
                        <div class="automation-header">
                            <div class="automation-title">
                                <i class="fas fa-box"></i>
                                Product SEO
                            </div>
                            <div class="automation-status">
                                <i class="fas fa-sync-alt"></i>
                                <span>LIVE</span>
                            </div>
                        </div>
                        <p class="automation-description">
                            AI rewrites all products while agencies make spreadsheets
                        </p>
                        <div class="automation-metric">
                            <i class="fas fa-arrow-up"></i>
                            <span>+284% traffic</span>
                        </div>
                    </div>

                    <div class="automation-card">
                        <div class="automation-header">
                            <div class="automation-title">
                                <i class="fas fa-layer-group"></i>
                                Collections
                            </div>
                            <div class="automation-status">
                                <i class="fas fa-sync-alt"></i>
                                <span>LIVE</span>
                            </div>
                        </div>
                        <p class="automation-description">
                            Creates trending collections before competitors
                        </p>
                        <div class="automation-metric">
                            <i class="fas fa-fire"></i>
                            <span>24/7 trends</span>
                        </div>
                    </div>

                    <div class="automation-card">
                        <div class="automation-header">
                            <div class="automation-title">
                                <i class="fas fa-language"></i>
                                Languages
                            </div>
                            <div class="automation-status">
                                <i class="fas fa-sync-alt"></i>
                                <span>LIVE</span>
                            </div>
                        </div>
                        <p class="automation-description">
                            Expands to 15 languages instantly
                        </p>
                        <div class="automation-metric">
                            <i class="fas fa-globe"></i>
                            <span>97% cheaper</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Form Panel -->
            <div class="form-panel">
                <div class="form-container">
                    <!-- Urgency Banner -->
                    <div class="urgency-banner" style="margin-bottom: 1.5rem;">
                        <i class="fas fa-fire"></i>
                        <span class="urgency-text">Get 80% Off - Reserve for $1</span>
                    </div>

                    <!-- Progress Steps -->
                    <div class="progress-steps">
                        <div class="step active" id="step1-indicator">
                            <div class="step-circle">1</div>
                            <div class="step-label">Your Info</div>
                        </div>
                        <div class="step" id="step2-indicator">
                            <div class="step-circle">2</div>
                            <div class="step-label">Your Store</div>
                        </div>
                        <div class="step" id="step3-indicator">
                            <div class="step-circle">3</div>
                            <div class="step-label">Get Access</div>
                        </div>
                    </div>

            <!-- Multi-step Form -->
            <form id="waitlistForm">
                    <!-- Step 1: Basic Info -->
                    <div class="form-step active" id="step1">
                        <h2 style="font-family: 'Lexend', sans-serif; font-size: 1.5rem; margin-bottom: 1.5rem;">Become a Founding Member</h2>
                        
                        <div class="form-group">
                            <label class="form-label" for="name">Full Name</label>
                            <input type="text" class="form-input" id="name" name="name" placeholder="John Smith" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="email">Email Address</label>
                            <input type="email" class="form-input" id="email" name="email" placeholder="<EMAIL>" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="store_url">Website URL</label>
                            <input type="text" class="form-input" id="store_url" name="store_url" placeholder="yourstore.com" required title="Enter your website URL">
                            <small style="color: #6b7280; font-size: 0.75rem; margin-top: 0.25rem; display: block;">Enter your store's website URL</small>
                        </div>

                        <div class="btn-group">
                            <button type="button" class="btn btn-primary" onclick="nextStep()" style="margin-left: auto;">
                                Continue <i class="fas fa-arrow-right"></i>
                            </button>
                        </div>
                    </div>

                <!-- Step 2: Store Details -->
                <div class="form-step" id="step2">
                    <h2 class="ac-section-title">About your store</h2>
                    
                    <div class="form-group">
                        <label class="form-label" for="monthly_revenue">Monthly Revenue</label>
                        <select class="form-input form-select" id="monthly_revenue" name="monthly_revenue" required>
                            <option value="">Select revenue range</option>
                            <option value="0-10k">$0 - $10,000</option>
                            <option value="10k-50k">$10,000 - $50,000</option>
                            <option value="50k-100k">$50,000 - $100,000</option>
                            <option value="100k-500k">$100,000 - $500,000</option>
                            <option value="500k+">$500,000+</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="main_challenge">Biggest SEO Challenge</label>
                        <select class="form-input form-select" id="main_challenge" name="main_challenge" required>
                            <option value="">Select your main challenge</option>
                            <option value="time">Not enough time for SEO</option>
                            <option value="knowledge">Lack of SEO expertise</option>
                            <option value="scale">Too many products to optimize</option>
                            <option value="results">Not seeing results from current efforts</option>
                            <option value="cost">Current solutions too expensive</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="expected_launch_interest">When do you need this?</label>
                        <select class="form-input form-select" id="expected_launch_interest" name="expected_launch_interest" required>
                            <option value="">Select timeframe</option>
                            <option value="immediate">ASAP - I'm losing sales daily</option>
                            <option value="within_month">Within a month</option>
                            <option value="within_quarter">Within 3 months</option>
                        </select>
                    </div>

                    <div class="urgency-banner">
                        <i class="fas fa-clock"></i>
                        <span class="urgency-text">Limited time early bird offer!</span>
                    </div>

                    <div class="btn-group">
                        <button type="button" class="btn btn-secondary" onclick="prevStep()">
                            <i class="fas fa-arrow-left"></i> Back
                        </button>
                        <button type="button" class="btn btn-primary" onclick="nextStep()">
                            Final Step <i class="fas fa-arrow-right"></i>
                        </button>
                    </div>
                </div>

                <!-- Step 3: Complete Registration -->
                <div class="form-step" id="step3">
                    <h2 style="font-family: 'Lexend', sans-serif; font-size: 1.5rem; margin-bottom: 1.5rem;">Become a Founding Member</h2>
                    
                    <!-- Payment Card Section -->
                    <div class="payment-card-section" style="background: #f9fafb; border-radius: 12px; padding: 1.5rem; margin-bottom: 1.5rem;">
                        <h3 style="font-size: 1.125rem; font-weight: 600; margin-bottom: 1rem; display: flex; align-items: center; justify-content: space-between;">
                            <span>Secure Your Spot</span>
                            <div style="display: flex; gap: 0.5rem; align-items: center;">
                                <i class="fab fa-cc-visa" style="font-size: 1.5rem; color: #1a1f71;"></i>
                                <i class="fab fa-cc-mastercard" style="font-size: 1.5rem; color: #eb001b;"></i>
                                <i class="fab fa-cc-amex" style="font-size: 1.5rem; color: #006fcf;"></i>
                                <i class="fab fa-cc-paypal" style="font-size: 1.5rem; color: #003087;"></i>
                            </div>
                        </h3>
                        
                        <div class="price-display" style="text-align: center; padding: 1.5rem 0; border-bottom: 1px solid #e5e7eb; margin-bottom: 1.5rem;">
                            <div style="font-size: 2.5rem; font-weight: 800; color: var(--grove-pink);">
                                $1<span style="font-size: 1.5rem; font-weight: 600;">.00</span>
                            </div>
                            <p style="color: var(--ac-text-secondary); margin: 0.5rem 0 0;">One-time reservation fee</p>
                            
                            <p style="color: var(--grove-green); font-weight: 600; margin: 1rem 0 0;">
                                <i class="fas fa-check-circle"></i> Early Access: $99 per month for 3 months
                            </p>
                            <p style="color: var(--ac-text-secondary); font-size: 0.95rem; margin: 0.5rem 0 0;">
                                Limited-time founding member pricing
                            </p>
                            <p style="color: var(--ac-text-muted); font-size: 0.875rem; margin: 0.25rem 0 0;">
                                Regular price after 3 months: $499/mo
                            </p>
                        </div>
                        
                        <!-- Billing Details -->
                        <h3 style="font-size: 1.125rem; font-weight: 600; color: var(--ac-text-primary); margin-bottom: 1rem; margin-top: 1.5rem;">
                            Payment Method
                        </h3>
                        
                        <div class="form-group">
                            <div id="payment-element" style="min-height: 250px;">
                                <!-- Stripe Payment Element will be inserted here -->
                            </div>
                            <div id="payment-errors" role="alert" style="color: #ef4444; font-size: 0.875rem; margin-top: 0.5rem;"></div>
                        </div>
                    </div>
                    
                    <!-- What You Get -->
                    <div class="what-you-get" style="background: #f0fdf4; border: 1px solid #22c55e; border-radius: 12px; padding: 1rem; margin-bottom: 1.5rem;">
                        <h4 style="font-size: 0.875rem; font-weight: 600; color: #16a34a; margin-bottom: 0.75rem; text-transform: uppercase; letter-spacing: 0.05em;">
                            <i class="fas fa-star"></i> What Your $1 Gets You
                        </h4>
                        <ul style="list-style: none; padding: 0; margin: 0; font-size: 0.875rem;">
                            <li style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
                                <i class="fas fa-check" style="color: #22c55e; font-size: 0.75rem;"></i>
                                <span>80% off for 3 months ($99/pm, normally $499/mo)</span>
                            </li>
                            <li style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
                                <i class="fas fa-check" style="color: #22c55e; font-size: 0.75rem;"></i>
                                <span>Priority onboarding when we launch</span>
                            </li>
                            <li style="display: flex; align-items: center; gap: 0.5rem;">
                                <i class="fas fa-check" style="color: #22c55e; font-size: 0.75rem;"></i>
                                <span>Full refund if you change your mind</span>
                            </li>
                        </ul>
                    </div>

                    <div class="ac-checkbox-group">
                        <input type="checkbox" id="terms_consent" name="terms_consent" required>
                        <label for="terms_consent">
                            I agree to the <a href="terms.html" target="_blank">Terms of Service</a> and 
                            <a href="privacy.html" target="_blank">Privacy Policy</a>
                        </label>
                    </div>

                    <input type="hidden" name="registration_type" value="pre_registration">
                    <input type="hidden" name="payment_amount" value="1.00">
                    <input type="hidden" name="referral_code" id="referral_code" value="">

                    <div class="btn-group">
                        <button type="button" class="btn btn-secondary" onclick="prevStep()">
                            <i class="fas fa-arrow-left"></i> Back
                        </button>
                        <button type="submit" class="btn btn-primary" id="submitBtn" style="position: relative;">
                            <span id="btnText">Pay $1 & Reserve Spot</span>
                            <i class="fas fa-lock" style="margin-left: 0.5rem;"></i>
                        </button>
                    </div>
                    
                    <div style="text-align: center; margin-top: 1rem;">
                        <p style="font-size: 0.75rem; color: var(--ac-text-muted);">
                            <i class="fas fa-shield-alt" style="color: var(--grove-green);"></i>
                            Secured by Stripe • 256-bit SSL encryption
                        </p>
                    </div>
                </div>
            </form>

            <!-- Success State -->
            <div class="success-container" id="successState">
                <div class="success-icon">
                    <i class="fas fa-check"></i>
                </div>
                
                <h2 class="ac-section-title">Spot Secured!</h2>
                <p class="ac-subtitle">You just saved $1,800/year on SEO. Check your email for what happens next.</p>

                <div class="referral-card">
                    <h3 class="ac-feature-title">Your Waitlist Position: #<span id="waitlistPosition">--</span></h3>
                    <p style="color: var(--ac-text-secondary); margin-bottom: 1rem;">
                        Want to skip the line? Share your referral code:
                    </p>
                    <div class="referral-code" id="referralCode">------</div>
                    <button class="btn btn-secondary" onclick="copyReferralLink()" style="margin: 1rem auto; display: block;">
                        <i class="fas fa-copy"></i> Copy Referral Link
                    </button>
                    <p style="color: var(--ac-text-muted); font-size: 0.875rem;">
                        Each friend who joins = you both move up 10 spots!
                    </p>
                </div>

                <div class="share-buttons">
                    <a href="#" class="share-btn share-twitter" id="shareTwitter" target="_blank">
                        <i class="fab fa-twitter"></i> Share
                    </a>
                    <a href="#" class="share-btn share-linkedin" id="shareLinkedIn" target="_blank">
                        <i class="fab fa-linkedin"></i> Share
                    </a>
                    <a href="#" class="share-btn share-email" id="shareEmail">
                        <i class="fas fa-envelope"></i> Email
                    </a>
                </div>
            </div>
        </div>
        </div>
    </div>
</section>


<section class="ac-automation-section" style="position: relative; background: linear-gradient(180deg, #ffffff 0%, #f5f5f5 100%);">
    <div class="ac-features-container" style="position: relative;">
        <div class="ac-section-header">
            <h2 class="ac-section-title">Set It Once. Watch Your Rankings Soar.</h2>
            <p class="ac-section-subtitle">
                Toggle on the automations you want. Our AI handles the rest 24/7. No maintenance required.
            </p>
        </div>
        
        <!-- AI Sparkles -->
        <div class="ac-ai-sparkle ac-sparkle-1">
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 0 L14 10 L24 12 L14 14 L12 24 L10 14 L0 12 L10 10 L12 0 Z" fill="#ef2b70"/>
            </svg>
        </div>
        <div class="ac-ai-sparkle ac-sparkle-2 small">
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 0 L14 10 L24 12 L14 14 L12 24 L10 14 L0 12 L10 10 L12 0 Z" fill="#ef2b70"/>
            </svg>
        </div>
        <div class="ac-ai-sparkle ac-sparkle-3 large">
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 0 L14 10 L24 12 L14 14 L12 24 L10 14 L0 12 L10 10 L12 0 Z" fill="#ef2b70"/>
            </svg>
        </div>
        <div class="ac-ai-sparkle ac-sparkle-4">
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 0 L14 10 L24 12 L14 14 L12 24 L10 14 L0 12 L10 10 L12 0 Z" fill="#ef2b70"/>
            </svg>
        </div>
        <div class="ac-ai-sparkle ac-sparkle-5 small">
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 0 L14 10 L24 12 L14 14 L12 24 L10 14 L0 12 L10 10 L12 0 Z" fill="#ef2b70"/>
            </svg>
        </div>
        <div class="ac-ai-sparkle ac-sparkle-6">
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 0 L14 10 L24 12 L14 14 L12 24 L10 14 L0 12 L10 10 L12 0 Z" fill="#ef2b70"/>
            </svg>
        </div>
        <!-- Desktop-only sparkle on left side of heading -->
        <div class="ac-ai-sparkle ac-sparkle-7 desktop-only large" style="position: absolute; left: 5%; top: 8%; animation-delay: 3.5s;">
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 0 L14 10 L24 12 L14 14 L12 24 L10 14 L0 12 L10 10 L12 0 Z" fill="#ef2b70"/>
            </svg>
        </div>
        
        <div class="ac-ai-console-rounded">
            <div class="ac-console-header-rounded">
                <div class="ac-console-dots">
                    <span class="ac-dot ac-dot-red"></span>
                    <span class="ac-dot ac-dot-yellow"></span>
                    <span class="ac-dot ac-dot-green"></span>
                </div>
                <div class="ac-console-status">
                    <span class="ac-status-indicator-rounded"></span>
                    <span>AI Automations Active</span>
                </div>
            </div>
            
            <div class="ac-ai-grid-rounded">
                <!-- Product SEO Agent Rounded -->
                <div class="ac-ai-agent-card-rounded" id="automation-products">
                <div class="ac-agent-header">
                    <div class="ac-agent-avatar-rounded">
                        <i class="fas fa-brain"></i>
                        <span class="ac-agent-status-ring-rounded"></span>
                    </div>
                    <div class="ac-agent-info">
                        <h3 class="ac-agent-name">Product SEO Engine</h3>
                    </div>
                    <label class="ac-agent-toggle-rounded">
                        <input type="checkbox" id="automation-toggle-products" onchange="toggleAutomation('products')">
                        <span class="ac-toggle-slider-rounded"></span>
                    </label>
                </div>
                
                <ul class="ac-automation-features">
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-pen"></i>
                        </div>
                        <span class="ac-automation-feature-text">AI-powered descriptions</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-tag"></i>
                        </div>
                        <span class="ac-automation-feature-text">Meta tag optimization</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-image"></i>
                        </div>
                        <span class="ac-automation-feature-text">Smart alt tags</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-sync"></i>
                        </div>
                        <span class="ac-automation-feature-text">Daily updates</span>
                    </li>
                </ul>
                
                <div class="ac-agent-live-strip">
                    <span class="ac-live-indicator"></span>
                    <span class="ac-live-text">Analyzing 1,247 products...</span>
                </div>
            </div>
            
            <!-- Collections AI Agent Rounded -->
            <div class="ac-ai-agent-card-rounded" id="automation-collections">
                <div class="ac-agent-header">
                    <div class="ac-agent-avatar-rounded">
                        <i class="fas fa-network-wired"></i>
                        <span class="ac-agent-status-ring-rounded"></span>
                    </div>
                    <div class="ac-agent-info">
                        <h3 class="ac-agent-name">Smart Collections</h3>
                    </div>
                    <label class="ac-agent-toggle-rounded">
                        <input type="checkbox" id="automation-toggle-collections" onchange="toggleAutomation('collections')">
                        <span class="ac-toggle-slider-rounded"></span>
                    </label>
                </div>
                
                <ul class="ac-automation-features">
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-layer-group"></i>
                        </div>
                        <span class="ac-automation-feature-text">Trending collections</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-calendar"></i>
                        </div>
                        <span class="ac-automation-feature-text">Seasonal updates</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-bullseye"></i>
                        </div>
                        <span class="ac-automation-feature-text">Keyword targeting</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <span class="ac-automation-feature-text">Performance-based</span>
                    </li>
                </ul>
                
                <div class="ac-agent-live-strip">
                    <span class="ac-live-indicator"></span>
                    <span class="ac-live-text">Found trending opportunity</span>
                </div>
            </div>
            
            <!-- Search Insights AI Agent Rounded -->
            <div class="ac-ai-agent-card-rounded" id="automation-search">
                <div class="ac-agent-header">
                    <div class="ac-agent-avatar-rounded">
                        <i class="fas fa-microscope"></i>
                        <span class="ac-agent-status-ring-rounded"></span>
                    </div>
                    <div class="ac-agent-info">
                        <h3 class="ac-agent-name">Search Insights</h3>
                    </div>
                    <label class="ac-agent-toggle-rounded">
                        <input type="checkbox" id="automation-toggle-search" onchange="toggleAutomation('search')">
                        <span class="ac-toggle-slider-rounded"></span>
                    </label>
                </div>
                
                <ul class="ac-automation-features">
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fab fa-google"></i>
                        </div>
                        <span class="ac-automation-feature-text">Search Console sync</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <span class="ac-automation-feature-text">Opportunity finder</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-rocket"></i>
                        </div>
                        <span class="ac-automation-feature-text">Position 1 targeting</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-trophy"></i>
                        </div>
                        <span class="ac-automation-feature-text">Daily tracking</span>
                    </li>
                </ul>
                
                <div class="ac-agent-live-strip">
                    <span class="ac-live-indicator"></span>
                    <span class="ac-live-text">Monitoring 1,892 keywords</span>
                </div>
            </div>
            
            <!-- Content Machine AI Agent Rounded -->
            <div class="ac-ai-agent-card-rounded" id="automation-content">
                <div class="ac-agent-header">
                    <div class="ac-agent-avatar-rounded">
                        <i class="fas fa-feather-alt"></i>
                        <span class="ac-agent-status-ring-rounded"></span>
                    </div>
                    <div class="ac-agent-info">
                        <h3 class="ac-agent-name">Content Machine</h3>
                    </div>
                    <label class="ac-agent-toggle-rounded">
                        <input type="checkbox" id="automation-toggle-content" onchange="toggleAutomation('content')">
                        <span class="ac-toggle-slider-rounded"></span>
                    </label>
                </div>
                
                <ul class="ac-automation-features">
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-newspaper"></i>
                        </div>
                        <span class="ac-automation-feature-text">Weekly blog posts</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-link"></i>
                        </div>
                        <span class="ac-automation-feature-text">Smart internal links</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-globe"></i>
                        </div>
                        <span class="ac-automation-feature-text">Multi-language content</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-book"></i>
                        </div>
                        <span class="ac-automation-feature-text">Topical authority</span>
                    </li>
                </ul>
                
                <div class="ac-agent-live-strip">
                    <span class="ac-live-indicator"></span>
                    <span class="ac-live-text">Writing buying guide...</span>
                </div>
            </div>
            
            <!-- Technical SEO -->
            <div class="ac-ai-agent-card-rounded" id="automation-technical">
                <div class="ac-agent-header">
                    <div class="ac-agent-avatar-rounded">
                        <i class="fas fa-cog"></i>
                        <span class="ac-agent-status-ring-rounded"></span>
                    </div>
                    <div class="ac-agent-info">
                        <h3 class="ac-agent-name">Technical SEO</h3>
                    </div>
                    <label class="ac-agent-toggle-rounded">
                        <input type="checkbox" id="automation-toggle-technical" onchange="toggleAutomation('technical')">
                        <span class="ac-toggle-slider-rounded"></span>
                    </label>
                </div>
                
                <ul class="ac-automation-features">
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-sitemap"></i>
                        </div>
                        <span class="ac-automation-feature-text">Smart sitemaps</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-code"></i>
                        </div>
                        <span class="ac-automation-feature-text">Schema markup</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                        <span class="ac-automation-feature-text">Speed monitoring</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <span class="ac-automation-feature-text">404 auto-fix</span>
                    </li>
                </ul>
                
                <div class="ac-agent-live-strip">
                    <span class="ac-live-indicator"></span>
                    <span class="ac-live-text">Optimizing site structure...</span>
                </div>
            </div>
            
            <!-- Competitor Tracking -->
            <div class="ac-ai-agent-card-rounded" id="automation-competitor">
                <div class="ac-agent-header">
                    <div class="ac-agent-avatar-rounded">
                        <i class="fas fa-user-secret"></i>
                        <span class="ac-agent-status-ring-rounded"></span>
                    </div>
                    <div class="ac-agent-info">
                        <h3 class="ac-agent-name">Competitor Intel</h3>
                    </div>
                    <label class="ac-agent-toggle-rounded">
                        <input type="checkbox" id="automation-toggle-competitor" onchange="toggleAutomation('competitor')">
                        <span class="ac-toggle-slider-rounded"></span>
                    </label>
                </div>
                
                <ul class="ac-automation-features">
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-user-secret"></i>
                        </div>
                        <span class="ac-automation-feature-text">Competitor tracking</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-lightbulb"></i>
                        </div>
                        <span class="ac-automation-feature-text">Keyword discovery</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-flag"></i>
                        </div>
                        <span class="ac-automation-feature-text">Opportunity alerts</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-crosshairs"></i>
                        </div>
                        <span class="ac-automation-feature-text">Weakness targeting</span>
                    </li>
                </ul>
                
                <div class="ac-agent-live-strip">
                    <span class="ac-live-indicator"></span>
                    <span class="ac-live-text">Tracking 3 competitors...</span>
                </div>
            </div>
            
            <!-- Link Building -->
            <div class="ac-ai-agent-card-rounded" id="automation-linkbuilding">
                <div class="ac-agent-header">
                    <div class="ac-agent-avatar-rounded">
                        <i class="fas fa-link"></i>
                        <span class="ac-agent-status-ring-rounded"></span>
                    </div>
                    <div class="ac-agent-info">
                        <h3 class="ac-agent-name">Link Building</h3>
                    </div>
                    <label class="ac-agent-toggle-rounded">
                        <input type="checkbox" id="automation-toggle-linkbuilding" onchange="toggleAutomation('linkbuilding')">
                        <span class="ac-toggle-slider-rounded"></span>
                    </label>
                </div>
                
                <ul class="ac-automation-features">
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-link"></i>
                        </div>
                        <span class="ac-automation-feature-text">Quality backlinks</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-handshake"></i>
                        </div>
                        <span class="ac-automation-feature-text">Auto outreach</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-network-wired"></i>
                        </div>
                        <span class="ac-automation-feature-text">Strategic links</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <span class="ac-automation-feature-text">Authority tracking</span>
                    </li>
                </ul>
                
                <div class="ac-agent-live-strip">
                    <span class="ac-live-indicator"></span>
                    <span class="ac-live-text">Building authority links...</span>
                </div>
            </div>
            
            <!-- Translations -->
            <div class="ac-ai-agent-card-rounded" id="automation-translations">
                <div class="ac-agent-header">
                    <div class="ac-agent-avatar-rounded">
                        <i class="fas fa-globe"></i>
                        <span class="ac-agent-status-ring-rounded"></span>
                    </div>
                    <div class="ac-agent-info">
                        <h3 class="ac-agent-name">Translations</h3>
                    </div>
                    <label class="ac-agent-toggle-rounded">
                        <input type="checkbox" id="automation-toggle-translations" onchange="toggleAutomation('translations')">
                        <span class="ac-toggle-slider-rounded"></span>
                    </label>
                </div>
                
                <ul class="ac-automation-features">
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-globe"></i>
                        </div>
                        <span class="ac-automation-feature-text">Multi-language SEO</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-language"></i>
                        </div>
                        <span class="ac-automation-feature-text">Native translations</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <span class="ac-automation-feature-text">Local keywords</span>
                    </li>
                    <li class="ac-automation-feature">
                        <div class="ac-automation-feature-icon">
                            <i class="fas fa-flag-checkered"></i>
                        </div>
                        <span class="ac-automation-feature-text">Global expansion</span>
                    </li>
                </ul>
                
                <div class="ac-agent-live-strip">
                    <span class="ac-live-indicator"></span>
                    <span class="ac-live-text">Translating content...</span>
                </div>
            </div>
        </div>
        </div>
    </div>
</section>

<!-- What You Actually Get Section -->
<section class="ac-what-you-get-section" style="background: linear-gradient(180deg, #ffffff 0%, #f5f5f5 100%);">
    <div class="ac-section-container">
        <div class="ac-section-header">
            <h2 class="ac-section-title">What Early Access Actually Gets You</h2>
            <p class="ac-subtitle">Not just another SEO tool. A complete AI workforce that never sleeps.</p>
        </div>
        
        <div class="ac-features-grid">
            <!-- AI Workers -->
            <div class="ac-feature-card ac-feature-card-hover">
                <div class="ac-feature-icon ac-icon-gradient-pink">
                    <i class="fas fa-robot"></i>
                </div>
                <h3 class="ac-feature-title">8 AI Agents Running 24/7</h3>
                <ul class="ac-worker-list">
                    <li><strong>Product Writer:</strong> Rewrites every description while you sleep</li>
                    <li><strong>Collection Builder:</strong> Creates trending collections before competitors</li>
                    <li><strong>Translator:</strong> Expands to 15 languages automatically</li>
                    <li><strong>Technical Optimizer:</strong> Fixes site speed, schema, meta tags</li>
                    <li><strong>And 4 more:</strong> All working together to dominate your niche</li>
                </ul>
                <div class="ac-feature-badge">
                    <i class="fas fa-bolt"></i> Always Active
                </div>
            </div>
            
            <!-- Real Results -->
            <div class="ac-feature-card ac-feature-card-hover">
                <div class="ac-feature-icon ac-icon-gradient-green">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h3 class="ac-feature-title">Results Agencies Can't Match</h3>
                <div class="ac-metrics-list">
                    <div class="ac-metric-item">
                        <span class="ac-metric-label">Average traffic increase</span>
                        <span class="ac-metric-value ac-text-green">+284%</span>
                    </div>
                    <div class="ac-metric-item">
                        <span class="ac-metric-label">AI optimization running</span>
                        <span class="ac-metric-value">24/7/365</span>
                    </div>
                    <div class="ac-metric-item">
                        <span class="ac-metric-label">Time to first results</span>
                        <span class="ac-metric-value">Days</span>
                    </div>
                    <div class="ac-metric-item">
                        <span class="ac-metric-label">Cost vs agencies</span>
                        <span class="ac-metric-value ac-text-pink">10X less</span>
                    </div>
                </div>
                <div class="ac-feature-badge ac-badge-green">
                    <i class="fas fa-trophy"></i> Proven Results
                </div>
            </div>
            
            <!-- Exclusive Access -->
            <div class="ac-feature-card ac-feature-card-hover">
                <div class="ac-feature-icon ac-icon-gradient-purple">
                    <i class="fas fa-crown"></i>
                </div>
                <h3 class="ac-feature-title">Why Join as a Founding Member?</h3>
                <ul class="ac-perk-list">
                    <li><i class="fas fa-check"></i> <strong>$99/pm</strong> for 3 months (80% off)</li>
                    <li><i class="fas fa-check"></i> Direct WhatsApp access to founders</li>
                    <li><i class="fas fa-check"></i> Dedicated account manager</li>
                    <li><i class="fas fa-check"></i> 2x faster processing speeds</li>
                    <li><i class="fas fa-check"></i> Price locked forever (no increases)</li>
                </ul>
                <div class="ac-feature-badge ac-badge-purple">
                    <i class="fas fa-star"></i> VIP Access
                </div>
            </div>
        </div>
        
        <!-- Founding Member Benefits Tiles -->
        <div class="ac-benefits-tiles">
            <h3 class="ac-tiles-title">Exclusive Founding Member Benefits</h3>
            <div class="ac-tiles-grid">
                <!-- Core Access -->
                <div class="ac-benefit-tile">
                    <div class="ac-tile-header">
                        <span class="ac-tile-emoji">🚀</span>
                        <h4 class="ac-tile-title">Core Access</h4>
                    </div>
                    <ul class="ac-tile-list">
                        <li>Full Grove AI SEO Platform</li>
                        <li>24/7 AI optimization for unlimited products</li>
                        <li>All current and future AI modules</li>
                    </ul>
                </div>
                
                <!-- VIP Support -->
                <div class="ac-benefit-tile">
                    <div class="ac-tile-header">
                        <span class="ac-tile-emoji">👥</span>
                        <h4 class="ac-tile-title">VIP Support</h4>
                    </div>
                    <ul class="ac-tile-list">
                        <li>Direct WhatsApp access to Grove founders</li>
                        <li>Dedicated account manager</li>
                        <li>Priority support response</li>
                    </ul>
                </div>
                
                <!-- Performance Perks -->
                <div class="ac-benefit-tile">
                    <div class="ac-tile-header">
                        <span class="ac-tile-emoji">⚡</span>
                        <h4 class="ac-tile-title">Performance Perks</h4>
                    </div>
                    <ul class="ac-tile-list">
                        <li>2x processing speeds</li>
                        <li>Beta features early access</li>
                    </ul>
                </div>
                
                <!-- Financial Benefits -->
                <div class="ac-benefit-tile">
                    <div class="ac-tile-header">
                        <span class="ac-tile-emoji">💰</span>
                        <h4 class="ac-tile-title">Financial Benefits</h4>
                    </div>
                    <ul class="ac-tile-list">
                        <li>80% off for 3 months ($99/pm)</li>
                        <li>Price lock guarantee (no future increases)</li>
                        <li>Refer someone, get $250 credit</li>
                        <li>60-day pause option per year</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- How Our Waitlist Works Section -->
        <div class="ac-waitlist-works-tiles">
            <h3 class="ac-tiles-title">How Our Waitlist Works (No Surprises!)</h3>
            <div class="ac-tiles-grid">
                <!-- Step 1 -->
                <div class="ac-benefit-tile">
                    <div class="ac-tile-header">
                        <span class="ac-tile-emoji">💵</span>
                        <h4 class="ac-tile-title">Reserve Your Spot</h4>
                    </div>
                    <p class="ac-tile-description">
                        Just $1 today to secure your founding member pricing and perks. 
                        Thanks for the early support!
                    </p>
                </div>
                
                <!-- Step 2 -->
                <div class="ac-benefit-tile">
                    <div class="ac-tile-header">
                        <span class="ac-tile-emoji">🎁</span>
                        <h4 class="ac-tile-title">Launch Day Welcome</h4>
                    </div>
                    <p class="ac-tile-description">
                        We'll send you a VIP welcome pack with your activation link and exclusive bonuses
                    </p>
                </div>
                
                <!-- Step 3 -->
                <div class="ac-benefit-tile">
                    <div class="ac-tile-header">
                        <span class="ac-tile-emoji">🛡️</span>
                        <h4 class="ac-tile-title">Risk-Free Trial</h4>
                    </div>
                    <p class="ac-tile-description">
                        3-day free trial. You won't be charged until after. Cancel anytime, no questions asked!
                    </p>
                </div>
            </div>
            <div class="ac-waitlist-guarantee">
                <i class="fas fa-lock"></i>
                <span>100% Secure • No Hidden Fees • Cancel Anytime</span>
            </div>
        </div>
    </div>
</section>

<!-- AI Brain Orchestration Section -->
<section class="ac-ai-brain-section">
    <div class="ac-features-container">
        <div class="ac-section-header">
            <h2 class="ac-section-title">Agentic SEO for Shopify</h2>
            <p class="ac-section-subtitle">
                8 specialized agents. One AI brain. Your store growing 24/7.
            </p>
        </div>
        
        <div class="ac-brain-orchestration">
            <!-- Left side - Brain -->
            <div class="ac-brain-left">
                <div class="ac-brain-icon-container">
                    <div class="ac-brain-blob"></div>
                    <div class="ac-brain-inner">
                        <div class="ac-brain-icon">
                            <i class="fas fa-brain"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Center - Connection Line -->
            <div class="ac-brain-connection">
                <div class="ac-dotted-line">
                    <div class="ac-task-icon" data-agent="product">
                        <i class="fas fa-brain"></i>
                    </div>
                </div>
            </div>
            
            <!-- Right side - Dashboard -->
            <div class="ac-brain-dashboard">
                <div class="ac-dashboard-header">
                    <div class="ac-dashboard-dots">
                        <div class="ac-dashboard-dot red"></div>
                        <div class="ac-dashboard-dot yellow"></div>
                        <div class="ac-dashboard-dot green"></div>
                    </div>
                    <div class="ac-dashboard-title" style="font-family: var(--ac-font-heading);">AI Automation Center</div>
                </div>
                
                <div class="ac-dashboard-content">
                    <div class="ac-task-scheduled-indicator">
                        <i class="fas fa-check-circle"></i> TASK COMPLETE
                    </div>
                    
                    <div class="ac-notifications-header">
                        <div class="ac-live-dot"></div>
                        24/7 Growth
                    </div>
                    
                    <div class="ac-notifications-list">
                        <!-- Notifications will be dynamically added here when tasks complete -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- AI Sparkles -->
        <div class="ac-brain-sparkle ac-brain-sparkle-1">
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 0 L14 10 L24 12 L14 14 L12 24 L10 14 L0 12 L10 10 L12 0 Z" fill="#ef2b70"/>
            </svg>
        </div>
        <div class="ac-brain-sparkle ac-brain-sparkle-2 small">
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 0 L14 10 L24 12 L14 14 L12 24 L10 14 L0 12 L10 10 L12 0 Z" fill="#ef2b70"/>
            </svg>
        </div>
        <div class="ac-brain-sparkle ac-brain-sparkle-3">
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 0 L14 10 L24 12 L14 14 L12 24 L10 14 L0 12 L10 10 L12 0 Z" fill="#ef2b70"/>
            </svg>
        </div>
    </div>
</section>

<style>
/* AI Brain Orchestration Section Styles */
.ac-ai-brain-section {
    padding: 6rem 2rem;
    background: linear-gradient(180deg, #ffffff 0%, #f5f5f5 100%);
    position: relative;
    overflow: hidden;
}

.ac-brain-orchestration {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    gap: 3rem;
    min-height: 600px;
}

/* Left side - Clean Brain Icon */
.ac-brain-left {
    flex: 0 0 250px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding-left: 2rem;
}

.ac-brain-icon-container {
    width: 200px;
    height: 200px;
    position: relative;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
}

.ac-brain-blob {
    position: absolute;
    width: 320px;
    height: 320px;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, 
        rgba(239, 43, 112, 0.08) 0%, 
        rgba(255, 64, 133, 0.12) 30%,
        rgba(239, 43, 112, 0.06) 70%,
        rgba(255, 64, 133, 0.08) 100%);
    border: 1px solid rgba(239, 43, 112, 0.2);
    border-radius: 60% 40% 40% 60% / 40% 60% 40% 60%;
    animation: morphHeroBlob 20s ease-in-out infinite;
    z-index: 0;
    filter: blur(0.5px);
}

.ac-brain-inner {
    width: 120px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 2;
    animation: brainPulse 3s ease-in-out infinite;
}

.ac-brain-icon {
    font-size: 3.5rem;
    color: var(--ac-primary);
    animation: brainThink 2s ease-in-out infinite;
    z-index: 3;
    position: relative;
    filter: drop-shadow(0 4px 12px rgba(239, 43, 112, 0.3));
}

/* Center - Dotted Line Connection */
.ac-brain-connection {
    flex: 1;
    position: relative;
    height: 2px;
    margin: 0 2rem 0 3rem;
    min-width: 200px;
}

.ac-dotted-line {
    width: 100%;
    height: 2px;
    background: repeating-linear-gradient(
        to right,
        rgba(239, 43, 112, 0.3) 0px,
        rgba(239, 43, 112, 0.3) 6px,
        transparent 6px,
        transparent 12px
    );
    border-radius: 1px;
    position: relative;
    overflow: visible;
}

.ac-task-icon {
    position: absolute;
    top: -12px;
    left: -30px;
    width: 24px;
    height: 24px;
    background: linear-gradient(135deg, var(--ac-primary) 0%, #ff4085 100%);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.75rem;
    opacity: 0;
    box-shadow: 0 4px 12px rgba(239, 43, 112, 0.4);
    z-index: 10;
    transition: all 0.3s ease;
}

/* Right side - Dashboard Mockup */
.ac-brain-dashboard {
    flex: 0 0 400px;
    background: white;
    border-radius: 20px;
    box-shadow: 0 24px 80px rgba(0, 0, 0, 0.15);
    border: 2px solid #e5e7eb;
    overflow: hidden;
    position: relative;
    transition: all 0.4s ease;
}

.ac-dashboard-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.ac-dashboard-dots {
    display: flex;
    gap: 0.5rem;
}

.ac-dashboard-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #cbd5e1;
}

.ac-dashboard-dot.red { background: #ef4444; }
.ac-dashboard-dot.yellow { background: #f59e0b; }
.ac-dashboard-dot.green { background: #10b981; }

.ac-dashboard-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--ac-text-primary);
}

.ac-dashboard-content {
    padding: 2rem;
    height: 400px;
    position: relative;
    overflow: hidden;
}

.ac-task-scheduled-indicator {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 700;
    opacity: 0;
    transform: translateY(-10px) scale(0.8);
    transition: all 0.3s ease;
    z-index: 100;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.ac-notifications-header {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--ac-text-primary);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.ac-live-dot {
    width: 8px;
    height: 8px;
    background: #10b981;
    border-radius: 50%;
    animation: livePulse 2s ease-in-out infinite;
}

.ac-notifications-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

/* Brain Sparkles */
.ac-brain-sparkle {
    position: absolute;
    pointer-events: none;
}

.ac-brain-sparkle svg {
    width: 24px;
    height: 24px;
    fill: #ef2b70;
    opacity: 0.6;
    animation: sparkPulse 3s ease-in-out infinite;
}

.ac-brain-sparkle.small svg {
    width: 16px;
    height: 16px;
}

.ac-brain-sparkle.large svg {
    width: 32px;
    height: 32px;
}

/* Position sparkles around dashboard */
.ac-brain-sparkle-1 {
    top: 20%;
    right: 42%;
    animation-delay: 0s;
}

.ac-brain-sparkle-2 {
    bottom: 25%;
    right: -2%;
    animation-delay: 1.5s;
}

.ac-brain-sparkle-3 {
    top: 45%;
    right: -5%;
    animation-delay: 2.5s;
}

/* Animations */
@keyframes brainPulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes brainThink {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

@keyframes morphHeroBlob {
    0%, 100% {
        border-radius: 60% 40% 40% 60% / 40% 60% 40% 60%;
        transform: translate(-50%, -50%) rotate(0deg) scale(1);
    }
    20% {
        border-radius: 40% 60% 60% 40% / 60% 40% 60% 40%;
        transform: translate(-50%, -50%) rotate(72deg) scale(1.05);
    }
    40% {
        border-radius: 60% 40% 40% 60% / 40% 60% 40% 60%;
        transform: translate(-50%, -50%) rotate(144deg) scale(0.95);
    }
    60% {
        border-radius: 40% 60% 60% 40% / 60% 40% 60% 40%;
        transform: translate(-50%, -50%) rotate(216deg) scale(1.02);
    }
    80% {
        border-radius: 60% 40% 40% 60% / 40% 60% 40% 60%;
        transform: translate(-50%, -50%) rotate(288deg) scale(0.98);
    }
}

@keyframes livePulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.3;
    }
}

@keyframes sparkPulse {
    0%, 100% {
        opacity: 0.4;
        transform: scale(0.9);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.1);
    }
}

@keyframes iconTravel {
    0% {
        left: -30px;
        opacity: 0;
        transform: scale(0.8);
    }
    15% {
        opacity: 1;
        transform: scale(1);
    }
    85% {
        opacity: 1;
        transform: scale(1);
    }
    100% {
        left: calc(100% + 10px);
        opacity: 0;
        transform: scale(1.2);
    }
}

@keyframes notificationPop {
    0% {
        opacity: 0;
        transform: translateY(-15px) scale(0.95);
    }
    60% {
        opacity: 1;
        transform: translateY(-3px) scale(1.02);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes notificationSlideDown {
    0% {
        transform: translateY(-100%);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Notification styles */
.ac-notification {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.625rem 0.875rem;
    background: white;
    border-radius: 10px;
    border: 1px solid #e5e7eb;
    opacity: 0;
    transform: translateY(-15px) scale(0.95);
    animation: notificationPop 0.8s ease-out forwards;
    position: relative;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
}

.ac-notification:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.ac-notification-icon {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, var(--ac-primary) 0%, #ff4085 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
    flex-shrink: 0;
    box-shadow: 0 2px 8px rgba(239, 43, 112, 0.3);
}

.ac-notification-content {
    flex: 1;
}

.ac-notification-title {
    font-size: 0.8125rem;
    font-weight: 700;
    color: var(--ac-text-primary);
    margin-bottom: 0.125rem;
    line-height: 1.2;
}

.ac-notification-message {
    font-size: 0.75rem;
    color: var(--ac-text-secondary);
    line-height: 1.3;
}

.ac-notification-time {
    font-size: 0.6875rem;
    color: var(--ac-text-muted);
    font-weight: 500;
    margin-left: auto;
    flex-shrink: 0;
}

.ac-task-icon.traveling {
    animation: iconTravel 1.5s ease-in-out;
}

.ac-brain-dashboard.task-received {
    border-color: #10b981;
    box-shadow: 0 24px 80px rgba(16, 185, 129, 0.25);
    transform: scale(1.02);
}

.ac-brain-dashboard.task-scheduled {
    border-color: #10b981;
    box-shadow: 0 24px 80px rgba(16, 185, 129, 0.2);
}

.ac-task-scheduled-indicator.show {
    opacity: 1;
    transform: translateY(0) scale(1);
}

.ac-task-scheduled-indicator.hide {
    opacity: 0;
    transform: translateY(-10px) scale(0.8);
}

.ac-task-scheduled-indicator i {
    font-size: 0.875rem;
    margin-right: 0.25rem;
    vertical-align: middle;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .ac-brain-orchestration {
        gap: 2rem;
    }
    
    .ac-brain-left {
        flex: 0 0 230px;
        padding-left: 1rem;
    }
    
    .ac-brain-dashboard {
        flex: 0 0 380px;
    }
}

@media (max-width: 1024px) {
    .ac-brain-orchestration {
        flex-direction: column;
        gap: 3rem;
        text-align: center;
    }
    
    .ac-brain-left {
        flex: none;
        padding-left: 0;
    }
    
    .ac-brain-connection {
        display: none;
    }
    
    .ac-brain-dashboard {
        flex: none;
        width: 100%;
        max-width: 500px;
        margin: 0 auto;
    }
}

@media (max-width: 768px) {
    .ac-brain-icon-container {
        width: 150px;
        height: 150px;
    }
    
    .ac-brain-icon {
        font-size: 3rem;
    }
    
    .ac-dashboard-content {
        padding: 1.5rem;
        height: 350px;
    }
}

/* How Our Waitlist Works - Tiles Style */
.ac-waitlist-works-tiles {
    margin-top: 3rem;
    padding-top: 3rem;
    border-top: 1px solid rgba(0, 0, 0, 0.08);
}

.ac-waitlist-works-tiles .ac-tiles-title {
    font-size: 1.5rem;
    font-weight: 700;
    text-align: center;
    color: var(--ac-text-primary);
    margin-bottom: 2rem;
}

.ac-waitlist-works-tiles .ac-tile-description {
    font-size: 0.875rem;
    line-height: 1.5;
    color: var(--ac-text-secondary);
    margin: 0;
}

.ac-waitlist-guarantee {
    text-align: center;
    margin-top: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    color: var(--ac-text-secondary);
    font-size: 0.813rem;
    font-weight: 500;
}

.ac-waitlist-guarantee i {
    color: var(--ac-grove);
    font-size: 0.875rem;
}
</style>

<!-- How It Works Section -->
<section class="ac-how-it-works-section">
    <div class="ac-section-container">
        <div class="ac-section-header">
            <h2 class="ac-section-title">How Grove Replaces SEO Teams</h2>
            <p class="ac-subtitle">From signup to rankings in days. Here's exactly what happens.</p>
        </div>
        
        <div class="ac-process-timeline">
            <!-- Step 1 -->
            <div class="ac-process-step" data-aos="fade-up" data-aos-delay="100">
                <div class="ac-step-number">
                    <span>1</span>
                    <div class="ac-step-time">5 MIN</div>
                </div>
                <div class="ac-step-content">
                    <h3 class="ac-step-title">Connect Your Store</h3>
                    <p class="ac-step-description">One-click Shopify connection. Grove immediately starts scanning your catalog.</p>
                </div>
                <div class="ac-step-connector"></div>
            </div>
            
            <!-- Step 2 -->
            <div class="ac-process-step" data-aos="fade-up" data-aos-delay="200">
                <div class="ac-step-number">
                    <span>2</span>
                    <div class="ac-step-time">2 HRS</div>
                </div>
                <div class="ac-step-content">
                    <h3 class="ac-step-title">AI Learns Your Brand</h3>
                    <p class="ac-step-description">Grove analyzes your top products, brand voice, and customer reviews to match your style.</p>
                </div>
                <div class="ac-step-connector"></div>
            </div>
            
            <!-- Step 3 -->
            <div class="ac-process-step" data-aos="fade-up" data-aos-delay="300">
                <div class="ac-step-number">
                    <span>3</span>
                    <div class="ac-step-time">24 HRS</div>
                </div>
                <div class="ac-step-content">
                    <h3 class="ac-step-title">Bulk AI Automation</h3>
                    <p class="ac-step-description">New descriptions, meta tags, and image alt text. All optimized for your target keywords.</p>
                </div>
                <div class="ac-step-connector"></div>
            </div>
            
            <!-- Step 4 -->
            <div class="ac-process-step" data-aos="fade-up" data-aos-delay="400">
                <div class="ac-step-number">
                    <span>4</span>
                    <div class="ac-step-time ac-time-continuous">DAILY</div>
                </div>
                <div class="ac-step-content">
                    <h3 class="ac-step-title">Never Stop Improving</h3>
                    <p class="ac-step-description">AI monitors rankings, creates new collections, publishes content, and optimizes 24/7.</p>
                </div>
            </div>
        </div>
        
        <!-- Comparison Table -->
        <div class="ac-comparison-card" style="margin-top: 4rem;">
            <h3 class="ac-comparison-title">Why Stores Are Firing Their Agencies</h3>
            <div class="ac-table-container">
                <table class="ac-comparison-table">
                    <thead>
                        <tr>
                            <th></th>
                            <th class="ac-table-header-agency">
                                <i class="fas fa-building"></i>
                                Traditional Agency
                            </th>
                            <th class="ac-table-header-grove">
                                <i class="fas fa-rocket"></i>
                                Grove AI
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="ac-table-feature">Monthly Cost</td>
                            <td class="ac-table-agency">
                                <span class="ac-cost-bad">$3,000 - $10,000</span>
                            </td>
                            <td class="ac-table-grove">
                                <span class="ac-cost-good">
                                    $99/mo
                                    <span class="ac-cost-original">$499/mo</span>
                                </span>
                                <span class="ac-savings-badge">Get 80% Off - Reserve for $1</span>
                            </td>
                        </tr>
                        <tr>
                            <td class="ac-table-feature">Implementation Speed</td>
                            <td class="ac-table-agency">Weeks per change</td>
                            <td class="ac-table-grove">
                                <strong>Instant updates</strong>
                                <i class="fas fa-zap ac-speed-icon"></i>
                            </td>
                        </tr>
                        <tr>
                            <td class="ac-table-feature">Working Hours</td>
                            <td class="ac-table-agency">9-5 weekdays</td>
                            <td class="ac-table-grove">
                                <strong>24/7/365</strong>
                                <i class="fas fa-infinity ac-infinity-icon"></i>
                            </td>
                        </tr>
                        <tr>
                            <td class="ac-table-feature">Time to Results</td>
                            <td class="ac-table-agency">3-6 months</td>
                            <td class="ac-table-grove">
                                <strong>Days</strong>
                                <i class="fas fa-bolt ac-speed-icon"></i>
                            </td>
                        </tr>
                        <tr>
                            <td class="ac-table-feature">Contract Lock-in</td>
                            <td class="ac-table-agency">12-24 months</td>
                            <td class="ac-table-grove">
                                <strong>Cancel anytime</strong>
                                <i class="fas fa-shield-alt ac-shield-icon"></i>
                            </td>
                        </tr>
                        <tr>
                            <td class="ac-table-feature">Actual Work Done</td>
                            <td class="ac-table-agency">Reports & recommendations</td>
                            <td class="ac-table-grove">
                                <strong>Complete implementation</strong>
                                <i class="fas fa-check-circle ac-check-icon"></i>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</section>



<!-- FAQ Section -->
<section class="ac-faq-section">
    <div class="ac-section-container">
        <div class="ac-section-header">
            <h2 class="ac-section-title">Frequently Asked Questions</h2>
            <p class="ac-subtitle">Everything you need to know about joining Grove</p>
        </div>
        
        <div class="ac-faq-grid">
            <div class="ac-faq-card" data-aos="fade-up" data-aos-delay="100">
                <div class="ac-faq-icon">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <h3 class="ac-faq-question">Do I need to pay anything today?</h3>
                <p class="ac-faq-answer">Just $1 to reserve your spot. Fully refundable until launch. We only charge the monthly fee when Grove launches and you activate.</p>
            </div>
            
            <div class="ac-faq-card" data-aos="fade-up" data-aos-delay="150">
                <div class="ac-faq-icon">
                    <i class="fas fa-tag"></i>
                </div>
                <h3 class="ac-faq-question">Why only 100 spots at this price?</h3>
                <p class="ac-faq-answer">We need case studies. Get 80% Off - Reserve for $1 in exchange for letting us showcase their results.</p>
            </div>
            
            <div class="ac-faq-card" data-aos="fade-up" data-aos-delay="200">
                <div class="ac-faq-icon">
                    <i class="fas fa-magic"></i>
                </div>
                <h3 class="ac-faq-question">How is this different from other SEO apps?</h3>
                <p class="ac-faq-answer">Other apps show you problems. Grove fixes them. While they generate reports, we're rewriting your entire catalog with AI.</p>
            </div>
            
            <div class="ac-faq-card" data-aos="fade-up" data-aos-delay="250">
                <div class="ac-faq-icon">
                    <i class="fas fa-calendar"></i>
                </div>
                <h3 class="ac-faq-question">When does Grove launch?</h3>
                <p class="ac-faq-answer">Grove will rollout to waitlist members over the coming 60 days. Early access members get in first. The sooner you join, the sooner you start outranking competitors.</p>
            </div>
            
            <div class="ac-faq-card" data-aos="fade-up" data-aos-delay="300">
                <div class="ac-faq-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h3 class="ac-faq-question">What if it doesn't work for my store?</h3>
                <p class="ac-faq-answer">90-day money-back guarantee. If your organic traffic doesn't increase, get a full refund. Simple as that.</p>
            </div>
            
            <div class="ac-faq-card" data-aos="fade-up" data-aos-delay="350">
                <div class="ac-faq-icon">
                    <i class="fas fa-exchange-alt"></i>
                </div>
                <h3 class="ac-faq-question">I already pay for SEO. Why switch?</h3>
                <p class="ac-faq-answer">Because you're paying for reports, not results. Grove does in hours what agencies take months to do - at 10X less cost.</p>
            </div>
            
            <div class="ac-faq-card" data-aos="fade-up" data-aos-delay="400">
                <div class="ac-faq-icon">
                    <i class="fas fa-crown"></i>
                </div>
                <h3 class="ac-faq-question">What's special about being a founding member?</h3>
                <p class="ac-faq-answer">Founding members get 80% Off - Reserve for $1, direct WhatsApp access to our founders, 2x faster processing, and your price is locked forever. Plus, you get a dedicated account manager and early access to all new features.</p>
            </div>
            
            <div class="ac-faq-card" data-aos="fade-up" data-aos-delay="450">
                <div class="ac-faq-icon">
                    <i class="fas fa-users"></i>
                </div>
                <h3 class="ac-faq-question">How many founding member spots are available?</h3>
                <p class="ac-faq-answer">Only 100 stores can become founding members. Once these spots are filled, the founding member benefits are gone forever. New members will pay the full $499/mo with standard support.</p>
            </div>
        </div>
        
        <!-- Final CTA -->
        <div class="ac-final-cta">
            <h3 class="ac-cta-title">Ready to Fire Your SEO Agency?</h3>
            <p class="ac-cta-subtitle">Join 180 stores already winning with AI</p>
            <button class="ac-btn ac-btn-primary ac-btn-xl ac-btn-glow" onclick="scrollToForm()">
                <i class="fas fa-rocket"></i> Get 80% Off - Reserve for $1
            </button>
            <p class="ac-cta-disclaimer">
                <i class="fas fa-lock"></i> Secure payment • Cancel anytime • 90-day guarantee
            </p>
        </div>
    </div>
</section>

    </main>
    
    <!-- Footer -->
    <footer class="footer">
        <div class="footer-container">
            <div class="footer-brand">
                <img src="static/images/grove-logo.png" alt="Grove">
                <p>The world's first agentic SEO app for Shopify. Replace your $5,000/mo agency with AI that actually works.</p>
            </div>
            
            <div class="footer-columns">
                <div class="footer-column">
                    <h4>Product</h4>
                    <ul>
                        <li><a href="features.html">All Features</a></li>
                        <li><a href="pricing.html">Pricing</a></li>
                        <li><a href="pre-registration.html">Join Waitlist</a></li>
                        <li><a href="about.html">How It Works</a></li>
                        <li><a href="auth/login.html" class="footer-login">Customer Login</a></li>
                    </ul>
                </div>
                
                <div class="footer-column">
                    <h4>Features</h4>
                    <ul>
                        <li><a href="features.html#product-seo-engine">Product SEO Engine</a></li>
                        <li><a href="features.html#smart-collections">Smart Collections</a></li>
                        <li><a href="features.html#search-insights">Search Insights</a></li>
                        <li><a href="features.html#content-machine">Content Machine</a></li>
                        <li><a href="features.html#technical-seo">Technical SEO</a></li>
                        <li><a href="features.html#competitor-intel">Competitor Intel</a></li>
                        <li><a href="features.html#link-building">Link Building</a></li>
                        <li><a href="features.html#translations">Translations</a></li>
                    </ul>
                </div>
                
                <div class="footer-column">
                    <h4>Resources</h4>
                    <ul>
                        <li><a href="blog.html">Blog</a></li>
                        <li><a href="shopify-seo-guide.html">Complete SEO Guide</a></li>
                        <li><a href="shopify-seo-checklist.html">SEO Checklist</a></li>
                    </ul>
                </div>
                
                <div class="footer-column">
                    <h4>Company</h4>
                    <ul>
                        <li><a href="about.html">About Grove</a></li>
                        <li><a href="contact.html">Contact Us</a></li>
                        <li><a href="privacy.html">Privacy Policy</a></li>
                        <li><a href="terms.html">Terms of Service</a></li>
                    </ul>
                    <div class="footer-social">
                        <a href="https://twitter.com/groveai" class="social-link">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="https://linkedin.com/company/groveai" class="social-link">
                            <i class="fab fa-linkedin"></i>
                        </a>
                        <a href="https://youtube.com/@groveai" class="social-link">
                            <i class="fab fa-youtube"></i>
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p class="footer-copyright">© 2025 Grove AI Inc. All rights reserved.</p>
                <p class="footer-tagline">
                    <i class="fas fa-robot"></i> AI That Actually Does SEO, Not Just Reports It
                </p>
            </div>
        </div>
    </footer>
    
    <!-- Scripts -->
    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add scroll effect to navigation
        window.addEventListener('scroll', function() {
            const nav = document.querySelector('.ac-nav');
            if (window.scrollY > 50) {
                nav.classList.add('scrolled');
            } else {
                nav.classList.remove('scrolled');
            }
        });
        
        // Mega Menu Interactions with Smart Positioning
        const dropdowns = document.querySelectorAll('.ac-nav-dropdown');
        let closeTimeout;
        
        dropdowns.forEach(dropdown => {
            const megaMenu = dropdown.querySelector('.ac-mega-menu');
            const simpleMenu = dropdown.querySelector('.ac-dropdown-menu');
            let openTimeout;
            
            dropdown.addEventListener('mouseenter', function() {
                clearTimeout(closeTimeout);
                clearTimeout(openTimeout);
                
                // Small delay before opening to prevent accidental hovers
                openTimeout = setTimeout(() => {
                    // Close other dropdowns
                    dropdowns.forEach(d => {
                        if (d !== dropdown) {
                            d.classList.remove('active');
                        }
                    });
                    dropdown.classList.add('active');
                    
                    // Smart positioning for mega menus
                    if (megaMenu) {
                        const rect = dropdown.getBoundingClientRect();
                        const menuWidth = 900; // Match CSS width
                        const viewportWidth = window.innerWidth;
                        
                        // Reset classes
                        megaMenu.classList.remove('align-right', 'align-center');
                        
                        // Check if menu would go off right edge
                        if (rect.left + menuWidth > viewportWidth - 20) {
                            // Check if there's enough space on the left
                            if (rect.right - menuWidth > 20) {
                                megaMenu.classList.add('align-right');
                            } else {
                                // Center it if no good option
                                megaMenu.classList.add('align-center');
                            }
                        }
                        // Check if menu would go off left edge
                        else if (rect.left < 20) {
                            // Keep default left alignment
                        }
                    }
                }, 50); // 50ms delay
            });
            
            dropdown.addEventListener('mouseleave', function() {
                clearTimeout(openTimeout); // Cancel opening if user leaves quickly
                closeTimeout = setTimeout(() => {
                    dropdown.classList.remove('active');
                }, 200);
            });
        });
        
        // Mobile Menu
        const mobileToggle = document.querySelector('.ac-mobile-toggle');
        const mobileMenu = document.querySelector('.ac-mobile-menu');
        const mobileClose = document.querySelector('.ac-mobile-close');
        
        if (mobileToggle) {
            mobileToggle.addEventListener('click', function() {
                mobileMenu.style.display = 'block';
                // Small delay to ensure display change takes effect before animation
                setTimeout(() => {
                    mobileMenu.classList.add('active');
                }, 10);
                document.body.style.overflow = 'hidden';
            });
        }
        
        if (mobileClose) {
            mobileClose.addEventListener('click', function() {
                mobileMenu.classList.remove('active');
                document.body.style.overflow = '';
                // Hide after transition completes
                setTimeout(() => {
                    mobileMenu.style.display = 'none';
                }, 300);
            });
        }
        
        // Close mobile menu on outside click
        mobileMenu?.addEventListener('click', function(e) {
            if (e.target === mobileMenu) {
                mobileMenu.classList.remove('active');
                document.body.style.overflow = '';
                // Hide after transition completes
                setTimeout(() => {
                    mobileMenu.style.display = 'none';
                }, 300);
            }
        });
        
        // Prevent dropdown closing on click inside
        const megaMenus = document.querySelectorAll('.ac-mega-menu');
        megaMenus.forEach(menu => {
            menu.addEventListener('click', function(e) {
                e.stopPropagation();
            });
        });
    </script>
    
    
<!-- Stripe.js -->
<script src="https://js.stripe.com/v3/"></script>

<!-- AOS Animation Library -->
<link href="../unpkg.com/aos%402.3.1/dist/aos.css" rel="stylesheet">
<script src="../unpkg.com/aos%402.3.1/dist/aos.js"></script>
<script>
// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing form...');
    
    // iOS payment button debug logging
    setTimeout(() => {
        const paymentBtn = document.getElementById('submitBtn');
        if (paymentBtn) {
            paymentBtn.addEventListener('click', function(e) {
                // Log to server for iOS debugging
                const termsCheckbox = document.getElementById('terms_consent');
                const stripeReady = {
                    stripe: typeof stripe !== 'undefined' && stripe !== null,
                    elements: typeof elements !== 'undefined' && elements !== null,
                    paymentElement: typeof paymentElement !== 'undefined' && paymentElement !== null
                };
                
                fetch('/api/ios-payment-debug', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        event: 'payment_button_clicked',
                        details: {
                            buttonText: this.innerText,
                            formStep: currentStep,
                            timestamp: new Date().toISOString(),
                            termsChecked: termsCheckbox ? termsCheckbox.checked : 'checkbox not found',
                            stripeReady: stripeReady,
                            formId: document.getElementById('waitlistForm') ? 'form exists' : 'form not found'
                        }
                    })
                }).catch(() => {}); // Ignore errors
                
                // On iOS, manually trigger form submission if everything is ready
                if (/iPhone|iPad|iPod/.test(navigator.userAgent)) {
                    if (termsCheckbox && termsCheckbox.checked && stripeReady.stripe && stripeReady.elements && stripeReady.paymentElement) {
                        // Manually trigger form submission on iOS
                        setTimeout(() => {
                            const form = document.getElementById('waitlistForm');
                            if (form) {
                                console.log('iOS: Manually triggering form submission');
                                const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
                                form.dispatchEvent(submitEvent);
                            }
                        }, 100);
                    }
                }
            });
        }
    }, 1000); // Wait for form to fully load
});

// Form state
let currentStep = 1;
const totalSteps = 3;
let formData = {};

// Stripe configuration
let stripe;
let elements;
let paymentElement;

// Initialize Stripe after a small delay to ensure the library is loaded
setTimeout(() => {
    try {
        if (typeof Stripe !== 'undefined') {
            stripe = Stripe('pk_live_51Rb8rSLehTIYisPPmXwcaJbUI4fJDJkUCDla50RNn4786P5stbNmcbJXntPjdVRzTN9vw5sKrVtkNXcwsASYOEgh00pQA245i7');
            console.log('Stripe initialized with key:', stripe._apiKey ? stripe._apiKey.substring(0, 20) + '...' : 'NO KEY');
            
            // Define appearance for Stripe Elements
            const appearance = {
                theme: 'stripe',
                variables: {
                    colorPrimary: '#ef2b70',
                    colorBackground: '#ffffff',
                    colorSurface: '#ffffff',
                    colorText: '#1f2937',
                    colorDanger: '#ef4444',
                    fontFamily: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                    spacingUnit: '4px',
                    borderRadius: '8px'
                }
            };
            
            // We'll initialize elements when we have the client secret
            console.log('Stripe initialized successfully');
            
            // Try to initialize payment element if DOM is ready
            if (document.readyState === 'complete' || document.readyState === 'interactive') {
                initializeStripe();
            }
        } else {
            console.error('Stripe library not loaded');
        }
    } catch (error) {
        console.error('Stripe initialization error:', error);
        // Continue without Stripe for now so form navigation works
    }
}, 100);

// Scroll to form function
function scrollToForm() {
    const formSection = document.querySelector('.pre-reg-hero');
    if (formSection) {
        formSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
}

// Update waitlist counter animation
function animateCounter() {
    const counter = document.getElementById('waitlistCount');
    // Check if the element exists before trying to access it
    if (!counter) {
        console.warn('waitlistCount element not found, skipping counter animation');
        return;
    }
    
    const target = parseInt(counter.textContent);
    if (isNaN(target)) {
        console.warn('Invalid counter value, skipping animation');
        return;
    }
    
    const increment = Math.ceil(target / 100);
    let current = 0;
    
    const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
            counter.textContent = target;
            clearInterval(timer);
        } else {
            counter.textContent = current;
        }
    }, 20);
}

// Initialize Stripe Elements separately to ensure it runs
function initializeStripe() {
    // Elements will be initialized when we have a client secret
    // This function is kept for compatibility but no longer needed
    console.log('Stripe initialization will happen when payment intent is created');
}

// Run on load
window.addEventListener('DOMContentLoaded', () => {
    // Try to run animations (non-critical)
    try {
        animateCounter();
    } catch (e) {
        console.warn('Counter animation error:', e);
    }
    
    try {
        animateAutomationCards();
    } catch (e) {
        console.warn('Automation cards animation error:', e);
    }
    
    // Initialize Stripe (critical)
    initializeStripe();
});

// Add scroll effect to navigation
window.addEventListener('scroll', function() {
    const nav = document.querySelector('.ac-nav');
    if (nav) {
        if (window.scrollY > 50) {
            nav.classList.add('scrolled');
        } else {
            nav.classList.remove('scrolled');
        }
    }
});

// Animate automation cards on scroll
function animateAutomationCards() {
    const cards = document.querySelectorAll('.automation-card');
    
    // Check if any automation cards exist
    if (!cards || cards.length === 0) {
        console.warn('No automation-card elements found, skipping animation');
        return;
    }
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.style.opacity = '0';
                    entry.target.style.transform = 'translateY(20px)';
                    entry.target.style.transition = 'all 0.6s ease';
                    
                    setTimeout(() => {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }, 50);
                }, index * 200);
                
                observer.unobserve(entry.target);
            }
        });
    }, { threshold: 0.1 });
    
    cards.forEach(card => {
        card.style.opacity = '0';
        observer.observe(card);
    });
}

// Form navigation
function updateProgress() {
    // Update step indicators
    for (let i = 1; i <= totalSteps; i++) {
        const indicator = document.getElementById(`step${i}-indicator`);
        if (i < currentStep) {
            indicator.classList.add('completed');
            indicator.classList.remove('active');
        } else if (i === currentStep) {
            indicator.classList.add('active');
            indicator.classList.remove('completed');
        } else {
            indicator.classList.remove('active', 'completed');
        }
    }
}

function showStep(step) {
    // Hide all steps
    document.querySelectorAll('.form-step').forEach(el => {
        el.classList.remove('active');
    });
    
    // Show current step
    document.getElementById(`step${step}`).classList.add('active');
    currentStep = step;
    updateProgress();
    
    // Mount Stripe payment element when showing step 3
    if (step === 3) {
        // We need to create payment intent first to get client secret
        // This will be done when the user submits the form
        // For now, just prepare the container
        setTimeout(() => {
            const paymentElementContainer = document.getElementById('payment-element');
            console.log('Step 3 - Ready for payment element:', {
                stripe: !!stripe,
                container: !!paymentElementContainer
            });
            
            if (!stripe) {
                console.error('Stripe not initialized');
                // Show a message to the user
                if (paymentElementContainer) {
                    paymentElementContainer.innerHTML = '<p style="color: #ef4444; padding: 1rem;">Payment system loading error. Please refresh the page.</p>';
                }
            }
        }, 100); // Small delay to ensure DOM is ready
    }
}

function nextStep() {
    // Validate current step
    const currentStepEl = document.getElementById(`step${currentStep}`);
    const inputs = currentStepEl.querySelectorAll('input[required], select[required]');
    
    for (let input of inputs) {
        if (!input.value.trim()) {
            input.focus();
            input.style.borderColor = '#ef4444';
            setTimeout(() => {
                input.style.borderColor = '';
            }, 3000);
            return;
        }
    }
    
    // Save form data
    const formElements = currentStepEl.querySelectorAll('input, select, textarea');
    formElements.forEach(el => {
        if (el.type === 'checkbox') {
            formData[el.name] = el.checked;
        } else {
            formData[el.name] = el.value;
        }
    });
    
    // If moving to payment step, create payment intent first
    if (currentStep === 2) {
        createPaymentIntentAndShowStep3();
    } else {
        showStep(currentStep + 1);
    }
}

// New function to create payment intent and initialize Payment Element
async function createPaymentIntentAndShowStep3() {
    // Show loading state
    const nextBtn = document.querySelector(`#step${currentStep} .form-nav button[onclick*="nextStep"]`);
    if (nextBtn) {
        nextBtn.disabled = true;
        nextBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading payment...';
    }
    
    try {
        // First, create pre-registration and get payment intent
        const response = await fetch('/api/pre-registration', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });
        
        const result = await response.json();
        
        if (!response.ok) {
            throw new Error(result.error || 'Registration failed');
        }
        
        // Store waitlist ID for later
        formData.waitlist_id = result.waitlist_id;
        
        // Initialize elements with the client secret
        const appearance = {
            theme: 'stripe',
            variables: {
                colorPrimary: '#ef2b70',
                colorBackground: '#ffffff',
                colorSurface: '#ffffff',
                colorText: '#1f2937',
                colorDanger: '#ef4444',
                fontFamily: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                spacingUnit: '4px',
                borderRadius: '8px'
            }
        };
        
        elements = stripe.elements({ clientSecret: result.client_secret, appearance });
        
        // Create payment element with PayPal support
        paymentElement = elements.create('payment', {
            defaultValues: {
                billingDetails: {
                    name: formData.name || '',
                    email: formData.email || ''
                }
            },
            fields: {
                billingDetails: {
                    address: 'auto'
                }
            },
            // Ensure PayPal and other payment methods are shown
            wallets: {
                applePay: 'never',  // Disable Apple Pay for now
                googlePay: 'never'  // Disable Google Pay for now
            }
        });
        
        // Handle payment element events
        paymentElement.on('change', function(event) {
            const displayError = document.getElementById('payment-errors');
            if (displayError) {
                if (event.error) {
                    displayError.textContent = event.error.message;
                } else {
                    displayError.textContent = '';
                }
            }
        });
        
        // Move to step 3
        showStep(3);
        
        // Mount payment element after showing step 3
        setTimeout(() => {
            const container = document.getElementById('payment-element');
            if (container && paymentElement) {
                paymentElement.mount('#payment-element');
                console.log('Payment element mounted successfully');
                console.log('Payment methods available:', result);
            } else {
                console.error('Payment element container or element not found');
            }
        }, 100);
        
    } catch (error) {
        console.error('Error creating payment intent:', error);
        alert(error.message || 'An error occurred. Please try again.');
        
        // Re-enable button
        if (nextBtn) {
            nextBtn.disabled = false;
            nextBtn.innerHTML = 'Next Step <i class="fas fa-arrow-right"></i>';
        }
    }
}

function prevStep() {
    if (currentStep > 1) {
        showStep(currentStep - 1);
    }
}

// URL parameter handling
const urlParams = new URLSearchParams(window.location.search);

// Handle referral code
const refCode = urlParams.get('ref');
if (refCode && document.getElementById('referral_code')) {
    document.getElementById('referral_code').value = refCode;
    formData.referral_code = refCode;
}

// Handle store URL from homepage
const storeUrlParam = urlParams.get('store');
if (storeUrlParam && document.getElementById('store_url')) {
    document.getElementById('store_url').value = storeUrlParam;
    formData.store_url = storeUrlParam;
}

// Form submission
document.getElementById('waitlistForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    console.log('Form submitted, processing payment...');
    
    // Log form submission attempt
    fetch('/api/ios-payment-debug', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            event: 'form_submitted',
            details: {
                message: 'Form submission handler triggered',
                timestamp: new Date().toISOString()
            }
        })
    }).catch(() => {});
    
    // Collect final step data
    const finalStepEl = document.getElementById('step3');
    const formElements = finalStepEl.querySelectorAll('input, select, textarea');
    formElements.forEach(el => {
        if (el.type === 'checkbox') {
            formData[el.name] = el.checked;
        } else if (el.name) {
            formData[el.name] = el.value;
        }
    });
    
    // Validate consent
    if (!formData.terms_consent) {
        alert('Please agree to the terms to continue.');
        return;
    }
    
    // Show loading state
    const submitBtn = document.getElementById('submitBtn');
    const originalText = submitBtn.innerHTML;
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing payment...';
    
    try {
        // Confirm payment with Stripe Payment Element
        const { error, paymentIntent } = await stripe.confirmPayment({
            elements,
            confirmParams: {
                return_url: window.location.origin + `/pre-registration-thank-you?id=${formData.waitlist_id}`,
            },
            redirect: 'if_required'  // This allows us to handle the result without redirect for card payments
        });
        
        if (error) {
            // This point will only be reached if there's an error or if the payment
            // requires no redirect (like card payments)
            if (error.type === 'card_error' || error.type === 'validation_error') {
                throw new Error(error.message);
            } else {
                // For other errors, confirm with backend
                throw new Error(error.message);
            }
        } else if (paymentIntent && paymentIntent.status === 'succeeded') {
            // Payment succeeded without redirect (card payment)
            // Verify with backend before showing success
            const verifyResponse = await fetch('/api/pre-registration/verify-payment-status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    payment_intent_id: paymentIntent.id,
                    waitlist_id: formData.waitlist_id
                })
            });
            
            const verifyResult = await verifyResponse.json();
            
            if (verifyResponse.ok && verifyResult.status === 'completed') {
                // Payment verified - redirect to thank you page
                window.location.href = `/pre-registration-thank-you?id=${formData.waitlist_id}&verified=true`;
            } else {
                throw new Error('Payment verification failed. Please contact support if you were charged.');
            }
        } else {
            // If we get here without an error or payment intent, redirect to verification
            // This handles edge cases where payment might still be processing
            if (formData.payment_intent_id) {
                window.location.href = `/pre-registration-thank-you?payment_intent=${formData.payment_intent_id}&id=${formData.waitlist_id}`;
            } else {
                throw new Error('Payment processing error. Please try again.');
            }
        }
    } catch (error) {
        console.error('Submission error:', error);
        
        // Show error in payment errors div
        const paymentErrors = document.getElementById('payment-errors');
        if (paymentErrors) {
            paymentErrors.textContent = error.message || 'An error occurred. Please try again.';
        }
        
        // If it's a duplicate email, show special message
        if (error.message.includes('already registered')) {
            alert('This email is already registered. Check your email for your referral code.');
        } else {
            alert(error.message || 'An error occurred. Please try again.');
        }
        
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    }
});

// Copy referral link
function copyReferralLink() {
    const code = document.getElementById('referralCode').textContent;
    const link = `${window.location.origin}/pre-registration?ref=${code}`;
    
    navigator.clipboard.writeText(link).then(() => {
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check"></i> Copied!';
        btn.style.background = 'var(--ac-grove)';
        btn.style.color = 'white';
        
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.style.background = '';
            btn.style.color = '';
        }, 2000);
    });
}

// Play demo video
function playDemo() {
    const button = event.currentTarget;
    const placeholder = button.closest('.ac-video-placeholder');
    
    // Create video element
    const video = document.createElement('div');
    video.innerHTML = `
        <iframe 
            src="https://www.youtube.com/embed/dQw4w9WgXcQ?autoplay=1" 
            width="100%" 
            height="400" 
            frameborder="0" 
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
            allowfullscreen
            style="border-radius: 12px;">
        </iframe>
    `;
    
    // Replace placeholder with video
    placeholder.innerHTML = video.innerHTML;
}

// Initialize AOS animations
if (typeof AOS !== 'undefined') {
    AOS.init({
        duration: 800,
        easing: 'ease-out',
        once: true,
        offset: 100
    });
}

// Add parallax effect to hero section
window.addEventListener('scroll', () => {
    const scrolled = window.pageYOffset;
    const parallax = document.querySelector('.pre-reg-hero::before');
    if (parallax) {
        const speed = 0.5;
        parallax.style.transform = `translateY(${scrolled * speed}px)`;
    }
});

// Animate metrics on scroll
function animateValue(element, start, end, duration) {
    let current = start;
    const range = end - start;
    const increment = end > start ? 1 : -1;
    const stepTime = Math.abs(Math.floor(duration / range));
    
    const timer = setInterval(() => {
        current += increment;
        element.textContent = current + (element.dataset.suffix || '');
        if (current === end) {
            clearInterval(timer);
        }
    }, stepTime);
}

// Intersection observer for metric animations
const metricObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            const element = entry.target;
            const end = parseInt(element.dataset.value);
            animateValue(element, 0, end, 1500);
            metricObserver.unobserve(element);
        }
    });
}, { threshold: 0.5 });

// Observe all metric values
document.querySelectorAll('[data-value]').forEach(el => {
    metricObserver.observe(el);
});

// Add hover effects to comparison table
document.querySelectorAll('.ac-comparison-table tbody tr').forEach(row => {
    row.addEventListener('mouseenter', () => {
        row.style.transform = 'translateX(8px)';
    });
    
    row.addEventListener('mouseleave', () => {
        row.style.transform = 'translateX(0)';
    });
});

// Smooth scroll enhancement
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start',
                inline: 'nearest'
            });
        }
    });
});

// Toggle automation function
function toggleAutomation(type) {
    const card = document.getElementById('automation-' + type);
    const checkbox = card.querySelector('input[type="checkbox"]');
    const statusText = card.querySelector('.ac-automation-status span:last-child');
    const statusDot = card.querySelector('.ac-automation-status-dot');
    
    // Mobile-specific behavior (matching homepage)
    if (window.innerWidth <= 767) {
        const liveStrip = card.querySelector('.ac-agent-live-strip');
        const liveIndicator = card.querySelector('.ac-live-indicator');
        const statusRing = card.querySelector('.ac-agent-status-ring-rounded');
        
        if (checkbox.checked) {
            card.classList.add('active');
            
            // Animate the status ring if it exists
            if (statusRing) {
                statusRing.style.animation = 'pulse-ring 2s infinite';
            }
            
            // Animate the live indicator
            if (liveIndicator) {
                liveIndicator.style.animation = 'pulse-dot 2s infinite';
            }
            
            // Show the live strip with animation
            if (liveStrip) {
                liveStrip.style.opacity = '1';
                liveStrip.style.transform = 'translateY(0)';
            }
            
            // Update status if it exists (pre-reg specific)
            if (statusText) {
                statusText.textContent = 'Active - Running 24/7';
            }
            if (statusDot) {
                statusDot.style.background = '#22c55e';
                statusDot.style.animation = 'pulse 2s infinite';
            }
            
            // Animate feature icons
            const featureIcons = card.querySelectorAll('.ac-automation-feature-icon');
            featureIcons.forEach((icon, index) => {
                setTimeout(() => {
                    icon.style.transform = 'scale(1.2)';
                    setTimeout(() => {
                        icon.style.transform = 'scale(1)';
                    }, 200);
                }, index * 100);
            });
        } else {
            card.classList.remove('active');
            
            // Reset animations
            if (statusRing) {
                statusRing.style.animation = '';
            }
            if (liveIndicator) {
                liveIndicator.style.animation = '';
            }
            if (liveStrip) {
                liveStrip.style.opacity = '0';
                liveStrip.style.transform = 'translateY(10px)';
            }
            
            // Update status if it exists (pre-reg specific)
            if (statusText) {
                statusText.textContent = 'Inactive - Click to activate';
            }
            if (statusDot) {
                statusDot.style.background = '#dc2626';
                statusDot.style.animation = 'none';
            }
        }
    } else {
        // Desktop behavior (original)
        if (checkbox.checked) {
            card.classList.add('active');
            statusText.textContent = 'Active - Running 24/7';
            statusDot.style.background = '#22c55e';
            statusDot.style.animation = 'pulse 2s infinite';
            
            // Animate feature icons
            const featureIcons = card.querySelectorAll('.ac-automation-feature-icon');
            featureIcons.forEach((icon, index) => {
                setTimeout(() => {
                    icon.style.transform = 'scale(1.2)';
                    setTimeout(() => {
                        icon.style.transform = 'scale(1)';
                    }, 200);
                }, index * 100);
            });
        } else {
            card.classList.remove('active');
            statusText.textContent = 'Inactive - Click to activate';
            statusDot.style.background = '#dc2626';
            statusDot.style.animation = 'none';
        }
    }
}

// Scroll-triggered automation activation (MOBILE ONLY) - matching homepage
if (window.innerWidth <= 767) {
    const automationObserverOptions = {
        threshold: 0.5,
        rootMargin: '-100px'
    };

    let automationActivated = false;

    const automationObserver = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting && !automationActivated) {
                automationActivated = true;
                console.log('Automation section in view, starting sequence...');
                
                // Activate automations in sequence - matching the 8 on pre-reg page
                const automationTypes = ['products', 'collections', 'search', 'content', 'technical', 'competitor', 'linkbuilding', 'translations'];
                automationTypes.forEach((type, index) => {
                    setTimeout(() => {
                        const card = document.getElementById(`automation-${type}`);
                        const checkbox = card ? card.querySelector('input[type="checkbox"]') : null;
                        console.log(`Activating ${type}:`, card, checkbox);
                        if (checkbox && !checkbox.checked) {
                            checkbox.checked = true;
                            toggleAutomation(type);
                        }
                    }, index * 400); // Stagger activations
                });
            }
        });
    }, automationObserverOptions);

    // Observe the automation section
    const automationSection = document.querySelector('.ac-automation-section');
    if (automationSection) {
        automationObserver.observe(automationSection);
        console.log('Observing automation section:', automationSection);
    } else {
        console.error('Automation section not found!');
    }
}

// Animate automation cards icon blob on page load
document.querySelectorAll('.ac-automation-feature-icon').forEach(icon => {
    icon.style.transition = 'transform 0.3s ease';
});


// Scroll-triggered automation activation
const automationObserverOptions = {
    threshold: 0.3,
    rootMargin: '0px'
};

let automationActivated = false;

const automationObserver = new IntersectionObserver(function(entries) {
    entries.forEach(entry => {
        if (entry.isIntersecting && !automationActivated) {
            automationActivated = true;
            console.log('Automation section in view, activating cards...');
            
            // Activate all automations in sequence
            const automationTypes = ['products', 'collections', 'search', 'content', 'technical', 'competitor', 'linkbuilding', 'translations'];
            automationTypes.forEach((type, index) => {
                setTimeout(() => {
                    const card = document.getElementById(`automation-${type}`);
                    if (card) {
                        const checkbox = card.querySelector('input[type="checkbox"]');
                        if (checkbox) {
                            checkbox.checked = true;
                            toggleAutomation(type);
                        }
                    }
                }, 500 + (index * 200));
            });
        }
    });
}, automationObserverOptions);

// Observe the automation section and setup billing modal
document.addEventListener('DOMContentLoaded', function() {
    const automationSection = document.querySelector('.ac-automation-section');
    if (automationSection) {
        automationObserver.observe(automationSection);
        console.log('Observing automation section:', automationSection);
    }
    
});

// Brain to Dashboard Task Flow Animation
function initBrainDashboardAnimation() {
    const dashboard = document.querySelector('.ac-brain-dashboard');
    const taskIcon = document.querySelector('.ac-task-icon');
    const notificationsList = document.querySelector('.ac-notifications-list');
    if (!dashboard || !taskIcon || !notificationsList) return;
    
    // Define agent types that correspond to notifications
    const agents = [
        { 
            icon: 'fas fa-brain', 
            type: 'product',
            title: 'Product SEO Engine',
            messages: [
                'Optimized 24 product titles for search rankings',
                'Enhanced meta descriptions for 18 products',
                'Generated SEO-friendly URLs for 32 products',
                'Updated alt tags for improved accessibility'
            ]
        },
        { 
            icon: 'fas fa-network-wired', 
            type: 'collections',
            title: 'Smart Collections',
            messages: [
                'Created "Summer Essentials" smart collection',
                'Built "Trending Products" automated rules',
                'Updated "Best Sellers" collection criteria',
                'Generated "Winter Must-Haves" collection'
            ]
        },
        { 
            icon: 'fas fa-microscope', 
            type: 'search',
            title: 'Search Insights',
            messages: [
                'Tracked 150+ keywords, found 12 opportunities',
                'Identified 8 high-potential ranking gaps',
                'Monitored competitor movements on 25 terms',
                'Updated Search Console performance data'
            ]
        },
        { 
            icon: 'fas fa-feather-alt', 
            type: 'content',
            title: 'Content Machine',
            messages: [
                'Published weekly blog post on trending topics',
                'Generated buyer guides for top collections',
                'Created category landing page content',
                'Built internal link strategy for authority'
            ]
        },
        { 
            icon: 'fas fa-cog', 
            type: 'technical',
            title: 'Technical Optimizer',
            messages: [
                'Fixed 12 broken internal links automatically',
                'Optimized page load speed for mobile devices',
                'Implemented schema markup for 45 products',
                'Updated XML sitemap with new collections'
            ]
        },
        { 
            icon: 'fas fa-language', 
            type: 'translator',
            title: 'Universal Translator',
            messages: [
                'Translated 50 products to Spanish market',
                'Created French descriptions for new arrivals',
                'Generated German meta tags for expansion',
                'Localized collection pages for Italy'
            ]
        },
        { 
            icon: 'fas fa-link', 
            type: 'links',
            title: 'Link Optimizer',
            messages: [
                'Built 18 high-quality internal links',
                'Created breadcrumb navigation structure',
                'Fixed 7 broken external references',
                'Generated link equity distribution map'
            ]
        },
        { 
            icon: 'fas fa-robot', 
            type: 'intelligence',
            title: 'Competitor Intel',
            messages: [
                'Analyzed top 3 competitors\' new strategies',
                'Found 15 keyword gaps to exploit',
                'Discovered trending product opportunities',
                'Tracked market shifts in real-time'
            ]
        }
    ];
    
    let currentAgentIndex = 0;
    let messageHistory = [];
    const maxNotifications = 5;
    
    function getRelativeTime() {
        const times = ['Just now', '30s ago', '1m ago', '2m ago', '5m ago'];
        return times[Math.floor(Math.random() * times.length)];
    }
    
    function createNewNotification(agent) {
        // Select a random message from this agent's message pool
        const message = agent.messages[Math.floor(Math.random() * agent.messages.length)];
        
        // Check if this exact message was recently used
        if (messageHistory.includes(message) && messageHistory.length < agent.messages.length * 2) {
            // Try to find an unused message
            for (let i = 0; i < agent.messages.length; i++) {
                const altMessage = agent.messages[i];
                if (!messageHistory.includes(altMessage)) {
                    messageHistory.push(altMessage);
                    createNotificationElement(agent, altMessage);
                    return;
                }
            }
        }
        
        // Use the message anyway if all have been used
        messageHistory.push(message);
        if (messageHistory.length > agents.length * 3) {
            messageHistory = messageHistory.slice(-agents.length * 2);
        }
        
        createNotificationElement(agent, message);
    }
    
    function createNotificationElement(agent, message) {
        const notification = document.createElement('div');
        notification.className = 'ac-notification';
        notification.style.animationDelay = '0s';
        
        notification.innerHTML = `
            <div class="ac-notification-icon">
                <i class="${agent.icon}"></i>
            </div>
            <div class="ac-notification-content">
                <div class="ac-notification-title">${agent.title}</div>
                <div class="ac-notification-message">${message}</div>
            </div>
            <div class="ac-notification-time">${getRelativeTime()}</div>
        `;
        
        // Insert at the beginning
        notificationsList.insertBefore(notification, notificationsList.firstChild);
        
        // Limit number of notifications
        while (notificationsList.children.length > maxNotifications) {
            const lastNotification = notificationsList.lastChild;
            lastNotification.style.animation = 'notificationSlideDown 0.3s ease-out reverse';
            setTimeout(() => lastNotification.remove(), 300);
        }
        
        // Update time periodically for all notifications
        updateNotificationTimes();
    }
    
    function updateNotificationTimes() {
        const times = ['Just now', '30s ago', '1m ago', '2m ago', '5m ago', '10m ago'];
        notificationsList.querySelectorAll('.ac-notification').forEach((notif, index) => {
            const timeEl = notif.querySelector('.ac-notification-time');
            if (timeEl && index < times.length) {
                timeEl.textContent = times[index];
            }
        });
    }
    
    function sendTaskIcon() {
        const currentAgent = agents[currentAgentIndex];
        const scheduledIndicator = document.querySelector('.ac-task-scheduled-indicator');
        
        // Update the traveling icon
        taskIcon.innerHTML = `<i class="${currentAgent.icon}"></i>`;
        taskIcon.setAttribute('data-agent', currentAgent.type);
        
        // Reset and start animation
        taskIcon.classList.remove('traveling');
        taskIcon.offsetHeight; // Force reflow
        taskIcon.classList.add('traveling');
        
        // When icon reaches dashboard (at 85% of 1.5s = 1.275s)
        setTimeout(() => {
            // Add visual feedback - pink border for receiving
            dashboard.classList.add('task-received');
            
            // Show "TASK SCHEDULED" indicator
            if (scheduledIndicator) {
                scheduledIndicator.classList.add('show');
            }
            
            // Create transformation effect
            taskIcon.style.transform = 'scale(1.5)';
            taskIcon.style.opacity = '0';
            
            // After brief moment, show task scheduled confirmation
            setTimeout(() => {
                dashboard.classList.remove('task-received');
                dashboard.classList.add('task-scheduled');
                
                // Create new notification at the top
                createNewNotification(currentAgent);
                
                // Hide the scheduled indicator after showing it
                setTimeout(() => {
                    if (scheduledIndicator) {
                        scheduledIndicator.classList.remove('show');
                    }
                    dashboard.classList.remove('task-scheduled');
                }, 1500);
                
                // Reset task icon
                taskIcon.style.transform = '';
                taskIcon.style.opacity = '';
            }, 400);
        }, 1275);
        
        // Move to next agent
        currentAgentIndex = (currentAgentIndex + 1) % agents.length;
    }
    
    // Start first icon immediately
    sendTaskIcon();
    
    // Send new icon every 2.5 seconds (faster cycle)
    setInterval(sendTaskIcon, 2500);
}

// Initialize brain dashboard interaction
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initBrainDashboardAnimation);
} else {
    initBrainDashboardAnimation();
}


</script>



    
</body>

<!-- Mirrored from seogrove.ai/pre-registration by HTTrack Website Copier/3.x [XR&CO'2014], Wed, 09 Jul 2025 16:39:39 GMT -->
</html>