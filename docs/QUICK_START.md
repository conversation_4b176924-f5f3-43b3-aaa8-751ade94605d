# 🚀 Quick Start Guide - Google Ads AI Platform

This guide helps you get the Google Ads AI Platform up and running quickly using the recommended manual setup.

## Manual Setup & Run

### Step 1: Backend Setup
```bash
# Navigate to the backend directory from project root
cd backend

# Create Python virtual environment (if it doesn't exist)
python -m venv venv

# Activate the virtual environment
# On Windows (Git Bash or CMD with venv\Scripts in PATH):
source venv/Scripts/activate 
# On macOS/Linux:
# source venv/bin/activate

# Install backend dependencies
pip install -r requirements.txt
```

### Step 2: Frontend Setup
```bash
# Navigate to the frontend directory from project root
cd frontend

# Install frontend dependencies (if node_modules doesn't exist)
npm install
```

### Step 3: Run Servers

**Terminal 1 - Start Backend Server:**
```bash
# Ensure you are in the 'backend' directory
# Ensure virtual environment (venv) is activated
python run_server.py
```
The backend will be available at `http://127.0.0.1:8000`.

**Terminal 2 - Start Frontend Server:**
```bash
# Ensure you are in the 'frontend' directory
npm run dev
```
The frontend will be available at `http://localhost:5173`.

Alternatively, after initial setup, you can use the simplified run scripts from the project root:
*   `./run-app.sh` (for Linux/macOS)
*   `run-app.bat` (for Windows CMD)

## 🌐 Access Points

- **Frontend**: http://localhost:5173
- **Backend API**: http://127.0.0.1:8000
- **API Docs**: http://127.0.0.1:8000/docs

## 🧪 Testing OAuth Flow

1. **Open Frontend**: http://localhost:5173
2. **Sign Up/Login**: Create a new Supabase account or login.
3. **Navigate**: Click on "Google Ads Setup" in the dashboard (or equivalent page for connecting Google Ads).
4. **Connect**: Click "Generate Authorization URL" (or similar button).
5. **Authorize**: You'll be redirected to Google. Log in with the test Google Ads account: `<EMAIL>`.
6. **Grant Permissions**: Allow the requested permissions.
7. **Success**: You should be redirected back to the application, and it should indicate a successful connection.

## 📋 Test Credentials

### Google Ads Test Account
- **Email**: <EMAIL>
- **Manager ID**: ************ (This is the `GOOGLE_ADS_LOGIN_CUSTOMER_ID`)
- **Test Clients**: 3 client accounts are linked under this manager.

### Developer Token
(Configured in `backend/.env`)
```
USJoZ_CN_pYY2MP-jlhjqA
```

## 🔍 Verify Setup

You can run the setup verification script from the project root:
```bash
python test-setup.py
```
(Note: `test-setup.py` may need its internal API test endpoint updated to `/health` if `/api/auth/test` was removed.)

## ⚠️ Common Issues

### "Module not found" (Python Backend)
Ensure you are in the `backend` directory, the virtual environment (`venv`) is activated, and you've run `pip install -r requirements.txt`.

### "Port already in use"
- Backend uses port 8000.
- Frontend uses port 5173.
- Make sure no other applications are using these ports. You can use tools like `netstat` or `lsof` to find and kill processes using these ports if necessary.

### "Invalid authentication" (Supabase Login)
- Make sure you're logged into Supabase via the application's login page.
- Ensure the backend server is running.
- Verify Supabase URL and Anon Key in `frontend/.env` and `backend/.env` are correct.

### "OAuth error" (Google Ads Connection)
- Ensure you are using the correct Google test account email (`<EMAIL>`).
- Verify that this email is listed as a test user in your Google Cloud Console OAuth consent screen settings.
- Double-check that the redirect URI configured in Google Cloud Console matches `http://localhost:8000/api/google-ads/auth/callback`.
- Ensure `backend/credentials.json` and `backend/.env` have the correct Google Ads Client ID and Secret.

## 📞 Need Help?

1. Check the detailed `OAUTH_TESTING_GUIDE.md` (located in the `docs/testing/` folder).
2. Review `GOOGLE_ADS_ACCOUNT_STRUCTURE.md` (in `PRD Files/`) for account hierarchy.
3. Examine backend (Python/Uvicorn) and frontend (Vite/browser console) logs for specific error messages.
4. Ensure all dependencies are correctly installed in both backend and frontend environments.
