# 🚀 Google Ads AI Platform - Ready to Test!

## ✅ Configuration Status

### Google Ads API
- **Developer Token**: `USJoZ_CN_pYY2MP-jlhjqA` ✅ ACTIVE
- **Test Manager**: `605-234-4141` ✅ CONFIGURED
- **OAuth Client**: Fresh client created ✅ NEW
- **Authentication**: Proper Supabase auth ✅ NO DEBUG MODE

### Test Accounts Available
```
Manager: 605-234-4141 (<EMAIL>)
├── Client 1: 690-798-5586
├── Client 2: 406-000-1758
└── Dental: 788-496-2297
```

## 🧪 How to Test

### 1. Install Dependencies
```bash
# Backend
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt

# Frontend
cd ../frontend
npm install
```

### 2. Start the Application

**Option A - Use the scripts (Recommended for quick start after initial setup):**
```bash
# Windows
./run-app.bat

# Linux/Mac
chmod +x run-app.sh
./run-app.sh
```

**Option B - Manual (As per APP_STARTUP_GUIDE.md):**
```bash
# Terminal 1 - Backend
cd backend
source venv/bin/activate # Or venv\Scripts\activate on Windows
python run_server.py

# Terminal 2 - Frontend
cd frontend
npm run dev
```

### 3. Test OAuth Flow

1. **Open Browser**: http://localhost:5173
2. **Sign Up/Login**: Create a Supabase account or login
3. **Navigate**: Go to Google Ads Setup
4. **Connect**: Click "Generate Authorization URL"
5. **Authorize**: <NAME_EMAIL>
6. **Success**: You should be redirected back

## 📋 What to Verify

### Backend API
- [ ] http://127.0.0.1:8000/docs - API docs load
- [ ] http://127.0.0.1:8000/health - Returns success/status
- [ ] Authentication works (no debug bypass)

### Frontend
- [ ] Login/Signup with Supabase works (Note: profile fetch from `users` table may timeout, app should fallback)
- [ ] Dashboard loads after login
- [ ] Google Ads Setup page accessible
- [ ] OAuth URL generation works

### OAuth Flow
- [ ] Authorization URL opens Google
- [ ] Can login with test account
- [ ] Redirects back to app
- [ ] Token exchange works

## ⚠️ Important Notes

1. **Supabase Auth Required**: You must create an account or login
2. **Test Accounts Only**: Use <EMAIL> for Google Ads
3. **OAuth Consent**: Only test users can access (Testing mode)
4. **Zero Metrics**: Test accounts show 0 for all data (normal)

## 🐛 Troubleshooting

### "Invalid authentication"
- Make sure you're logged into Supabase first
- Check if backend is running
- Verify Supabase credentials in .env

### "OAuth error"
- Use correct test account email
- Check redirect URI matches exactly
- Ensure you're on the consent screen test users list

### "Connection refused"
- Backend not started or still loading
- Check if port 8000 is free
- Look at backend terminal for errors

## 🎯 Next Steps After Testing

1. **Basic Access**: Apply when ready for production
2. **Real Accounts**: Switch to production manager
3. **AI Integration**: Add OpenRouter API keys
4. **Deploy**: Set up production environment
