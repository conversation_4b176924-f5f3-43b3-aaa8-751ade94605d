# Google OAuth Testing Guide

## ✅ What We've Updated

### 1. **New OAuth Client Created**
- **Client ID**: `*************-qj2cokqle91qjnb9ulba5n5fqild9m8f.apps.googleusercontent.com`
- **Client Secret**: `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`
- **Authorized Redirect URIs**:
  - `http://localhost:8000/api/google-ads/auth/callback`
  - `http://localhost:5173/auth/callback`
  - `http://127.0.0.1:8000/api/google-ads/auth/callback`

### 2. **Backend Configuration Updated**
- Created `/backend/credentials.json` with new OAuth client config
- Updated `/backend/.env` with new credentials
- Updated `/backend/app/core/config.py` with new OAuth settings
- Fixed redirect URI to use `GOOGLE_OAUTH_REDIRECT_URI`
- Added Supabase credentials for authentication

### 3. **Frontend Configuration Updated**
- Updated `/frontend/src/lib/api.ts` to use authenticated endpoint
- Removed temporary no-auth endpoints

### 4. **Removed Test Endpoints**
- Cleaned up all temporary test endpoints from `/backend/app/api/google_ads.py`

## 🧪 Testing Steps

### Step 1: Install Dependencies
```bash
cd backend
pip install -r requirements.txt
```

### Step 2: Start Backend Server
```bash
cd backend
python run_server.py
# OR
uvicorn app.main:app --reload --host 127.0.0.1 --port 8000
```

### Step 3: Start Frontend Server
```bash
cd frontend
npm install
npm run dev
```

### Step 4: Test OAuth Flow

1. **Open Browser**: Go to `http://localhost:5173`
2. **Login**: Use your Supabase credentials
3. **Navigate**: Go to Google Ads Setup page
4. **Generate Auth URL**: Click "Generate Authorization URL"
5. **Authorize**: Click "Open Authorization Page" and authorize with Google
6. **Verify**: Check if you're redirected back successfully

## 🔍 What to Check

### 1. **OAuth URL Generation**
- The auth URL should look like:
  ```
  https://accounts.google.com/o/oauth2/auth?
    client_id=*************-qj2cokqle91qjnb9ulba5n5fqild9m8f.apps.googleusercontent.com&
    redirect_uri=http://localhost:8000/api/google-ads/auth/callback&
    scope=https://www.googleapis.com/auth/adwords&
    access_type=offline&
    prompt=consent&
    state=<random_state>
  ```

### 2. **Console Logs**
Check browser console for:
- No 401 errors
- Successful auth URL generation
- Proper redirect handling

### 3. **Backend Logs**
Check backend terminal for:
- Successful OAuth URL generation
- State parameter creation
- Token exchange logs

## 🚨 Common Issues & Solutions

### Issue 1: 401 Unauthorized
**Solution**: Ensure you're logged in with Supabase auth first

### Issue 2: Redirect URI Mismatch
**Solution**: Verify the redirect URI in Google Cloud Console matches exactly:
`http://localhost:8000/api/google-ads/auth/callback`

### Issue 3: Invalid Client
**Solution**: Ensure the credentials.json file is in the backend directory

### Issue 4: CORS Error
**Solution**: Backend should allow CORS from `http://localhost:5173`

## 📋 Next Steps After Testing

1. **If OAuth Works**:
   - Test with your test Google Ads account
   - Verify accounts are listed
   - Test search term retrieval

2. **If OAuth Fails**:
   - Check error messages in console
   - Verify all credentials are correct
   - Ensure OAuth consent screen is published

3. **Production Readiness**:
   - Apply for Google Ads API Basic Access
   - Implement refresh token storage in Supabase
   - Add proper error handling
   - Implement token refresh logic

## 🔐 Security Notes

- Never commit `.env` file to git
- Store refresh tokens encrypted in database
- Use environment-specific OAuth clients
- Implement proper state parameter validation
- Add rate limiting for OAuth endpoints

## 📚 API Endpoints

### Authentication Required
- `GET /api/google-ads/auth/url` - Generate OAuth URL
- `GET /api/google-ads/auth/callback` - OAuth callback
- `POST /api/google-ads/auth/tokens` - Exchange code for tokens
- `GET /api/google-ads/accounts` - List Google Ads accounts
- `GET /api/google-ads/test-connection` - Test connection

### Response Format
```json
{
  "auth_url": "https://accounts.google.com/o/oauth2/auth?...",
  "state": "random_state_token",
  "message": "Redirect user to this URL to authorize Google Ads access"
}
