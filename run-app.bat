@echo off
echo Starting Google Ads AI Optimization Platform...
echo ================================================

:: Start backend server
echo.
echo Starting Backend Server...
start "Backend Server" cmd /k "cd backend && call venv\Scripts\activate.bat && python run_server.py"

:: Wait a bit for backend to start
timeout /t 5 /nobreak >nul

:: Start frontend server
echo.
echo Starting Frontend Server...
start "Frontend Server" cmd /k "cd frontend && npm run dev"

echo.
echo ================================================
echo Application is starting!
echo ================================================
echo Frontend: http://localhost:5173
echo Backend API: http://127.0.0.1:8000
echo API Documentation: http://127.0.0.1:8000/docs
echo ================================================
echo.
echo Test Accounts Available:
echo   - Test Manager: 605-234-4141 (<EMAIL>)
echo   - Test Client 1: 690-798-5586
echo   - Test Client 2: 406-000-1758
echo   - Test Dental: 788-496-2297
echo.
echo Note: Using Test Account Access (Basic Access needed for production)
echo.
echo Close the command windows to stop the servers
pause
