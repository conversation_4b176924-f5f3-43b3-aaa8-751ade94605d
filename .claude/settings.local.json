{"permissions": {"allow": ["Bash(find:*)", "Bash(grep:*)", "<PERSON><PERSON>(python:*)", "Bash(ls:*)", "Bash(./venv/Scripts/python.exe run_server.py:*)", "Bash(npm run dev:*)", "Bash(npm install)", "Bash(npm run build:*)", "Bash(git checkout:*)", "<PERSON>sh(claude mcp-servers)", "<PERSON><PERSON>(claude --mcp-debug)", "<PERSON><PERSON>(cat:*)", "Bash(rm:*)", "mcp__supabase__list_projects", "mcp__supabase__get_project", "mcp__supabase__list_tables", "<PERSON><PERSON>(source:*)", "Bash(npm run:*)", "Bash(./venv/Scripts/python.exe ../test_supabase.py)", "Bash(./venv/Scripts/python.exe ../test_google_ads.py)", "Bash(./venv/Scripts/python.exe -m pytest -v)", "<PERSON><PERSON>(chmod:*)", "Bash(./run-dev.sh:*)", "Bash(ss:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(wget:*)", "Bash(./venv/Scripts/python.exe ../test_oauth.py)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "Bash(kill:*)", "Bash(./venv/Scripts/pip.exe list)", "WebFetch(domain:developers.google.com)", "Bash(./start-backend.sh:*)", "Bash(pip3 install:*)", "Bash(npm ls:*)", "Bash(npm uninstall:*)", "Bash(npm install:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(./venv/Scripts/python -m pip install:*)", "Bash(./venv/Scripts/python.exe -m pip install:*)", "Bash(pip install:*)", "Bash(venv/Scripts/pip.exe install:*)", "<PERSON><PERSON>(pip show:*)", "mcp__supabase__execute_sql", "mcp__supabase__apply_migration", "Bash(git remote set-url:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git push:*)", "Bash(npx tailwindcss init:*)", "Bash(cp:*)", "WebFetch(domain:tailwindcss.com)", "WebFetch(domain:react.dev)", "WebFetch(domain:vitejs.dev)", "WebFetch(domain:reactrouter.com)", "WebFetch(domain:markaicode.com)", "WebFetch(domain:www.robinwieruch.de)", "WebFetch(domain:codeparrot.ai)", "WebFetch(domain:github.com)", "WebFetch(domain:ursualexandr-21492.medium.com)", "Bash(for file in src/pages/*.jsx src/pages/ai/*.jsx)", "Bash(do echo \"=== $file ===\")", "<PERSON><PERSON>(tail:*)", "Bash(done)", "Bash(node:*)"]}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["supabase", "puppeteer"]}