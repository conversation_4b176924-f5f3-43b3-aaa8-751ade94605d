# 🚀 Start Application Guide - Google Ads AI Platform

## ✅ Pre-Verification Checklist

### 1. Supabase User Setup
- ✅ **User Created**: <EMAIL>
- ✅ **Project**: irftzijnouubcjkyeuxj.supabase.co
- ✅ **Database**: 57 tables deployed and ready

### 2. Google Cloud OAuth Setup
- ✅ **Client ID**: *************-qj2cokqle91qjnb9ulba5n5fqild9m8f
- ✅ **Test Users**: <EMAIL>, <EMAIL>
- ✅ **Redirect URIs**: Backend callback configured

### 3. Google Ads API Setup
- ✅ **Developer Token**: USJoZ_CN_pYY2MP-jlhjqA (Active)
- ✅ **Test Manager**: 605-234-4141
- ✅ **Test Accounts**: 3 client accounts ready

## 🚀 Option 1: Automated Startup (Recommended)

### Windows Users:
```cmd
# Navigate to project root
cd "J:\Brand Wisdom\Web App\SAAS\Google Ads AI Optimization"

# Run the automated script
install-and-run.bat
```

This will:
1. ✅ Create/activate Python virtual environment
2. ✅ Install all backend dependencies
3. ✅ Install all frontend dependencies  
4. ✅ Start backend server (http://127.0.0.1:8000)
5. ✅ Start frontend server (http://localhost:5173)

## 🔧 Option 2: Manual Startup

### Step 1: Start Backend
```cmd
# Navigate to backend
cd "J:\Brand Wisdom\Web App\SAAS\Google Ads AI Optimization\backend"

# Activate virtual environment
venv\Scripts\activate.bat

# Install dependencies (if needed)
pip install -r requirements.txt

# Start server
python run_server.py
```

### Step 2: Start Frontend (New Terminal)
```cmd
# Navigate to frontend
cd "J:\Brand Wisdom\Web App\SAAS\Google Ads AI Optimization\frontend"

# Install dependencies (if needed)
npm install

# Start development server
npm run dev
```

## 🧪 Testing the OAuth Flow

### Step 1: Access the Application
1. **Open Browser**: Go to http://localhost:5173
2. **Check Backend**: Verify http://127.0.0.1:8000 shows API message
3. **API Docs**: Check http://127.0.0.1:8000/docs for Swagger UI

### Step 2: Supabase Authentication
1. **Sign Up/Login**: Use <EMAIL>
2. **Password**: Use your Supabase password
3. **Verify**: Should see dashboard after login

### Step 3: Google Ads OAuth Testing
1. **Navigate**: Go to "Google Ads Setup" page
2. **Generate URL**: Click "Generate Authorization URL"
3. **Authorize**: <NAME_EMAIL>
4. **Grant Access**: Allow Google Ads permissions
5. **Callback**: Should redirect back to dashboard

### Step 4: Test API Calls
1. **List Accounts**: Should show test manager account
2. **Select Account**: Choose one of the 3 test clients
3. **Fetch Data**: Test campaigns/search terms (will be empty for test accounts)

## 🔍 Troubleshooting

### Backend Issues
- **Port 8000 in use**: Kill existing processes or change port
- **Import errors**: Ensure virtual environment is activated
- **Missing dependencies**: Run `pip install -r requirements.txt`

### Frontend Issues  
- **Port 5173 in use**: Vite will auto-assign new port
- **Build errors**: Run `npm install` to update dependencies
- **CORS errors**: Ensure backend is running on 127.0.0.1:8000

### OAuth Issues
- **Invalid client**: Verify credentials.json matches Google Cloud
- **Redirect mismatch**: Check redirect URI in Google Cloud Console
- **Test user access**: Ensure <EMAIL> is authorized

### Supabase Issues
- **Auth timeout**: Check network connection to Supabase
- **User not found**: Verify user exists in Supabase dashboard
- **Database errors**: Check Supabase project status

## 📋 Expected Results

### Successful Backend Startup
```
Starting Google Ads AI Platform Backend...
Backend URL: http://localhost:8000
API Docs: http://localhost:8000/docs
Auto-reload enabled for development
--------------------------------------------------
INFO:     Uvicorn running on http://127.0.0.1:8000
INFO:     Started reloader process
INFO:     Started server process
INFO:     Waiting for application startup.
INFO:     Application startup complete.
```

### Successful Frontend Startup
```
  VITE v6.3.5  ready in 1234 ms

  ➜  Local:   http://localhost:5173/
  ➜  Network: use --host to expose
  ➜  press h + enter to show help
```

### Successful OAuth Flow
1. **Authorization URL Generated**: Long Google OAuth URL
2. **Google Login**: Redirects to Google login page
3. **Permission Grant**: Shows Google Ads permissions screen
4. **Callback Success**: Redirects to dashboard with success message
5. **API Access**: Can fetch Google Ads account data

## 🎯 Next Steps After Successful Testing

1. **Test All Features**: Try different API endpoints
2. **Error Handling**: Test with invalid data
3. **Production Setup**: Apply for Google Ads Basic Access
4. **AI Integration**: Configure OpenAI/Anthropic API keys
5. **Deployment**: Prepare for production deployment

## 📞 Support

If you encounter issues:
1. Check browser console for errors
2. Check backend terminal for error messages
3. Verify all environment variables are set
4. Ensure all services (Supabase, Google Cloud) are active
5. Review the CONFIGURATION_VERIFICATION.md file

The application is fully configured and ready for testing with your verified Google Cloud OAuth setup and Supabase user account!
