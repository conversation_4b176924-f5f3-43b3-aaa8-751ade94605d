# Product Requirements Document - AdsAI Platform
**Brand Wisdom Solutions - Google Ads AI Campaign Management Platform**  
**Version**: 3.0 Consolidated  
**Last Updated**: January 2025  
**Status**: API Approved - Ready for Implementation

---

## Executive Summary

AdsAI is an internal agency platform designed for Brand Wisdom Solutions to manage 20+ client Google Ads accounts through their Manager Customer Center (MCC: 310-946-3592). The platform combines traditional campaign management capabilities with AI-powered optimization features in a 50/50 split, enabling the agency to scale operations while improving client performance.

### Key Business Metrics
- **Agency Scale**: 20+ client accounts managed through single MCC
- **Client Types**: E-commerce, B2B, and local businesses
- **Monthly Spend**: ₹4.04 Crores across all clients
- **Team Size**: 5-10 campaign specialists + team leads

### Platform Value Propositions
- **40% reduction** in wasted ad spend through intelligent optimization
- **50% improvement** in Quality Scores via AI-powered relevance
- **90% reduction** in manual optimization time
- **3x faster** ad copy testing and optimization cycles
- **6+ hours saved** per week per campaign manager

---

## Product Overview

### Platform Philosophy: 50/50 Approach

**50% Core Campaign Management** (Daily Operations)
- Multi-client dashboard with unified view
- Campaign creation across all Google Ads types
- Budget monitoring and bid management
- Performance tracking and reporting
- Team collaboration tools
- Client report generation

**50% AI-Powered Assistance** (Intelligent Optimization)
- Search query mining and analysis
- Intent classification for targeting
- Ad copy generation with psychological triggers
- Negative keyword automation
- Bid intelligence and optimization
- Natural language insights

### Supported Campaign Types
- Search Network
- Display Network
- Shopping
- Performance Max
- Local Services
- Call-only
- Demand Gen
- Display Expansion on Search

---

## Core Features (12 Total)

### Programmatic Campaign Management

#### 1. Multi-Client Dashboard 📊
- **Purpose**: Centralized view of all client accounts
- **Features**:
  - Account switching with dropdown
  - Aggregate performance metrics
  - Client health indicators
  - Quick actions per account
  - Customizable views

#### 2. Campaign Creation & Management 🚀
- **Purpose**: Streamlined campaign setup and management
- **Features**:
  - Campaign creation wizard for all types
  - Bulk operations across accounts
  - Template library for quick setup
  - Campaign cloning and testing
  - Settings optimization

#### 3. Bid & Budget Management 💰
- **Purpose**: Optimize spend across all clients
- **Features**:
  - Real-time budget monitoring
  - Automated bid adjustments
  - Budget pacing alerts
  - ROI-based bid strategies
  - Cross-client budget allocation

#### 4. Performance Reporting 📈
- **Purpose**: Client-ready insights and reports
- **Features**:
  - Automated PDF generation
  - Custom report templates
  - White-label branding
  - Scheduled delivery
  - Interactive dashboards

#### 5. Monitoring & Alerts System 🔔
- **Purpose**: Proactive issue detection
- **Features**:
  - Real-time performance alerts
  - Budget exhaustion warnings
  - Approval issue notifications
  - Campaign health monitoring
  - Custom alert rules

#### 6. Keyword & Audience Management 🎯
- **Purpose**: Comprehensive targeting control
- **Features**:
  - Keyword research tools
  - Audience builder
  - Negative keyword lists
  - Search term reports
  - Audience insights

### AI-Powered Optimization Features

#### 7. Search Query Mining Engine 🔍
- **Purpose**: Extract value from search term data
- **AI Capabilities**:
  - Profitable query identification
  - Waste pattern detection
  - Conversion prediction
  - Keyword expansion suggestions
  - Automated categorization

#### 8. Search Intent Classifier 🎯
- **Purpose**: Understand user intent for better targeting
- **AI Capabilities**:
  - NLP-based intent analysis
  - 4 intent categories (Transactional, Informational, etc.)
  - Confidence scoring
  - Historical correlation
  - Bid recommendations by intent

#### 9. Ad Copy Laboratory ✍️
- **Purpose**: AI-powered ad creation and testing
- **AI Capabilities**:
  - Generate RSA variations
  - Psychological trigger integration
  - A/B test recommendations
  - Performance prediction
  - Brand voice consistency

#### 10. Negative Keyword AI 🚫
- **Purpose**: Automated waste prevention
- **AI Capabilities**:
  - Pattern recognition
  - Industry-specific suggestions
  - Conflict detection
  - Impact estimation
  - Confidence-based automation

#### 11. Bid Intelligence Engine 💡
- **Purpose**: Smart bidding optimization
- **AI Capabilities**:
  - Hourly bid adjustments
  - Conversion probability modeling
  - Competitive analysis
  - Budget allocation optimization
  - Performance forecasting

#### 12. Natural Language Insights 🧠
- **Purpose**: Explain complex data in simple terms
- **AI Capabilities**:
  - Performance change explanations
  - Trend analysis narratives
  - Opportunity identification
  - Client-ready summaries
  - Q&A interface

---

## User Personas

### 1. Campaign Manager (Emma)
- **Role**: Manages 15-20 client accounts daily
- **Needs**: Quick optimization identification, bulk actions, time-saving tools
- **Pain Points**: Manual search term reviews, repetitive tasks
- **Success Metrics**: Accounts managed, time saved, performance improvements

### 2. PPC Team Lead (Marcus)
- **Role**: Oversees team of 5 specialists
- **Needs**: Team performance visibility, standardized processes
- **Pain Points**: Inconsistent optimization approaches
- **Success Metrics**: Team efficiency, client satisfaction

### 3. Agency Director (Sarah)
- **Role**: Strategic oversight and client relationships
- **Needs**: High-level insights, client communication tools
- **Pain Points**: Explaining complex optimizations to clients
- **Success Metrics**: Client retention, revenue growth

---

## Technical Requirements

### Google Ads API Services (Approved)

**Core Services**:
- GoogleAdsService - Account and campaign data
- SearchTermViewService - Search query analysis
- KeywordPlanService - Keyword research
- AdService - Ad creation and management
- CampaignService - Campaign operations
- AdGroupService - Ad group management
- BiddingStrategyService - Bid optimization
- ExtensionFeedItemService - Ad extensions

**Management Services**:
- SharedSetService - Negative keyword lists
- BatchJobService - Bulk operations
- ReportingService - Custom reports

**Rate Limits**:
- 15,000 operations per day
- 15,000 get requests per day
- 5,000 mutate operations per request

### Data Requirements

**Sync Frequency**:
- Real-time: Account status, budgets
- 15 minutes: Performance metrics
- Hourly: Search terms, conversions
- Daily: Full account sync

**Data Retention**:
- Raw data: 90 days
- Aggregated data: 2 years
- Client reports: Indefinite

---

## Success Metrics

### Efficiency Metrics
- Time saved: 6+ hours/week per user
- Accounts per manager: 2x increase
- Optimization velocity: 10x faster

### Performance Metrics
- Wasted spend reduction: 40%
- Quality Score improvement: +2 points average
- CTR increase: 25%
- CPA reduction: 30%

### Business Metrics
- Client retention: 95%+
- Revenue per account: +35%
- Platform ROI: 10:1

---

## Development Phases

### Phase 1: Foundation (Months 1-2)
- Google Ads API integration
- Multi-client dashboard
- Basic campaign management
- User authentication
- Search query analysis (basic)

### Phase 2: Intelligence (Months 3-4)
- AI integration setup
- Intent classifier
- Ad copy generator
- Negative keyword AI
- Quality Score tracking

### Phase 3: Automation (Months 5-6)
- Automated workflows
- Scripts library
- Advanced bidding
- Natural language insights
- Bulk operations

### Phase 4: Scale (Months 7+)
- White-label options
- Advanced reporting
- Custom integrations
- Team features
- Enterprise capabilities

---

## Constraints & Considerations

### Technical Constraints
- Google Ads API rate limits
- Supabase plan limitations
- AI model token costs
- Processing time for large accounts

### Business Constraints
- Internal tool only (no external access)
- Agency workflow compatibility
- Client data security
- Team training requirements

### Compliance Requirements
- Google Ads API Terms of Service
- Data privacy regulations
- Client confidentiality
- Financial data security

---

## Appendix

### Glossary
- **MCC**: Manager Customer Center - Google's multi-account management system
- **RSA**: Responsive Search Ads
- **QS**: Quality Score
- **ROAS**: Return on Ad Spend
- **CPA**: Cost Per Acquisition

### References
- Google Ads API Documentation
- Brand Wisdom Solutions Website: brandwisdom.in
- Contact: <EMAIL>