# Google Ads API Application Mockups
**Brand Wisdom Solutions - Google Ads AI Campaign Management Platform**

## Overview
These HTML/CSS mockups demonstrate the user interface of our Google Ads AI Campaign Management Platform, created specifically for the Google Ads API Basic Access application (Question 7).

## Mockups Included

### 1. Dashboard Overview (`01-dashboard-overview.html`)
**Purpose**: Main agency dashboard showing aggregate view of all client accounts

**Key Features Demonstrated**:
- **Multi-Client Management**: Client selector dropdown showing 23 active clients
- **Key Performance Metrics**: Active clients, total campaigns, conversions, cost savings
- **Performance Trends**: Visual chart showing campaign performance over time
- **AI Recommendations**: Real-time AI-powered optimization suggestions with confidence scores
- **Recent Activity Feed**: Live updates on campaign changes and optimizations
- **Brand Wisdom Styling**: Professional agency interface with company branding

**Data Shown**:
- 23 Active Clients
- 187 Total Campaigns (94% active)
- 1,247 Monthly Conversions (+23% improvement)
- $12,340 Cost Savings from AI optimizations

### 2. Client Account View (`02-client-account-view.html`)
**Purpose**: Detailed view of individual client account (TechStart Solutions - B2B SaaS)

**Key Features Demonstrated**:
- **Client-Specific Metrics**: Monthly spend, conversions, CPA, ROAS for individual client
- **Campaign Type Diversity**: Search, Display, Shopping, Performance Max campaigns
- **Campaign Management Actions**: Optimize, Analyze, Review buttons for each campaign type
- **AI Insights**: Client-specific AI recommendations and performance insights
- **Professional Reporting**: Generate client reports and schedule optimizations

**Client Profile Shown**:
- **Company**: TechStart Solutions (B2B SaaS)
- **Account ID**: 123-456-7890
- **Monthly Budget**: $15,000
- **Monthly Spend**: $14,230 (95% utilization)
- **Conversions**: 247 (+18% vs last month)
- **Average CPA**: $57.65 (-12% improvement)
- **ROAS**: 4.2x (above 3.5x target)

## Design System

### Brand Alignment
Both mockups use the exact styling system from our actual application:

**Colors**:
- **Primary Brand**: `#3E5CE7` (Brand Wisdom blue)
- **Success**: `#27C084` (Brand green)
- **Error**: `#EF5E5E` (Brand red)
- **Text**: Slate color palette (slate-600, slate-700, slate-900)
- **Backgrounds**: Slate-50 main, white cards
- **Borders**: Slate-200 for subtle separation

**Typography**:
- **Font Family**: Inter (Google Fonts)
- **Headings**: `text-2xl font-semibold` for section titles
- **Body**: `text-sm font-medium` for labels, `text-base` for content
- **Metrics**: `text-2xl font-bold` for key numbers

**Components**:
- **Cards**: `bg-white rounded-lg border border-slate-200 shadow-sm`
- **Buttons**: Primary blue with `hover:bg-primary-700` states
- **Status Badges**: Color-coded for campaign types and statuses
- **Icons**: Lucide React icon set with proper sizing

### Layout Patterns
- **Grid System**: Responsive CSS Grid with `grid-cols-1 md:grid-cols-4` patterns
- **Spacing**: Consistent `p-6` padding and `gap-6` spacing
- **Card Design**: Consistent elevation and border radius
- **Interactive Elements**: Proper hover states and visual feedback

## Technical Implementation

### Framework
- **CSS Framework**: Tailwind CSS v3.4.0 via CDN
- **Icons**: Inline SVG icons matching Lucide React library
- **Fonts**: Inter from Google Fonts
- **Responsive**: Mobile-first responsive design

### Browser Compatibility
- Modern browsers supporting CSS Grid and Flexbox
- Progressive enhancement for older browsers
- Optimized for desktop viewing (primary use case for agency tool)

## Usage for API Application

These mockups serve as visual evidence for the Google Ads API Basic Access application, demonstrating:

1. **Professional Agency Tool**: High-quality interface suitable for managing 20+ client accounts
2. **Comprehensive Campaign Management**: Support for all Google Ads campaign types
3. **AI-Powered Features**: Advanced optimization and insights capabilities
4. **Multi-Client Architecture**: Efficient management of multiple client accounts
5. **Reporting Capabilities**: Client report generation and performance tracking

The mockups align perfectly with our submitted API application details, showing a legitimate, professional tool built by Brand Wisdom Solutions for internal agency use.

## Viewing the Mockups

Simply open the HTML files in any modern web browser to view the interactive mockups. All styling is self-contained with Tailwind CDN, requiring no build process or additional dependencies.