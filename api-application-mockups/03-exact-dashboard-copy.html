<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Brand Wisdom Google Ads AI Campaign Management Platform</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: "#F4F7FF",
                            100: "#E7EEFF", 
                            200: "#D1E1FF",
                            300: "#B9CBFF",
                            400: "#8DA8FF",
                            500: "#4172F5",
                            600: "#3E5CE7",
                            700: "#324ECF",
                            800: "#2A3FB8",
                            900: "#07153F"
                        },
                        success: "#27C084",
                        gold: {
                            500: "#EAB308"
                        },
                        emerald: {
                            600: "#059669"
                        }
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .animate-pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: .5; }
        }
    </style>
</head>
<body class="min-h-screen bg-slate-50">
    <!-- Sidebar -->
    <aside class="fixed inset-y-0 left-0 z-40 bg-white border-r border-slate-200 w-64">
        <!-- Header Section -->
        <header class="h-16 flex items-center justify-between px-4 border-b border-slate-200">
            <!-- Logo -->
            <div class="flex items-center gap-3 min-w-0">
                <div class="flex-shrink-0">
                    <img src="../Assets/Brand Wisdom Icon.png" alt="Brand Wisdom Solutions" class="h-8 w-8">
                </div>
                <div>
                    <h1 class="text-base font-semibold text-slate-900 whitespace-nowrap">
                        Brand Wisdom
                    </h1>
                    <p class="text-xs text-slate-500 font-medium">
                        AdsAI Platform
                    </p>
                </div>
            </div>
        </header>

        <!-- Quick Stats -->
        <section class="py-4 px-4 border-b border-slate-200">
            <div class="space-y-3">
                <!-- Active Clients -->
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-2">
                        <svg class="h-4 w-4 text-slate-600 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
                        </svg>
                        <span class="text-xs text-slate-600">Active Clients</span>
                    </div>
                    <span class="font-semibold text-slate-900 text-sm">24</span>
                </div>

                <!-- Monthly Spend -->
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-2">
                        <svg class="h-4 w-4 text-slate-600 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                        </svg>
                        <span class="text-xs text-slate-600">Monthly Spend</span>
                    </div>
                    <span class="font-semibold text-emerald-600 text-sm">₹4.04Cr</span>
                </div>
            </div>
        </section>

        <!-- Navigation -->
        <nav class="flex-1 px-3 py-4 overflow-y-auto">
            <ul class="space-y-1">
                <!-- Core Campaign Management (50%) -->
                <li>
                    <a href="#dashboard" class="group relative flex items-center px-3 py-2.5 rounded-lg text-sm font-medium bg-primary-50 text-primary-700 shadow-sm">
                        <svg class="w-5 h-5 flex-shrink-0 mr-3 text-primary-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                        </svg>
                        Multi-Client Dashboard
                    </a>
                </li>
                <li>
                    <a href="#campaigns" class="group relative flex items-center px-3 py-2.5 rounded-lg text-sm font-medium text-slate-600 hover:bg-slate-50 hover:text-slate-900">
                        <svg class="w-5 h-5 flex-shrink-0 mr-3 text-slate-500 group-hover:text-slate-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                        </svg>
                        Campaign Manager
                        <span class="ml-auto text-xs bg-slate-100 text-slate-600 px-2 py-0.5 rounded-full">All Types</span>
                    </a>
                </li>
                <li>
                    <a href="#accounts" class="group relative flex items-center px-3 py-2.5 rounded-lg text-sm font-medium text-slate-600 hover:bg-slate-50 hover:text-slate-900">
                        <svg class="w-5 h-5 flex-shrink-0 mr-3 text-slate-500 group-hover:text-slate-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                        </svg>
                        Client Accounts
                        <span class="ml-auto text-xs bg-emerald-100 text-emerald-700 px-2 py-0.5 rounded-full">24</span>
                    </a>
                </li>
                <li>
                    <a href="#reports" class="group relative flex items-center px-3 py-2.5 rounded-lg text-sm font-medium text-slate-600 hover:bg-slate-50 hover:text-slate-900">
                        <svg class="w-5 h-5 flex-shrink-0 mr-3 text-slate-500 group-hover:text-slate-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                        </svg>
                        Performance Reports
                    </a>
                </li>
                <li>
                    <a href="#bidding" class="group relative flex items-center px-3 py-2.5 rounded-lg text-sm font-medium text-slate-600 hover:bg-slate-50 hover:text-slate-900">
                        <svg class="w-5 h-5 flex-shrink-0 mr-3 text-slate-500 group-hover:text-slate-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                        </svg>
                        Bid & Budget Manager
                    </a>
                </li>
                <li>
                    <a href="#alerts" class="group relative flex items-center px-3 py-2.5 rounded-lg text-sm font-medium text-slate-600 hover:bg-slate-50 hover:text-slate-900">
                        <svg class="w-5 h-5 flex-shrink-0 mr-3 text-slate-500 group-hover:text-slate-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                        </svg>
                        Alerts & Monitoring
                        <span class="ml-auto w-2 h-2 bg-red-500 rounded-full"></span>
                    </a>
                </li>
                
                <!-- Divider -->
                <li class="pt-4">
                    <div class="flex items-center">
                        <div class="flex-1 border-t border-slate-200"></div>
                        <span class="px-3 text-xs text-slate-500 bg-slate-50">AI Assistance</span>
                        <div class="flex-1 border-t border-slate-200"></div>
                    </div>
                </li>
                
                <!-- AI-Powered Assistance (50%) -->
                <li>
                    <a href="#optimization" class="group relative flex items-center px-3 py-2.5 rounded-lg text-sm font-medium text-slate-600 hover:bg-slate-50 hover:text-slate-900">
                        <svg class="w-5 h-5 flex-shrink-0 mr-3 text-slate-500 group-hover:text-slate-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                        </svg>
                        Optimization Center
                        <span class="ml-auto text-xs bg-purple-100 text-purple-700 px-2 py-0.5 rounded-full">AI</span>
                    </a>
                </li>
                <li>
                    <a href="#insights" class="group relative flex items-center px-3 py-2.5 rounded-lg text-sm font-medium text-slate-600 hover:bg-slate-50 hover:text-slate-900">
                        <svg class="w-5 h-5 flex-shrink-0 mr-3 text-slate-500 group-hover:text-slate-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                        Search Query Insights
                        <span class="ml-auto text-xs bg-purple-100 text-purple-700 px-2 py-0.5 rounded-full">AI</span>
                    </a>
                </li>
                <li>
                    <a href="#quality-scores" class="group relative flex items-center px-3 py-2.5 rounded-lg text-sm font-medium text-slate-600 hover:bg-slate-50 hover:text-slate-900">
                        <svg class="w-5 h-5 flex-shrink-0 mr-3 text-slate-500 group-hover:text-slate-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"/>
                        </svg>
                        Quality Score Enhancer
                        <span class="ml-auto text-xs bg-purple-100 text-purple-700 px-2 py-0.5 rounded-full">AI</span>
                    </a>
                </li>
                <li>
                    <a href="#ad-testing" class="group relative flex items-center px-3 py-2.5 rounded-lg text-sm font-medium text-slate-600 hover:bg-slate-50 hover:text-slate-900">
                        <svg class="w-5 h-5 flex-shrink-0 mr-3 text-slate-500 group-hover:text-slate-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                        </svg>
                        Ad Copy Testing
                        <span class="ml-auto text-xs bg-purple-100 text-purple-700 px-2 py-0.5 rounded-full">AI</span>
                    </a>
                </li>
                
                <!-- Settings -->
                <li class="pt-4">
                    <a href="#settings" class="group relative flex items-center px-3 py-2.5 rounded-lg text-sm font-medium text-slate-600 hover:bg-slate-50 hover:text-slate-900">
                        <svg class="w-5 h-5 flex-shrink-0 mr-3 text-slate-500 group-hover:text-slate-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                        </svg>
                        Team Settings
                    </a>
                </li>
            </ul>
        </nav>

        <!-- User & Settings -->
        <footer class="px-3 py-4 border-t border-slate-200 space-y-2">
            <!-- User Profile -->
            <div class="flex items-center px-3 py-2 rounded-lg hover:bg-slate-50 cursor-pointer">
                <svg class="w-5 h-5 text-slate-600 flex-shrink-0 mr-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                </svg>
                <span class="text-sm text-slate-700 truncate">Team Member</span>
            </div>

            <!-- Sign Out -->
            <button class="w-full flex items-center px-3 py-2 rounded-lg hover:bg-red-50 text-sm text-slate-600 hover:text-red-600">
                <svg class="w-5 h-5 text-slate-600 hover:text-red-600 flex-shrink-0 mr-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
                </svg>
                Sign Out
            </button>
        </footer>
    </aside>

    <!-- Main Content -->
    <main class="lg:pl-64">
        <!-- Top Bar -->
        <header class="bg-white border-b border-slate-200 sticky top-0 z-30 shadow-sm">
            <div class="flex items-center justify-between h-16 px-6 lg:px-8">
                <!-- Left side - Mobile menu and search -->
                <div class="flex items-center gap-3">
                    <!-- Search -->
                    <div class="hidden md:flex items-center gap-2 px-3 py-1.5 bg-slate-50 rounded-lg text-sm w-64">
                        <svg class="h-4 w-4 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                        <input type="text" placeholder="Search campaigns, keywords..." class="bg-transparent outline-none flex-1 text-slate-700 placeholder-slate-400">
                    </div>
                </div>
                <!-- Client Selector -->
                <div class="relative">
                    <button class="flex items-center gap-2 px-3 py-1.5 rounded-lg bg-slate-100 hover:bg-slate-200 text-sm">
                        <svg class="h-4 w-4 text-slate-500" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M4 3a2 2 0 100 4h12a2 2 0 100-4H4z"/>
                            <path fill-rule="evenodd" d="M3 8a2 2 0 012-2v9a2 2 0 002 2h6a2 2 0 002-2V6a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2V8z" clip-rule="evenodd"/>
                        </svg>
                        <span class="font-medium text-slate-900">All Clients</span>
                        <svg class="h-4 w-4 text-slate-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                        </svg>
                    </button>
                </div>
                <!-- Right side - Quick actions -->
                <div class="flex items-center gap-2">
                    <button class="h-8 w-8 hidden md:flex items-center justify-center text-slate-500 hover:text-slate-700 rounded-lg hover:bg-slate-100">
                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3"/>
                        </svg>
                    </button>
                    <button class="relative h-8 w-8 rounded-lg hover:bg-slate-100 flex items-center justify-center">
                        <svg class="h-4 w-4 text-slate-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 1h5l-5 5V1z"/>
                        </svg>
                    </button>
                </div>
            </div>
        </header>

        <!-- Dashboard Content -->
        <div class="min-h-screen bg-slate-50">

            <!-- Top Action Bar -->
            <div class="bg-white border-b border-slate-200">
                <div class="max-w-screen-2xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-base font-semibold text-slate-900">Multi-Client Campaign Management</h1>
                            <p class="text-xs text-slate-500">Tuesday, January 7, 2025 - MCC: 310-946-3592 | 24 Active Clients</p>
                        </div>
                        <div class="flex items-center gap-2">
                            <button class="flex items-center gap-1.5 h-8 px-3 text-xs border border-slate-300 rounded-lg hover:bg-slate-50">
                                <svg class="h-3.5 w-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                </svg>
                                Last 7 days
                                <svg class="h-3.5 w-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                                </svg>
                            </button>
                            <button class="flex items-center gap-1 bg-primary-600 hover:bg-primary-700 h-8 px-3 text-xs text-white rounded-lg">
                                <svg class="h-3.5 w-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                                </svg>
                                New Campaign
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="max-w-screen-2xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
                <!-- Key Metrics -->
                <div class="mb-4">
                    <h2 class="text-xs font-medium text-slate-600 uppercase tracking-wider mb-2">Key Performance Metrics</h2>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                        <div class="bg-white rounded-md border border-slate-200 p-3">
                            <div class="flex items-center justify-between mb-1">
                                <span class="text-[11px] font-medium text-slate-500">Active Clients</span>
                                <span class="text-[11px] font-medium text-emerald-600">+12%</span>
                            </div>
                            <p class="text-lg font-semibold text-slate-900">24</p>
                        </div>
                        <div class="bg-white rounded-md border border-slate-200 p-3">
                            <div class="flex items-center justify-between mb-1">
                                <span class="text-[11px] font-medium text-slate-500">Total Client Spend</span>
                                <span class="text-[11px] font-medium text-emerald-600">+8%</span>
                            </div>
                            <p class="text-lg font-semibold text-slate-900">₹40,42,220</p>
                        </div>
                        <div class="bg-white rounded-md border border-slate-200 p-3">
                            <div class="flex items-center justify-between mb-1">
                                <span class="text-[11px] font-medium text-slate-500">Active Campaigns</span>
                                <span class="text-[11px] font-medium text-emerald-600">+3</span>
                            </div>
                            <p class="text-lg font-semibold text-slate-900">187</p>
                        </div>
                        <div class="bg-white rounded-md border border-slate-200 p-3">
                            <div class="flex items-center justify-between mb-1">
                                <span class="text-[11px] font-medium text-slate-500">Today's Alerts</span>
                                <span class="text-[11px] font-medium text-amber-600">3 New</span>
                            </div>
                            <p class="text-lg font-semibold text-slate-900">7 Total</p>
                        </div>
                    </div>
                </div>

                <!-- Two Column Layout -->
                <div class="grid grid-cols-1 xl:grid-cols-3 gap-4">
                    <!-- Left Column - Campaigns -->
                    <div class="xl:col-span-2 space-y-4">
                        <!-- Campaign Performance -->
                        <div>
                            <h2 class="text-xs font-medium text-slate-600 uppercase tracking-wider mb-2">Campaign Performance</h2>
                            <div class="border-slate-200 shadow-sm hover:shadow-md bg-white rounded-lg border">
                                <div class="flex flex-row items-center justify-between p-4">
                                    <div class="text-base font-semibold flex items-center gap-2">
                                        <svg class="h-5 w-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                                        </svg>
                                        Active Campaigns
                                    </div>
                                    <button class="h-9 text-sm px-3 hover:bg-slate-50 border border-slate-200 rounded-lg flex items-center gap-1.5">
                                        View All 
                                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                        </svg>
                                    </button>
                                </div>
                                <div class="p-4 pt-0">
                                    <div class="space-y-3">
                                        <!-- Campaign 1 -->
                                        <div class="border border-slate-200 rounded-lg p-4 hover:bg-slate-50 hover:shadow-sm cursor-pointer">
                                            <div class="flex items-start justify-between mb-2">
                                                <div class="flex-1">
                                                    <h4 class="font-semibold text-base text-slate-900">TechStart - Core SaaS Keywords</h4>
                                                    <div class="flex items-center gap-3 mt-1">
                                                        <span class="inline-flex items-center gap-1 px-2 py-1 rounded-md text-xs font-medium bg-emerald-100 text-emerald-700">
                                                            <svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"/>
                                                            </svg>
                                                            active
                                                        </span>
                                                        <span class="text-xs text-slate-600">Budget: ₹4,15,000</span>
                                                    </div>
                                                </div>
                                                <div class="text-center">
                                                    <p class="text-xs text-slate-600 font-medium">Quality Score</p>
                                                    <div class="text-2xl font-bold text-emerald-600">8.4</div>
                                                </div>
                                            </div>

                                            <div class="grid grid-cols-5 gap-4 mb-3 mt-3">
                                                <div class="text-center">
                                                    <p class="text-xs text-slate-600">Spend</p>
                                                    <p class="font-semibold text-sm text-slate-900">₹3,51,090</p>
                                                </div>
                                                <div class="text-center">
                                                    <p class="text-xs text-slate-600">Impressions</p>
                                                    <p class="font-semibold text-sm text-slate-900">125.4K</p>
                                                </div>
                                                <div class="text-center">
                                                    <p class="text-xs text-slate-600">Clicks</p>
                                                    <p class="font-semibold text-sm text-slate-900">3.2K</p>
                                                </div>
                                                <div class="text-center">
                                                    <p class="text-xs text-slate-600">CTR</p>
                                                    <p class="font-semibold text-sm flex items-center justify-center gap-1">
                                                        <span class="text-emerald-600">2.8%</span>
                                                        <svg class="h-3 w-3 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
                                                        </svg>
                                                    </p>
                                                </div>
                                                <div class="text-center">
                                                    <p class="text-xs text-slate-600">CPC</p>
                                                    <p class="font-semibold text-sm text-slate-900">₹109.56</p>
                                                </div>
                                            </div>

                                            <div class="mb-3 p-3 bg-slate-50 rounded-lg">
                                                <p class="text-xs font-medium text-slate-700 mb-2">7-Day Performance Trend</p>
                                                <div class="h-8 flex items-end justify-between">
                                                    <div class="w-2 bg-emerald-500 rounded-t" style="height: 60%"></div>
                                                    <div class="w-2 bg-emerald-500 rounded-t" style="height: 80%"></div>
                                                    <div class="w-2 bg-emerald-500 rounded-t" style="height: 40%"></div>
                                                    <div class="w-2 bg-emerald-500 rounded-t" style="height: 90%"></div>
                                                    <div class="w-2 bg-emerald-500 rounded-t" style="height: 70%"></div>
                                                    <div class="w-2 bg-emerald-500 rounded-t" style="height: 100%"></div>
                                                    <div class="w-2 bg-emerald-500 rounded-t" style="height: 85%"></div>
                                                </div>
                                            </div>

                                            <div class="flex gap-2">
                                                <button class="flex-1 h-9 text-sm font-medium hover:bg-slate-50 border border-slate-200 rounded-lg">View Details</button>
                                                <button class="flex-1 h-9 text-sm font-medium hover:bg-primary-50 hover:text-primary-700 hover:border-primary-300 border border-slate-200 rounded-lg">Quick Optimize</button>
                                            </div>
                                        </div>

                                        <!-- Campaign 2 -->
                                        <div class="border border-slate-200 rounded-lg p-4 hover:bg-slate-50 hover:shadow-sm cursor-pointer">
                                            <div class="flex items-start justify-between mb-2">
                                                <div class="flex-1">
                                                    <h4 class="font-semibold text-base text-slate-900">FinanceApp - Lead Generation</h4>
                                                    <div class="flex items-center gap-3 mt-1">
                                                        <span class="inline-flex items-center gap-1 px-2 py-1 rounded-md text-xs font-medium bg-emerald-100 text-emerald-700">
                                                            <svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"/>
                                                            </svg>
                                                            active
                                                        </span>
                                                        <span class="text-xs text-slate-600">Budget: ₹2,90,500</span>
                                                    </div>
                                                </div>
                                                <div class="text-center">
                                                    <p class="text-xs text-slate-600 font-medium">Quality Score</p>
                                                    <div class="text-2xl font-bold text-amber-600">6.8</div>
                                                </div>
                                            </div>

                                            <div class="grid grid-cols-5 gap-4 mb-3 mt-3">
                                                <div class="text-center">
                                                    <p class="text-xs text-slate-600">Spend</p>
                                                    <p class="font-semibold text-sm text-slate-900">₹2,39,870</p>
                                                </div>
                                                <div class="text-center">
                                                    <p class="text-xs text-slate-600">Impressions</p>
                                                    <p class="font-semibold text-sm text-slate-900">89.2K</p>
                                                </div>
                                                <div class="text-center">
                                                    <p class="text-xs text-slate-600">Clicks</p>
                                                    <p class="font-semibold text-sm text-slate-900">1.8K</p>
                                                </div>
                                                <div class="text-center">
                                                    <p class="text-xs text-slate-600">CTR</p>
                                                    <p class="font-semibold text-sm flex items-center justify-center gap-1">
                                                        <span class="text-red-600">2.0%</span>
                                                        <svg class="h-3 w-3 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"/>
                                                        </svg>
                                                    </p>
                                                </div>
                                                <div class="text-center">
                                                    <p class="text-xs text-slate-600">CPC</p>
                                                    <p class="font-semibold text-sm text-slate-900">₹133.63</p>
                                                </div>
                                            </div>

                                            <div class="mb-3 p-3 bg-slate-50 rounded-lg">
                                                <p class="text-xs font-medium text-slate-700 mb-2">7-Day Performance Trend</p>
                                                <div class="h-8 flex items-end justify-between">
                                                    <div class="w-2 bg-red-500 rounded-t" style="height: 80%"></div>
                                                    <div class="w-2 bg-red-500 rounded-t" style="height: 60%"></div>
                                                    <div class="w-2 bg-red-500 rounded-t" style="height: 70%"></div>
                                                    <div class="w-2 bg-red-500 rounded-t" style="height: 50%"></div>
                                                    <div class="w-2 bg-red-500 rounded-t" style="height: 55%"></div>
                                                    <div class="w-2 bg-red-500 rounded-t" style="height: 45%"></div>
                                                    <div class="w-2 bg-red-500 rounded-t" style="height: 40%"></div>
                                                </div>
                                            </div>

                                            <div class="flex gap-2">
                                                <button class="flex-1 h-9 text-sm font-medium hover:bg-slate-50 border border-slate-200 rounded-lg">View Details</button>
                                                <button class="flex-1 h-9 text-sm font-medium hover:bg-primary-50 hover:text-primary-700 hover:border-primary-300 border border-slate-200 rounded-lg">Quick Optimize</button>
                                            </div>
                                        </div>

                                        <!-- Campaign 3 -->
                                        <div class="border border-slate-200 rounded-lg p-4 hover:bg-slate-50 hover:shadow-sm cursor-pointer">
                                            <div class="flex items-start justify-between mb-2">
                                                <div class="flex-1">
                                                    <h4 class="font-semibold text-base text-slate-900">RetailCorp - Shopping Campaign</h4>
                                                    <div class="flex items-center gap-3 mt-1">
                                                        <span class="inline-flex items-center gap-1 px-2 py-1 rounded-md text-xs font-medium bg-emerald-100 text-emerald-700">
                                                            <svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"/>
                                                            </svg>
                                                            active
                                                        </span>
                                                        <span class="text-xs text-slate-600">Budget: ₹6,22,500</span>
                                                    </div>
                                                </div>
                                                <div class="text-center">
                                                    <p class="text-xs text-slate-600 font-medium">Quality Score</p>
                                                    <div class="text-2xl font-bold text-emerald-600">9.1</div>
                                                </div>
                                            </div>

                                            <div class="grid grid-cols-5 gap-4 mb-3 mt-3">
                                                <div class="text-center">
                                                    <p class="text-xs text-slate-600">Spend</p>
                                                    <p class="font-semibold text-sm text-slate-900">₹5,67,720</p>
                                                </div>
                                                <div class="text-center">
                                                    <p class="text-xs text-slate-600">Impressions</p>
                                                    <p class="font-semibold text-sm text-slate-900">234.1K</p>
                                                </div>
                                                <div class="text-center">
                                                    <p class="text-xs text-slate-600">Clicks</p>
                                                    <p class="font-semibold text-sm text-slate-900">8.9K</p>
                                                </div>
                                                <div class="text-center">
                                                    <p class="text-xs text-slate-600">CTR</p>
                                                    <p class="font-semibold text-sm flex items-center justify-center gap-1">
                                                        <span class="text-emerald-600">3.8%</span>
                                                        <svg class="h-3 w-3 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
                                                        </svg>
                                                    </p>
                                                </div>
                                                <div class="text-center">
                                                    <p class="text-xs text-slate-600">CPC</p>
                                                    <p class="font-semibold text-sm text-slate-900">₹63.91</p>
                                                </div>
                                            </div>

                                            <div class="mb-3 p-3 bg-slate-50 rounded-lg">
                                                <p class="text-xs font-medium text-slate-700 mb-2">7-Day Performance Trend</p>
                                                <div class="h-8 flex items-end justify-between">
                                                    <div class="w-2 bg-emerald-500 rounded-t" style="height: 70%"></div>
                                                    <div class="w-2 bg-emerald-500 rounded-t" style="height: 85%"></div>
                                                    <div class="w-2 bg-emerald-500 rounded-t" style="height: 90%"></div>
                                                    <div class="w-2 bg-emerald-500 rounded-t" style="height: 95%"></div>
                                                    <div class="w-2 bg-emerald-500 rounded-t" style="height: 80%"></div>
                                                    <div class="w-2 bg-emerald-500 rounded-t" style="height: 100%"></div>
                                                    <div class="w-2 bg-emerald-500 rounded-t" style="height: 92%"></div>
                                                </div>
                                            </div>

                                            <div class="flex gap-2">
                                                <button class="flex-1 h-9 text-sm font-medium hover:bg-slate-50 border border-slate-200 rounded-lg">View Details</button>
                                                <button class="flex-1 h-9 text-sm font-medium hover:bg-primary-50 hover:text-primary-700 hover:border-primary-300 border border-slate-200 rounded-lg">Quick Optimize</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Performance Charts -->
                        <div>
                            <h2 class="text-xs font-medium text-slate-600 uppercase tracking-wider mb-2">Performance Trends</h2>
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-3">
                                <div class="bg-white rounded-lg border border-slate-200 shadow-sm p-4">
                                    <h3 class="text-sm font-semibold text-slate-900 mb-3">Impressions & Clicks</h3>
                                    <div class="h-32 bg-slate-50 rounded-lg flex items-center justify-center">
                                        <svg class="h-8 w-8 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                                        </svg>
                                    </div>
                                </div>
                                <div class="bg-white rounded-lg border border-slate-200 shadow-sm p-4">
                                    <h3 class="text-sm font-semibold text-slate-900 mb-3">Spend & Conversions</h3>
                                    <div class="h-32 bg-slate-50 rounded-lg flex items-center justify-center">
                                        <svg class="h-8 w-8 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Right Column - Activity and Quick Actions -->
                    <div class="space-y-4">
                        <!-- Recent Activity -->
                        <div>
                            <h2 class="text-xs font-medium text-slate-600 uppercase tracking-wider mb-2">Recent Activity</h2>
                            <div class="bg-white rounded-lg border border-slate-200 shadow-sm">
                                <div class="p-4">
                                    <div class="space-y-4">
                                        <div class="flex items-start gap-3">
                                            <div class="w-8 h-8 bg-emerald-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                                <svg class="w-4 h-4 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                </svg>
                                            </div>
                                            <div class="flex-1 min-w-0">
                                                <p class="text-sm font-medium text-slate-900">Campaign optimization completed</p>
                                                <p class="text-xs text-slate-500">TechStart - Core SaaS Keywords</p>
                                                <p class="text-xs text-slate-400">2 hours ago</p>
                                            </div>
                                        </div>

                                        <div class="flex items-start gap-3">
                                            <div class="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                                <svg class="w-4 h-4 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                                                </svg>
                                            </div>
                                            <div class="flex-1 min-w-0">
                                                <p class="text-sm font-medium text-slate-900">New campaign created</p>
                                                <p class="text-xs text-slate-500">HealthTech - Display Awareness</p>
                                                <p class="text-xs text-slate-400">4 hours ago</p>
                                            </div>
                                        </div>

                                        <div class="flex items-start gap-3">
                                            <div class="w-8 h-8 bg-amber-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                                <svg class="w-4 h-4 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                                                </svg>
                                            </div>
                                            <div class="flex-1 min-w-0">
                                                <p class="text-sm font-medium text-slate-900">Quality score alert</p>
                                                <p class="text-xs text-slate-500">FinanceApp - Lead Generation</p>
                                                <p class="text-xs text-slate-400">6 hours ago</p>
                                            </div>
                                        </div>

                                        <div class="flex items-start gap-3">
                                            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                                <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                                                </svg>
                                            </div>
                                            <div class="flex-1 min-w-0">
                                                <p class="text-sm font-medium text-slate-900">AI recommendation generated</p>
                                                <p class="text-xs text-slate-500">15 new negative keywords suggested</p>
                                                <p class="text-xs text-slate-400">8 hours ago</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Daily Agency Actions -->
                        <div>
                            <h2 class="text-xs font-medium text-slate-600 uppercase tracking-wider mb-2">Daily Actions</h2>
                            <div class="bg-white rounded-lg border border-slate-200">
                                <div class="p-3">
                                    <div class="grid grid-cols-1 gap-2">
                                        <!-- Create New Campaign -->
                                        <button class="w-full justify-start text-sm font-medium hover:bg-primary-50 hover:border-primary-300 flex items-center p-3 border border-slate-200 rounded-lg group transition-all">
                                            <div class="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-primary-200">
                                                <svg class="h-4 w-4 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                                                </svg>
                                            </div>
                                            <div class="text-left flex-1">
                                                <div class="font-medium text-slate-900 group-hover:text-primary-900">Create Campaign</div>
                                                <div class="text-xs text-slate-500">Search, Display, Shopping, Performance Max</div>
                                            </div>
                                            <div class="text-xs bg-slate-100 text-slate-600 px-2 py-0.5 rounded-full">All Types</div>
                                        </button>

                                        <!-- Client Reports -->
                                        <button class="w-full justify-start text-sm font-medium hover:bg-emerald-50 hover:border-emerald-300 flex items-center p-3 border border-slate-200 rounded-lg group transition-all">
                                            <div class="w-8 h-8 bg-emerald-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-emerald-200">
                                                <svg class="h-4 w-4 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                                                </svg>
                                            </div>
                                            <div class="text-left flex-1">
                                                <div class="font-medium text-slate-900 group-hover:text-emerald-900">Generate Reports</div>
                                                <div class="text-xs text-slate-500">Client performance & insights</div>
                                            </div>
                                        </button>

                                        <!-- Bulk Bid Adjustments -->
                                        <button class="w-full justify-start text-sm font-medium hover:bg-blue-50 hover:border-blue-300 flex items-center p-3 border border-slate-200 rounded-lg group transition-all">
                                            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-blue-200">
                                                <svg class="h-4 w-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                                                </svg>
                                            </div>
                                            <div class="text-left flex-1">
                                                <div class="font-medium text-slate-900 group-hover:text-blue-900">Bulk Bid Manager</div>
                                                <div class="text-xs text-slate-500">Adjust bids across accounts</div>
                                            </div>
                                        </button>

                                        <!-- Keyword Planning -->
                                        <button class="w-full justify-start text-sm font-medium hover:bg-purple-50 hover:border-purple-300 flex items-center p-3 border border-slate-200 rounded-lg group transition-all">
                                            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-purple-200">
                                                <svg class="h-4 w-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                                </svg>
                                            </div>
                                            <div class="text-left flex-1">
                                                <div class="font-medium text-slate-900 group-hover:text-purple-900">Keyword Planner</div>
                                                <div class="text-xs text-slate-500">Research & expansion tools</div>
                                            </div>
                                        </button>

                                        <!-- Budget Monitor -->
                                        <button class="w-full justify-start text-sm font-medium hover:bg-amber-50 hover:border-amber-300 flex items-center p-3 border border-slate-200 rounded-lg group transition-all">
                                            <div class="w-8 h-8 bg-amber-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-amber-200">
                                                <svg class="h-4 w-4 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                                                </svg>
                                            </div>
                                            <div class="text-left flex-1">
                                                <div class="font-medium text-slate-900 group-hover:text-amber-900">Budget Monitor</div>
                                                <div class="text-xs text-slate-500">Check all client budgets</div>
                                            </div>
                                            <div class="text-xs bg-amber-100 text-amber-700 px-2 py-0.5 rounded-full">3 Alerts</div>
                                        </button>

                                        <!-- Account Audits -->
                                        <button class="w-full justify-start text-sm font-medium hover:bg-slate-50 hover:border-slate-300 flex items-center p-3 border border-slate-200 rounded-lg group transition-all">
                                            <div class="w-8 h-8 bg-slate-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-slate-200">
                                                <svg class="h-4 w-4 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                </svg>
                                            </div>
                                            <div class="text-left flex-1">
                                                <div class="font-medium text-slate-900 group-hover:text-slate-900">Account Audits</div>
                                                <div class="text-xs text-slate-500">Quality & performance checks</div>
                                            </div>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Account Manager Section - Full Width -->
                <div class="mt-6">
                    <div class="flex items-center justify-between mb-2">
                        <h2 class="text-xs font-medium text-slate-600 uppercase tracking-wider">Client Accounts Overview</h2>
                        <button class="h-8 text-xs border border-slate-200 rounded-lg px-3 hover:bg-slate-50 flex items-center gap-1.5">
                            <svg class="h-3.5 w-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
                            </svg>
                            Add Client
                        </button>
                    </div>
                    <div class="bg-white rounded-lg border border-slate-200 shadow-sm overflow-hidden">
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead class="bg-slate-50 border-b border-slate-200">
                                    <tr>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Client</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Industry</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Campaigns</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Monthly Spend</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">ROAS</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-slate-200">
                                    <tr class="hover:bg-slate-50">
                                        <td class="px-4 py-3">
                                            <div class="flex items-center">
                                                <div class="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center mr-3">
                                                    <span class="text-xs font-semibold text-primary-700">TS</span>
                                                </div>
                                                <div>
                                                    <div class="text-sm font-medium text-slate-900">TechStart Solutions</div>
                                                    <div class="text-xs text-slate-500">Account ID: 123-456-7890</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-4 py-3 text-sm text-slate-600">B2B SaaS</td>
                                        <td class="px-4 py-3 text-sm text-slate-900">8 active</td>
                                        <td class="px-4 py-3 text-sm font-medium text-slate-900">₹11,81,090</td>
                                        <td class="px-4 py-3">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800">4.2x</span>
                                        </td>
                                        <td class="px-4 py-3">
                                            <button class="text-primary-600 hover:text-primary-900 text-sm font-medium">View Details</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-slate-50">
                                        <td class="px-4 py-3">
                                            <div class="flex items-center">
                                                <div class="w-8 h-8 bg-emerald-100 rounded-lg flex items-center justify-center mr-3">
                                                    <span class="text-xs font-semibold text-emerald-700">FA</span>
                                                </div>
                                                <div>
                                                    <div class="text-sm font-medium text-slate-900">FinanceApp</div>
                                                    <div class="text-xs text-slate-500">Account ID: 234-567-8901</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-4 py-3 text-sm text-slate-600">Fintech</td>
                                        <td class="px-4 py-3 text-sm text-slate-900">5 active</td>
                                        <td class="px-4 py-3 text-sm font-medium text-slate-900">₹7,38,700</td>
                                        <td class="px-4 py-3">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800">3.1x</span>
                                        </td>
                                        <td class="px-4 py-3">
                                            <button class="text-primary-600 hover:text-primary-900 text-sm font-medium">View Details</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-slate-50">
                                        <td class="px-4 py-3">
                                            <div class="flex items-center">
                                                <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                                                    <span class="text-xs font-semibold text-purple-700">RC</span>
                                                </div>
                                                <div>
                                                    <div class="text-sm font-medium text-slate-900">RetailCorp</div>
                                                    <div class="text-xs text-slate-500">Account ID: 345-678-9012</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-4 py-3 text-sm text-slate-600">E-commerce</td>
                                        <td class="px-4 py-3 text-sm text-slate-900">12 active</td>
                                        <td class="px-4 py-3 text-sm font-medium text-slate-900">₹19,46,350</td>
                                        <td class="px-4 py-3">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800">5.8x</span>
                                        </td>
                                        <td class="px-4 py-3">
                                            <button class="text-primary-600 hover:text-primary-900 text-sm font-medium">View Details</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- AI Insights Engine Section -->
                <div class="mt-6">
                    <div class="flex items-center justify-between mb-2">
                        <h2 class="text-xs font-medium text-slate-600 uppercase tracking-wider">AI-Powered Insights Engine</h2>
                        <button class="h-8 text-xs border border-slate-200 rounded-lg px-3 hover:bg-slate-50 flex items-center gap-1.5">
                            <svg class="h-3.5 w-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                            </svg>
                            Ask AI: "Why did CPC increase?"
                        </button>
                    </div>
                    <div class="bg-white rounded-lg border border-slate-200 shadow-sm">
                        <div class="p-6">
                            <div class="space-y-4">
                                <!-- High-Priority Insight -->
                                <div class="flex items-start space-x-3 p-4 bg-red-50 border border-red-200 rounded-lg">
                                    <div class="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center flex-shrink-0">
                                        <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <div class="flex items-center justify-between mb-2">
                                            <p class="text-sm font-medium text-slate-900">🚨 Budget exhaustion alert across 3 clients</p>
                                            <span class="text-xs bg-red-100 text-red-700 px-2 py-1 rounded-full">High Priority</span>
                                        </div>
                                        <p class="text-xs text-slate-600 mb-2">TechStart, FinanceApp, and RetailCorp will exhaust daily budgets by 3 PM. Recommend increasing budget by 25% to capture evening traffic.</p>
                                        <div class="flex items-center space-x-2">
                                            <button class="text-xs bg-red-600 text-white px-3 py-1 rounded-md hover:bg-red-700">Auto-increase budgets</button>
                                            <button class="text-xs border border-red-300 text-red-700 px-3 py-1 rounded-md hover:bg-red-50">Schedule optimization</button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Opportunity Insight -->
                                <div class="flex items-start space-x-3 p-4 bg-emerald-50 border border-emerald-200 rounded-lg">
                                    <div class="w-8 h-8 bg-emerald-500 rounded-lg flex items-center justify-center flex-shrink-0">
                                        <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <div class="flex items-center justify-between mb-2">
                                            <p class="text-sm font-medium text-slate-900">💡 47 new profitable search terms discovered</p>
                                            <span class="text-xs bg-emerald-100 text-emerald-700 px-2 py-1 rounded-full">Opportunity</span>
                                        </div>
                                        <p class="text-xs text-slate-600 mb-2">AI identified high-converting terms like "enterprise CRM solution" and "SaaS customer platform" with 3.8x ROAS potential across B2B clients.</p>
                                        <div class="flex items-center space-x-2">
                                            <button class="text-xs bg-emerald-600 text-white px-3 py-1 rounded-md hover:bg-emerald-700">Add as keywords</button>
                                            <button class="text-xs border border-emerald-300 text-emerald-700 px-3 py-1 rounded-md hover:bg-emerald-50">Review terms</button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Quality Score Insight -->
                                <div class="flex items-start space-x-3 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                                    <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center flex-shrink-0">
                                        <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <div class="flex items-center justify-between mb-2">
                                            <p class="text-sm font-medium text-slate-900">🎯 Quality Score improvement of +1.8 points detected</p>
                                            <span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">Performance</span>
                                        </div>
                                        <p class="text-xs text-slate-600 mb-2">Ad relevance optimization on TechStart campaigns resulted in Quality Score increase from 6.2 to 8.0, reducing CPC by 23% and improving impression share.</p>
                                        <div class="flex items-center space-x-2">
                                            <button class="text-xs bg-blue-600 text-white px-3 py-1 rounded-md hover:bg-blue-700">Apply to all clients</button>
                                            <button class="text-xs border border-blue-300 text-blue-700 px-3 py-1 rounded-md hover:bg-blue-50">View details</button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Automation Status -->
                                <div class="flex items-start space-x-3 p-4 bg-purple-50 border border-purple-200 rounded-lg">
                                    <div class="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center flex-shrink-0">
                                        <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd"/>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <div class="flex items-center justify-between mb-2">
                                            <p class="text-sm font-medium text-slate-900">⚡ 127 optimizations auto-applied in last 24 hours</p>
                                            <span class="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded-full">Automation</span>
                                        </div>
                                        <p class="text-xs text-slate-600 mb-2">Negative keywords added (43), bids adjusted (31), ad extensions optimized (28), match types refined (25). Estimated cost savings: ₹1,94,220.</p>
                                        <div class="flex items-center space-x-2">
                                            <button class="text-xs bg-purple-600 text-white px-3 py-1 rounded-md hover:bg-purple-700">View activity log</button>
                                            <button class="text-xs border border-purple-300 text-purple-700 px-3 py-1 rounded-md hover:bg-purple-50">Automation settings</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- AI Assistant Query Interface -->
                <div class="mt-6">
                    <div class="bg-gradient-to-r from-primary-600 to-purple-600 rounded-lg shadow-sm p-6">
                        <div class="flex items-center space-x-4">
                            <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <h3 class="text-lg font-semibold text-white mb-1">AI Assistant - Natural Language Queries</h3>
                                <p class="text-sm text-white/80">Ask complex questions about your campaigns in plain English</p>
                            </div>
                        </div>
                        <div class="mt-4">
                            <div class="flex items-center space-x-3">
                                <div class="flex-1 relative">
                                    <input type="text" placeholder='Try: "Why did TechStart\'s CPC increase yesterday?" or "Which keywords have the best Quality Scores?"' class="w-full px-4 py-3 pr-12 rounded-lg border-0 text-slate-900 placeholder-slate-500 focus:ring-2 focus:ring-white/20">
                                    <button class="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                        </svg>
                                    </button>
                                </div>
                                <button class="px-6 py-3 bg-white text-primary-600 font-medium rounded-lg hover:bg-white/90 transition-colors">
                                    Ask AI
                                </button>
                            </div>
                            <div class="mt-3 flex flex-wrap gap-2">
                                <span class="text-xs text-white/70">Popular queries:</span>
                                <button class="text-xs bg-white/20 text-white px-2 py-1 rounded-full hover:bg-white/30">"Show my best performing ads"</button>
                                <button class="text-xs bg-white/20 text-white px-2 py-1 rounded-full hover:bg-white/30">"Which clients need budget increases?"</button>
                                <button class="text-xs bg-white/20 text-white px-2 py-1 rounded-full hover:bg-white/30">"Find wasted spend opportunities"</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
</body>
</html>