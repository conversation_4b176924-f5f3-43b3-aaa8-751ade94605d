<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Brand Wisdom Solutions - Google Ads AI Platform Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: "#F4F7FF",
                            100: "#E7EEFF",
                            300: "#B9CBFF",
                            500: "#4172F5",
                            600: "#3E5CE7",
                            700: "#324ECF",
                            900: "#07153F"
                        },
                        success: "#27C084",
                        error: "#EF5E5E",
                        'brand-primary': '#3E5CE7',
                        'brand-secondary': '#444751',
                        'brand-success': '#27C084',
                        'brand-error': '#EF5E5E'
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .chart-line { stroke-dasharray: 5; animation: dash 2s linear infinite; }
        @keyframes dash { to { stroke-dashoffset: -10; } }
    </style>
</head>
<body class="bg-slate-50">
    <!-- Header -->
    <header class="bg-white border-b border-slate-200 shadow-sm">
        <div class="px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <h1 class="text-xl font-semibold text-slate-900">Brand Wisdom Solutions</h1>
                        <span class="text-sm text-slate-500">Google Ads AI Platform</span>
                    </div>
                </div>
                
                <div class="flex items-center space-x-4">
                    <!-- Client Selector -->
                    <div class="relative">
                        <select class="appearance-none bg-white border border-slate-300 rounded-lg px-4 py-2 pr-8 text-sm font-medium text-slate-700 focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                            <option>All Clients (23)</option>
                            <option>TechStart Solutions</option>
                            <option>Local Bakery Co.</option>
                            <option>Fashion Forward Inc.</option>
                            <option>Green Energy Corp.</option>
                        </select>
                    </div>
                    
                    <!-- User Menu -->
                    <div class="flex items-center space-x-3">
                        <button class="p-2 text-slate-400 hover:text-slate-600 relative">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5-5-5h5zm0 0V3"/>
                            </svg>
                            <span class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
                        </button>
                        <div class="flex items-center space-x-3 pl-3 border-l border-slate-200">
                            <img class="w-8 h-8 rounded-full" src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 32 32'%3E%3Crect width='32' height='32' fill='%234172F5'/%3E%3Ctext x='16' y='20' text-anchor='middle' fill='white' font-family='Arial' font-size='14' font-weight='bold'%3EJS%3C/text%3E%3C/svg%3E" alt="User">
                            <span class="text-sm font-medium text-slate-700">John Smith</span>
                            <svg class="w-4 h-4 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Dashboard -->
    <div class="p-6">
        <!-- Key Metrics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg border border-slate-200 shadow-sm p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-slate-600">Active Clients</p>
                        <p class="text-3xl font-bold text-slate-900 mt-2">23</p>
                        <p class="text-sm text-success mt-1">+2 this month</p>
                    </div>
                    <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                        </svg>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg border border-slate-200 shadow-sm p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-slate-600">Total Campaigns</p>
                        <p class="text-2xl font-semibold text-slate-900 mt-1">187</p>
                        <p class="text-sm text-brand-success mt-1">94% active</p>
                    </div>
                    <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-brand-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                        </svg>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg border border-slate-200 shadow-sm p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-slate-600">This Month Conversions</p>
                        <p class="text-2xl font-semibold text-slate-900 mt-1">1,247</p>
                        <p class="text-sm text-brand-success mt-1">+23% vs last month</p>
                    </div>
                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-brand-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
                        </svg>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg border border-slate-200 shadow-sm p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-slate-600">Cost Savings</p>
                        <p class="text-3xl font-bold text-slate-900 mt-2">$12,340</p>
                        <p class="text-sm text-success mt-1">AI optimizations</p>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Chart -->
        <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6 mb-8">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-slate-900">Campaign Performance Trends</h3>
                <div class="flex items-center space-x-2">
                    <select class="appearance-none bg-slate-50 border border-slate-200 rounded-lg px-3 py-1 text-sm text-slate-600">
                        <option>Last 30 days</option>
                        <option>Last 90 days</option>
                        <option>Last 6 months</option>
                    </select>
                </div>
            </div>
            
            <!-- Simplified Chart Representation -->
            <div class="h-64 relative">
                <svg class="w-full h-full" viewBox="0 0 800 200">
                    <!-- Grid lines -->
                    <defs>
                        <pattern id="grid" width="80" height="40" patternUnits="userSpaceOnUse">
                            <path d="M 80 0 L 0 0 0 40" fill="none" stroke="#e2e8f0" stroke-width="1"/>
                        </pattern>
                    </defs>
                    <rect width="100%" height="100%" fill="url(#grid)" />
                    
                    <!-- Conversion line -->
                    <polyline fill="none" stroke="#4172F5" stroke-width="3" points="40,160 120,140 200,120 280,100 360,80 440,60 520,70 600,50 680,40 760,30"/>
                    
                    <!-- Revenue line -->
                    <polyline fill="none" stroke="#27C084" stroke-width="3" points="40,180 120,170 200,150 280,130 360,110 440,90 520,100 600,80 680,70 760,60"/>
                    
                    <!-- Data points -->
                    <circle cx="760" cy="30" r="4" fill="#4172F5"/>
                    <circle cx="760" cy="60" r="4" fill="#27C084"/>
                </svg>
                
                <!-- Legend -->
                <div class="absolute bottom-4 left-4 flex items-center space-x-6">
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-primary-500 rounded-full"></div>
                        <span class="text-sm text-slate-600">Conversions</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-success rounded-full"></div>
                        <span class="text-sm text-slate-600">Revenue</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- AI Recommendations and Recent Activity -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- AI Recommendations -->
            <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-slate-900">Recent AI Recommendations</h3>
                    <span class="bg-primary-100 text-primary-700 text-xs font-medium px-2 py-1 rounded-full">7 New</span>
                </div>
                
                <div class="space-y-4">
                    <div class="flex items-start space-x-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                        <div class="w-8 h-8 bg-success rounded-lg flex items-center justify-center flex-shrink-0">
                            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <p class="text-sm font-medium text-slate-900">Add 47 negative keywords to E-commerce Client A campaigns</p>
                            <p class="text-xs text-slate-500 mt-1">Potential savings: $2,340/month • Confidence: 94%</p>
                        </div>
                        <button class="text-success hover:text-green-700 text-sm font-medium">Apply</button>
                    </div>
                    
                    <div class="flex items-start space-x-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                        <div class="w-8 h-8 bg-primary-500 rounded-lg flex items-center justify-center flex-shrink-0">
                            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <p class="text-sm font-medium text-slate-900">Increase bids by 15% for B2B Client C's top converting keywords</p>
                            <p class="text-xs text-slate-500 mt-1">Expected revenue increase: +$5,600 • Confidence: 87%</p>
                        </div>
                        <button class="text-primary-600 hover:text-primary-700 text-sm font-medium">Review</button>
                    </div>
                    
                    <div class="flex items-start space-x-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <div class="w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center flex-shrink-0">
                            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <p class="text-sm font-medium text-slate-900">Optimize ad copy for Local Business D's underperforming ads</p>
                            <p class="text-xs text-slate-500 mt-1">Expected CTR improvement: +2.3% • Confidence: 91%</p>
                        </div>
                        <button class="text-yellow-600 hover:text-yellow-700 text-sm font-medium">Generate</button>
                    </div>
                </div>
                
                <div class="mt-4 pt-4 border-t border-slate-200">
                    <button class="w-full text-primary-600 hover:text-primary-700 text-sm font-medium">View All Recommendations</button>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
                <h3 class="text-lg font-semibold text-slate-900 mb-6">Recent Activity</h3>
                
                <div class="space-y-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-2 h-2 bg-success rounded-full"></div>
                        <div class="flex-1">
                            <p class="text-sm text-slate-900">Campaign "Holiday Sale 2024" created for TechStart Solutions</p>
                            <p class="text-xs text-slate-500">2 minutes ago</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-3">
                        <div class="w-2 h-2 bg-primary-500 rounded-full"></div>
                        <div class="flex-1">
                            <p class="text-sm text-slate-900">AI optimization applied to Fashion Forward Inc. campaigns</p>
                            <p class="text-xs text-slate-500">15 minutes ago</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-3">
                        <div class="w-2 h-2 bg-yellow-500 rounded-full"></div>
                        <div class="flex-1">
                            <p class="text-sm text-slate-900">Budget increased for Green Energy Corp. Performance Max campaign</p>
                            <p class="text-xs text-slate-500">1 hour ago</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-3">
                        <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                        <div class="flex-1">
                            <p class="text-sm text-slate-900">Alert: Local Bakery Co. campaign approaching daily budget limit</p>
                            <p class="text-xs text-slate-500">3 hours ago</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-3">
                        <div class="w-2 h-2 bg-slate-400 rounded-full"></div>
                        <div class="flex-1">
                            <p class="text-sm text-slate-900">Monthly report generated for all clients</p>
                            <p class="text-xs text-slate-500">1 day ago</p>
                        </div>
                    </div>
                </div>
                
                <div class="mt-4 pt-4 border-t border-slate-200">
                    <button class="w-full text-primary-600 hover:text-primary-700 text-sm font-medium">View Activity Log</button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>