import React from 'react'
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '../ui/card.jsx'
import { Button } from '../ui/button.jsx'
import { 
  TrendingUp, 
  TrendingDown, 
  BarChart3,
  <PERSON>Pointer,
  Eye,
  DollarSign,
  Activity,
  ArrowRight,
  Play,
  Pause,
  Square,
  ChevronRight
} from 'lucide-react'
import { Sparkline } from '../charts/Sparkline.jsx'

export const CampaignOverview = ({ campaigns = [] }) => {
  const getStatusIcon = (status) => {
    switch (status) {
      case 'active':
        return <Play className="h-3 w-3" />
      case 'paused':
        return <Pause className="h-3 w-3" />
      case 'ended':
        return <Square className="h-3 w-3" />
      default:
        return null
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'bg-emerald-100 text-emerald-700'
      case 'paused':
        return 'bg-amber-100 text-amber-700'
      case 'ended':
        return 'bg-slate-100 text-slate-700'
      default:
        return 'bg-slate-100 text-slate-600'
    }
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const formatNumber = (num) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K'
    }
    return num.toString()
  }

  return (
    <Card className="border-slate-200 shadow-sm hover:shadow-md transition-shadow duration-200">
      <CardHeader className="flex flex-row items-center justify-between p-4">
        <CardTitle className="text-base font-semibold flex items-center gap-2">
          <BarChart3 className="h-5 w-5 text-primary-600" />
          Active Campaigns
        </CardTitle>
        <Button variant="ghost" size="sm" className="h-9 text-sm px-3 hover:bg-slate-50">
          View All <ArrowRight className="h-4 w-4 ml-1.5" />
        </Button>
      </CardHeader>
      <CardContent className="p-4 pt-0">
        {campaigns.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-20 h-20 bg-slate-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <BarChart3 className="h-10 w-10 text-slate-400" />
            </div>
            <p className="text-lg font-semibold text-slate-900 mb-2">No campaigns found</p>
            <p className="text-sm text-slate-600">
              Connect your Google Ads account to see campaigns
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {campaigns.slice(0, 3).map((campaign) => (
              <div 
                key={campaign.id}
                className="border border-slate-200 rounded-lg p-4 hover:bg-slate-50 hover:shadow-sm transition-all duration-200 cursor-pointer"
              >
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1">
                    <h4 className="font-semibold text-base text-slate-900">
                      {campaign.name}
                    </h4>
                    <div className="flex items-center gap-3 mt-1">
                      <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-md text-xs font-medium ${getStatusColor(campaign.status)}`}>
                        {getStatusIcon(campaign.status)}
                        {campaign.status}
                      </span>
                      <span className="text-xs text-slate-600">
                        Budget: {formatCurrency(campaign.budget)}
                      </span>
                    </div>
                  </div>
                  <div className="text-center">
                    <p className="text-xs text-slate-600 font-medium">
                      Quality Score
                    </p>
                    <div className={`text-2xl font-bold ${
                      campaign.quality_score >= 8 ? 'text-emerald-600' :
                      campaign.quality_score >= 6 ? 'text-amber-600' :
                      'text-red-600'
                    }`}>
                      {campaign.quality_score}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-5 gap-4 mb-3 mt-3">
                  <div className="text-center">
                    <p className="text-xs text-slate-600">Spend</p>
                    <p className="font-semibold text-sm text-slate-900">
                      {formatCurrency(campaign.spend)}
                    </p>
                  </div>

                  <div className="text-center">
                    <p className="text-xs text-slate-600">Impressions</p>
                    <p className="font-semibold text-sm text-slate-900">
                      {formatNumber(campaign.impressions)}
                    </p>
                  </div>

                  <div className="text-center">
                    <p className="text-xs text-slate-600">Clicks</p>
                    <p className="font-semibold text-sm text-slate-900">
                      {formatNumber(campaign.clicks)}
                    </p>
                  </div>

                  <div className="text-center">
                    <p className="text-xs text-slate-600">CTR</p>
                    <p className="font-semibold text-sm flex items-center justify-center gap-1">
                      <span className={campaign.ctr > 2.5 ? 'text-emerald-600' : 'text-red-600'}>
                        {campaign.ctr}%
                      </span>
                      {campaign.ctr > 2.5 ? (
                        <TrendingUp className="h-3 w-3 text-emerald-600" />
                      ) : (
                        <TrendingDown className="h-3 w-3 text-red-600" />
                      )}
                    </p>
                  </div>

                  <div className="text-center">
                    <p className="text-xs text-slate-600">CPC</p>
                    <p className="font-semibold text-sm text-slate-900">
                      ${campaign.cpc}
                    </p>
                  </div>
                </div>

                {/* Performance Trend */}
                <div className="mb-3 p-3 bg-slate-50 rounded-lg">
                  <p className="text-xs font-medium text-slate-700 mb-2">7-Day Performance Trend</p>
                  <Sparkline 
                    color={campaign.ctr > 2.5 ? '#10B981' : '#EF4444'} 
                    height={32}
                    showTrend={true}
                  />
                </div>

                <div className="flex gap-2">
                  <Button size="sm" variant="outline" className="flex-1 h-9 text-sm font-medium hover:bg-slate-50">
                    View Details
                  </Button>
                  <Button size="sm" variant="outline" className="flex-1 h-9 text-sm font-medium hover:bg-primary-50 hover:text-primary-700 hover:border-primary-300">
                    Quick Optimize
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}