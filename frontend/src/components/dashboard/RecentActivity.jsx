import React from 'react'
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '../ui/card.jsx'
import { 
  Activity, 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle,
  Info,
  Clock,
  Zap,
  DollarSign,
  Target
} from 'lucide-react'

export const RecentActivity = ({ activities = [] }) => {
  const getActivityIcon = (type) => {
    switch (type) {
      case 'optimization':
        return <TrendingUp className="h-4 w-4 text-emerald-600" />
      case 'alert':
        return <AlertTriangle className="h-4 w-4 text-amber-600" />
      case 'success':
        return <CheckCircle className="h-4 w-4 text-primary-600" />
      case 'info':
        return <Info className="h-4 w-4 text-slate-500" />
      default:
        return <Activity className="h-4 w-4 text-slate-500" />
    }
  }

  const getActivityColor = (type) => {
    switch (type) {
      case 'optimization':
        return 'bg-emerald-50 border-emerald-200'
      case 'alert':
        return 'bg-amber-50 border-amber-200'
      case 'success':
        return 'bg-blue-50 border-blue-200'
      case 'info':
        return 'bg-slate-50 border-slate-200'
      default:
        return 'bg-slate-50 border-slate-200'
    }
  }

  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffMs = now - date
    const diffMins = Math.floor(diffMs / 60000)
    const diffHours = Math.floor(diffMs / 3600000)
    const diffDays = Math.floor(diffMs / 86400000)

    if (diffMins < 1) return 'Just now'
    if (diffMins < 60) return `${diffMins} minute${diffMins > 1 ? 's' : ''} ago`
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`
    if (diffDays < 7) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`
    
    return date.toLocaleDateString()
  }

  return (
    <Card className="dashboard-card">
      <CardHeader className="p-4">
        <CardTitle className="card-title">
          <Activity className="h-5 w-5 text-primary-600" />
          Recent Activity
        </CardTitle>
      </CardHeader>
      <CardContent className="p-4 pt-0">
        {activities.length === 0 ? (
          <div className="text-center py-8">
            <div className="w-16 h-16 bg-slate-100 rounded-xl flex items-center justify-center mx-auto mb-4">
              <Clock className="h-8 w-8 text-slate-400" />
            </div>
            <p className="text-base font-semibold text-slate-900 mb-2">No recent activity</p>
            <p className="text-sm text-slate-600">
              Your optimization activities will appear here
            </p>
          </div>
        ) : (
          <div className="space-y-2">
            {activities.slice(0, 5).map((activity, index) => (
              <div 
                key={activity.id}
                className={`activity-item ${getActivityColor(activity.type)}`}
              >
                <div className="flex-shrink-0">
                  {getActivityIcon(activity.type)}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="activity-message">
                    {activity.message}
                  </p>
                  {activity.impact && (
                    <p className="activity-impact">
                      {activity.impact}
                    </p>
                  )}
                  <p className="activity-time">
                    <Clock className="h-3 w-3" />
                    {formatTimestamp(activity.timestamp)}
                  </p>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}