import React, { useState } from 'react'
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle, CardDescription } from '../ui/card.jsx'
import { Button } from '../ui/button.jsx'
import { 
  ChevronRight, 
  ChevronLeft,
  Building,
  Users,
  Search,
  Zap,
  Target,
  Shield,
  CheckCircle,
  ArrowRight
} from 'lucide-react'

export const OnboardingFlow = ({ onComplete, userProfile }) => {
  const [currentStep, setCurrentStep] = useState(0)
  const [completedSteps, setCompletedSteps] = useState([])

  const steps = [
    {
      id: 'welcome',
      title: 'Welcome to Brand Wisdom Agency Platform',
      icon: Building,
      content: (
        <div className="space-y-4">
          <div className="text-center mb-6">
            <Building className="h-24 w-24 text-primary-600 mx-auto mb-4" />
            <h2 className="text-h2 font-display font-bold text-primary-900 mb-2">
              Welcome, {userProfile?.full_name || 'Team Member'}!
            </h2>
            <p className="text-body-lg text-text">
              Let's get you set up to manage client Google Ads campaigns
            </p>
          </div>
          
          <div className="bg-primary-50 rounded-lg p-6">
            <h3 className="font-semibold text-primary-900 mb-3">What you'll learn:</h3>
            <ul className="space-y-2">
              <li className="flex items-start gap-2">
                <CheckCircle className="h-5 w-5 text-success mt-0.5" />
                <span className="text-body-md">Access and manage client accounts</span>
              </li>
              <li className="flex items-start gap-2">
                <CheckCircle className="h-5 w-5 text-success mt-0.5" />
                <span className="text-body-md">Use AI-powered optimization tools</span>
              </li>
              <li className="flex items-start gap-2">
                <CheckCircle className="h-5 w-5 text-success mt-0.5" />
                <span className="text-body-md">Monitor campaign performance</span>
              </li>
              <li className="flex items-start gap-2">
                <CheckCircle className="h-5 w-5 text-success mt-0.5" />
                <span className="text-body-md">Collaborate with team members</span>
              </li>
            </ul>
          </div>
        </div>
      )
    },
    {
      id: 'client-access',
      title: 'Client Account Access',
      icon: Users,
      content: (
        <div className="space-y-4">
          <div className="flex items-center gap-4 mb-6">
            <Users className="h-12 w-12 text-primary-600" />
            <div>
              <h3 className="text-h3 font-display font-bold text-primary-900">
                Managing Client Accounts
              </h3>
              <p className="text-body-md text-text">
                All client accounts are managed through our agency's Google Ads Manager (MCC)
              </p>
            </div>
          </div>

          <div className="space-y-4">
            <div className="bg-white rounded-lg p-4 border border-grey-100">
              <h4 className="font-semibold text-primary-900 mb-2">Account Hierarchy</h4>
              <div className="space-y-3 ml-4">
                <div className="flex items-center gap-3">
                  <div className="w-4 h-4 bg-primary-600 rounded"></div>
                  <span className="text-body-md">
                    <strong>Agency MCC</strong> - Brand Wisdom Master Account
                  </span>
                </div>
                <div className="ml-6 space-y-2">
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 bg-success rounded-full"></div>
                    <span className="text-body-sm text-text">Client Account 1</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 bg-success rounded-full"></div>
                    <span className="text-body-sm text-text">Client Account 2</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 bg-success rounded-full"></div>
                    <span className="text-body-sm text-text">Client Account 3...</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-gold-500/10 rounded-lg p-4 border border-gold-500/20">
              <p className="text-body-sm text-primary-900">
                <strong>Note:</strong> You'll have access to all client accounts linked to our agency MCC. 
                Use the client selector in the top navigation to switch between accounts.
              </p>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'optimization-tools',
      title: 'AI Optimization Tools',
      icon: Zap,
      content: (
        <div className="space-y-4">
          <div className="flex items-center gap-4 mb-6">
            <Zap className="h-12 w-12 text-primary-600" />
            <div>
              <h3 className="text-h3 font-display font-bold text-primary-900">
                Powerful Optimization Tools
              </h3>
              <p className="text-body-md text-text">
                Save hours with our AI-powered optimization features
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-start gap-3">
                  <Search className="h-8 w-8 text-primary-600 mt-1" />
                  <div>
                    <h4 className="font-semibold text-primary-900">Search Mining</h4>
                    <p className="text-body-sm text-text mt-1">
                      Find profitable keywords and eliminate wasted spend automatically
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-start gap-3">
                  <Target className="h-8 w-8 text-primary-600 mt-1" />
                  <div>
                    <h4 className="font-semibold text-primary-900">Bulk Optimize</h4>
                    <p className="text-body-sm text-text mt-1">
                      Apply winning strategies across multiple accounts at once
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-start gap-3">
                  <Zap className="h-8 w-8 text-primary-600 mt-1" />
                  <div>
                    <h4 className="font-semibold text-primary-900">Ad Generator</h4>
                    <p className="text-body-sm text-text mt-1">
                      Create compelling ad copy with AI assistance
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-start gap-3">
                  <Shield className="h-8 w-8 text-primary-600 mt-1" />
                  <div>
                    <h4 className="font-semibold text-primary-900">Negative Keywords</h4>
                    <p className="text-body-sm text-text mt-1">
                      Maintain master negative lists across all accounts
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )
    },
    {
      id: 'best-practices',
      title: 'Agency Best Practices',
      icon: CheckCircle,
      content: (
        <div className="space-y-4">
          <div className="flex items-center gap-4 mb-6">
            <CheckCircle className="h-12 w-12 text-success" />
            <div>
              <h3 className="text-h3 font-display font-bold text-primary-900">
                Best Practices
              </h3>
              <p className="text-body-md text-text">
                Follow these guidelines for optimal results
              </p>
            </div>
          </div>

          <div className="space-y-4">
            <div className="bg-white rounded-lg p-4 border border-grey-100">
              <h4 className="font-semibold text-primary-900 mb-3">Daily Workflow</h4>
              <ol className="space-y-2 list-decimal list-inside">
                <li className="text-body-md">Check the dashboard for performance alerts</li>
                <li className="text-body-md">Review AI optimization recommendations</li>
                <li className="text-body-md">Apply bulk optimizations across similar accounts</li>
                <li className="text-body-md">Monitor client-specific KPIs</li>
              </ol>
            </div>

            <div className="bg-white rounded-lg p-4 border border-grey-100">
              <h4 className="font-semibold text-primary-900 mb-3">Collaboration Tips</h4>
              <ul className="space-y-2">
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-primary-600 rounded-full mt-2"></div>
                  <span className="text-body-md">Document major changes in campaign notes</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-primary-600 rounded-full mt-2"></div>
                  <span className="text-body-md">Use shared negative keyword lists</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-primary-600 rounded-full mt-2"></div>
                  <span className="text-body-md">Tag campaigns with client industry for bulk actions</span>
                </li>
              </ul>
            </div>

            <div className="bg-success/10 rounded-lg p-4 border border-success/20">
              <p className="text-body-sm text-primary-900">
                <strong>Pro Tip:</strong> Use the client selector to quickly switch between accounts, 
                or select "All Clients" to see aggregate performance data.
              </p>
            </div>
          </div>
        </div>
      )
    }
  ]

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCompletedSteps([...completedSteps, currentStep])
      setCurrentStep(currentStep + 1)
    } else {
      onComplete()
    }
  }

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleSkip = () => {
    onComplete()
  }

  const progress = ((currentStep + 1) / steps.length) * 100

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-white flex items-center justify-center p-6">
      <div className="max-w-4xl w-full">
        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-text">
              Step {currentStep + 1} of {steps.length}
            </span>
            <button
              onClick={handleSkip}
              className="text-sm text-primary-600 hover:text-primary-700 font-medium"
            >
              Skip onboarding
            </button>
          </div>
          <div className="w-full bg-grey-100 rounded-full h-2">
            <div 
              className="bg-primary-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>

        {/* Step Content */}
        <Card className="shadow-lg">
          <CardHeader className="text-center pb-2">
            <CardTitle className="text-h3 font-display">
              {steps[currentStep].title}
            </CardTitle>
          </CardHeader>
          <CardContent className="p-8">
            {steps[currentStep].content}
          </CardContent>
          
          {/* Navigation */}
          <div className="flex items-center justify-between p-6 border-t border-grey-100">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentStep === 0}
              className={currentStep === 0 ? 'invisible' : ''}
            >
              <ChevronLeft className="h-4 w-4 mr-2" />
              Previous
            </Button>

            <div className="flex gap-2">
              {steps.map((_, index) => (
                <div
                  key={index}
                  className={`w-2 h-2 rounded-full transition-all ${
                    index === currentStep 
                      ? 'bg-primary-600 w-8' 
                      : completedSteps.includes(index)
                      ? 'bg-success'
                      : 'bg-grey-100'
                  }`}
                />
              ))}
            </div>

            <Button onClick={handleNext}>
              {currentStep === steps.length - 1 ? (
                <>
                  Get Started
                  <ArrowRight className="h-4 w-4 ml-2" />
                </>
              ) : (
                <>
                  Next
                  <ChevronRight className="h-4 w-4 ml-2" />
                </>
              )}
            </Button>
          </div>
        </Card>
      </div>
    </div>
  )
}