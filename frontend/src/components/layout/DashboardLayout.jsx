import { useState, useEffect } from 'react'
import { useAuth } from '../../contexts/AuthContext.jsx'
import { Button } from '../ui/button.jsx'
import { fetchWithAuth, googleAdsApi } from '../../lib/api.js'
import { CommandPalette } from '../ui/CommandPalette.jsx'
import {
  BarChart3,
  Search,
  Target,
  Zap,
  Settings,
  LogOut,
  User,
  Bell,
  Building,
  Menu,
  X,
  ChevronRight,
  ChevronDown,
  Users,
  Command,
  DollarSign,
  ExternalLink
} from 'lucide-react'

export const DashboardLayout = ({ children }) => {
  const { user, signOut } = useAuth()
  const [userProfile, setUserProfile] = useState(null)
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [sidebarCollapsed, setSidebarCollapsed] = useState(() => {
    // Initialize from localStorage with proper error handling
    try {
      return localStorage.getItem('sidebarCollapsed') === 'true'
    } catch {
      return false
    }
  })
  const [clientAccounts, setClientAccounts] = useState([])
  const [loadingAccounts, setLoadingAccounts] = useState(true)

  // Helper function to toggle sidebar with proper state management
  const toggleSidebar = (collapsed) => {
    setSidebarCollapsed(collapsed)
    try {
      localStorage.setItem('sidebarCollapsed', collapsed.toString())
    } catch (error) {
      console.warn('Failed to save sidebar state:', error)
    }
  }

  useEffect(() => {
    const fetchUserProfile = async () => {
      try {
        const response = await fetchWithAuth('/api/user/profile')
        if (response.ok) {
          const data = await response.json()
          setUserProfile(data)
        }
      } catch (error) {
        console.error('Failed to fetch user profile:', error)
      }
    }
    
    if (user) {
      fetchUserProfile()
    }
  }, [user])

  // Fetch Google Ads accounts
  useEffect(() => {
    const fetchAccounts = async () => {
      try {
        setLoadingAccounts(true)
        const accounts = await googleAdsApi.getAccounts()
        
        // Flatten the account structure for the dropdown
        const allAccounts = []
        accounts.forEach(account => {
          if (account.type === 'MCC' && account.sub_accounts) {
            // Add sub-accounts from MCC
            account.sub_accounts.forEach(subAccount => {
              allAccounts.push({
                id: subAccount.customer_id,
                name: subAccount.descriptive_name,
                active: true,
                isTestAccount: subAccount.test_account || true
              })
            })
          } else if (account.type === 'STANDARD') {
            // Add standard accounts
            allAccounts.push({
              id: account.customer_id,
              name: account.descriptive_name,
              active: true,
              isTestAccount: account.test_account || false
            })
          }
        })
        
        setClientAccounts(allAccounts)
      } catch (error) {
        console.error('Failed to fetch Google Ads accounts:', error)
        // Fallback to empty array if not connected
        setClientAccounts([])
      } finally {
        setLoadingAccounts(false)
      }
    }

    if (userProfile?.google_ads_connected) {
      fetchAccounts()
    } else {
      setLoadingAccounts(false)
    }
  }, [userProfile?.google_ads_connected])

  const navigationItems = [
    { icon: BarChart3, label: 'Dashboard', href: '#dashboard' },
    { icon: Users, label: 'All Clients', href: '#clients' },
    { icon: Search, label: 'Search Mining', href: '#search-mining' },
    { icon: Target, label: 'Bulk Optimize', href: '#bulk-optimize' },
    { icon: Zap, label: 'Ad Generator', href: '#ad-generator' },
    { icon: Settings, label: 'Team Settings', href: '#settings' },
  ]

  const currentPath = window.location.hash || '#dashboard'

  const [selectedClient, setSelectedClient] = useState(null)
  const [showClientDropdown, setShowClientDropdown] = useState(false)

  // Connect Google Ads OAuth flow
  const connectGoogleAds = async () => {
    try {
      setShowClientDropdown(false) // Close dropdown
      const data = await googleAdsApi.getAuthUrl()
      // Redirect to Google OAuth
      window.location.href = data.auth_url
    } catch (error) {
      console.error('Failed to start OAuth:', error)
      alert('Failed to connect Google Ads. Please try again.')
    }
  }

  // Keyboard shortcuts and focus management
  useEffect(() => {
    const handleKeyDown = (event) => {
      // Ctrl/Cmd + B to toggle sidebar
      if ((event.ctrlKey || event.metaKey) && event.key === 'b') {
        event.preventDefault()
        toggleSidebar(!sidebarCollapsed)
      }

      // Escape to close mobile sidebar
      if (event.key === 'Escape' && sidebarOpen) {
        setSidebarOpen(false)
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [sidebarCollapsed, sidebarOpen, toggleSidebar])

  // Close mobile sidebar when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (sidebarOpen && !event.target.closest('aside') && !event.target.closest('[data-mobile-menu-trigger]')) {
        setSidebarOpen(false)
      }
    }

    if (sidebarOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [sidebarOpen])

  return (
    <div className="min-h-screen bg-slate-50">
      {/* Command Palette */}
      <CommandPalette />

      {/* Sidebar Overlay for Mobile */}
      {sidebarOpen && (
        <div
          className="lg:hidden fixed inset-0 bg-black/50 z-30 animate-in fade-in duration-200"
          onClick={() => setSidebarOpen(false)}
          aria-hidden="true"
        />
      )}

      {/* Sidebar */}
      <aside
        className={`
          fixed inset-y-0 left-0 z-40 bg-white border-r border-slate-200
          transition-all duration-300 ease-in-out
          ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
          ${sidebarCollapsed ? 'lg:w-16' : 'lg:w-64'}
          lg:translate-x-0 w-64
        `}
        aria-label="Main navigation"
      >
        {/* Header Section */}
        <header className="h-16 flex items-center justify-between px-4 border-b border-slate-200">
          {/* Logo */}
          <div className="flex items-center gap-3 min-w-0">
            <div className="flex-shrink-0 p-2 bg-primary-600 rounded-lg">
              <Building className="h-5 w-5 text-white" />
            </div>
            <div className={`
              transition-all duration-300 overflow-hidden
              ${sidebarCollapsed ? 'lg:w-0 lg:opacity-0' : 'lg:w-auto lg:opacity-100'}
            `}>
              <h1 className="text-base font-semibold text-slate-900 whitespace-nowrap">
                Brand Wisdom
              </h1>
            </div>
          </div>

          {/* Toggle Button */}
          <button
            onClick={() => toggleSidebar(!sidebarCollapsed)}
            className={`
              flex items-center justify-center w-8 h-8 rounded-lg
              hover:bg-slate-100 transition-colors duration-200
              ${sidebarCollapsed ? 'lg:hidden' : 'hidden lg:flex'}
            `}
            aria-label={sidebarCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
          >
            <ChevronRight className={`
              h-4 w-4 text-slate-600 transition-transform duration-300
              ${sidebarCollapsed ? 'rotate-0' : 'rotate-180'}
            `} />
          </button>

          {/* Floating Expand Button (Collapsed State) */}
          {sidebarCollapsed && (
            <button
              onClick={() => toggleSidebar(false)}
              className="
                hidden lg:flex absolute top-4 -right-3 z-50
                items-center justify-center w-6 h-6
                bg-white border border-slate-200 rounded-full shadow-sm
                hover:bg-slate-50 hover:shadow-md transition-all duration-200
              "
              aria-label="Expand sidebar"
            >
              <ChevronRight className="h-3 w-3 text-slate-600" />
            </button>
          )}
        </header>

        {/* Quick Stats */}
        <section className="py-4 px-4 border-b border-slate-200">
          <div className="space-y-3">
            {/* Active Clients */}
            <div className={`
              group relative flex items-center justify-between
              ${sidebarCollapsed ? 'lg:flex-col lg:gap-1' : ''}
            `}>
              <div className={`
                flex items-center gap-2
                ${sidebarCollapsed ? 'lg:flex-col lg:gap-1' : ''}
              `}>
                <Users className="h-4 w-4 text-slate-600 flex-shrink-0" />
                <span className={`
                  text-xs text-slate-600 transition-all duration-300
                  ${sidebarCollapsed ? 'lg:hidden' : ''}
                `}>
                  Active Clients
                </span>
              </div>
              <span className={`
                font-semibold text-slate-900
                ${sidebarCollapsed ? 'text-xs lg:text-xs' : 'text-sm'}
              `}>
                24
              </span>

              {/* Tooltip */}
              {sidebarCollapsed && (
                <div className="
                  hidden lg:block absolute left-full ml-3 px-2 py-1 z-50
                  bg-slate-900 text-white text-xs rounded-md shadow-lg
                  opacity-0 invisible group-hover:opacity-100 group-hover:visible
                  transition-all duration-200 pointer-events-none whitespace-nowrap
                ">
                  Active Clients: 24
                </div>
              )}
            </div>

            {/* Monthly Spend */}
            <div className={`
              group relative flex items-center justify-between
              ${sidebarCollapsed ? 'lg:flex-col lg:gap-1' : ''}
            `}>
              <div className={`
                flex items-center gap-2
                ${sidebarCollapsed ? 'lg:flex-col lg:gap-1' : ''}
              `}>
                <DollarSign className="h-4 w-4 text-slate-600 flex-shrink-0" />
                <span className={`
                  text-xs text-slate-600 transition-all duration-300
                  ${sidebarCollapsed ? 'lg:hidden' : ''}
                `}>
                  Monthly Spend
                </span>
              </div>
              <span className={`
                font-semibold text-emerald-600
                ${sidebarCollapsed ? 'text-xs lg:text-xs' : 'text-sm'}
              `}>
                $487K
              </span>

              {/* Tooltip */}
              {sidebarCollapsed && (
                <div className="
                  hidden lg:block absolute left-full ml-3 px-2 py-1 z-50
                  bg-slate-900 text-white text-xs rounded-md shadow-lg
                  opacity-0 invisible group-hover:opacity-100 group-hover:visible
                  transition-all duration-200 pointer-events-none whitespace-nowrap
                ">
                  Monthly Spend: $487K
                </div>
              )}
            </div>
          </div>
        </section>

        {/* Navigation */}
        <nav className="flex-1 px-3 py-4 overflow-y-auto" role="navigation">
          <ul className="space-y-1" role="list">
            {navigationItems.map((item) => {
              const isActive = currentPath === item.href
              return (
                <li key={item.href} role="none">
                  <a
                    href={item.href}
                    onClick={() => setSidebarOpen(false)}
                    className={`
                      group relative flex items-center px-3 py-2.5 rounded-lg
                      text-sm font-medium transition-all duration-200
                      ${sidebarCollapsed ? 'lg:justify-center' : 'justify-center lg:justify-start'}
                      ${isActive
                        ? 'bg-primary-50 text-primary-700 shadow-sm'
                        : 'text-slate-600 hover:bg-slate-50 hover:text-slate-900'
                      }
                    `}
                    aria-current={isActive ? 'page' : undefined}
                  >
                    <item.icon className={`
                      w-5 h-5 flex-shrink-0 transition-colors duration-200
                      ${sidebarCollapsed ? '' : 'lg:mr-3'}
                      ${isActive ? 'text-primary-700' : 'text-slate-500 group-hover:text-slate-700'}
                    `} />

                    <span className={`
                      transition-all duration-300 overflow-hidden
                      ${sidebarCollapsed ? 'lg:w-0 lg:opacity-0' : 'hidden lg:inline lg:w-auto lg:opacity-100'}
                    `}>
                      {item.label}
                    </span>

                    {/* Active indicator for collapsed state */}
                    {isActive && sidebarCollapsed && (
                      <div className="
                        hidden lg:block absolute -left-1 top-1/2
                        w-1 h-6 bg-primary-600 rounded-r-full
                        transform -translate-y-1/2
                      " />
                    )}

                    {/* Tooltip for collapsed sidebar */}
                    {sidebarCollapsed && (
                      <div className="
                        hidden lg:block absolute left-full ml-3 px-3 py-2 z-50
                        bg-slate-900 text-white text-sm rounded-lg shadow-lg
                        opacity-0 invisible group-hover:opacity-100 group-hover:visible
                        transition-all duration-200 pointer-events-none whitespace-nowrap
                      ">
                        {item.label}
                        <div className="
                          absolute left-0 top-1/2 w-2 h-2 bg-slate-900
                          transform -translate-x-1 -translate-y-1/2 rotate-45
                        " />
                      </div>
                    )}
                  </a>
                </li>
              )
            })}
          </ul>
        </nav>

        {/* User & Settings */}
        <footer className="px-3 py-4 border-t border-slate-200 space-y-2">
          {/* User Profile */}
          <div className={`
            group relative flex items-center px-3 py-2 rounded-lg
            hover:bg-slate-50 cursor-pointer transition-all duration-200
            ${sidebarCollapsed ? 'lg:justify-center' : 'justify-center lg:justify-start'}
          `}>
            <User className={`
              w-5 h-5 text-slate-600 flex-shrink-0
              ${sidebarCollapsed ? '' : 'lg:mr-2.5'}
            `} />
            <span className={`
              text-sm text-slate-700 truncate transition-all duration-300 overflow-hidden
              ${sidebarCollapsed ? 'lg:w-0 lg:opacity-0' : 'hidden lg:inline lg:w-auto lg:opacity-100'}
            `}>
              {userProfile?.full_name || user?.email?.split('@')[0]}
            </span>

            {/* Tooltip */}
            {sidebarCollapsed && (
              <div className="
                hidden lg:block absolute left-full ml-3 px-3 py-2 z-50
                bg-slate-900 text-white text-sm rounded-lg shadow-lg
                opacity-0 invisible group-hover:opacity-100 group-hover:visible
                transition-all duration-200 pointer-events-none whitespace-nowrap
              ">
                {userProfile?.full_name || user?.email?.split('@')[0]}
                <div className="
                  absolute left-0 top-1/2 w-2 h-2 bg-slate-900
                  transform -translate-x-1 -translate-y-1/2 rotate-45
                " />
              </div>
            )}
          </div>

          {/* Settings Button */}
          <button
            onClick={() => window.location.hash = 'settings'}
            className={`
              group relative w-full flex items-center px-3 py-2 rounded-lg
              hover:bg-slate-50 transition-all duration-200
              text-sm text-slate-600 hover:text-slate-900
              ${sidebarCollapsed ? 'lg:justify-center' : 'justify-center lg:justify-start'}
            `}
          >
            <Settings className={`
              w-5 h-5 text-slate-600 flex-shrink-0
              ${sidebarCollapsed ? '' : 'lg:mr-2.5'}
            `} />
            <span className={`
              transition-all duration-300 overflow-hidden
              ${sidebarCollapsed ? 'lg:w-0 lg:opacity-0' : 'hidden lg:inline lg:w-auto lg:opacity-100'}
            `}>
              Settings
            </span>

            {/* Tooltip */}
            {sidebarCollapsed && (
              <div className="
                hidden lg:block absolute left-full ml-3 px-3 py-2 z-50
                bg-slate-900 text-white text-sm rounded-lg shadow-lg
                opacity-0 invisible group-hover:opacity-100 group-hover:visible
                transition-all duration-200 pointer-events-none whitespace-nowrap
              ">
                Settings
                <div className="
                  absolute left-0 top-1/2 w-2 h-2 bg-slate-900
                  transform -translate-x-1 -translate-y-1/2 rotate-45
                " />
              </div>
            )}
          </button>

          {/* Sign Out */}
          <button
            onClick={signOut}
            className={`
              group relative w-full flex items-center px-3 py-2 rounded-lg
              hover:bg-red-50 transition-all duration-200
              text-sm text-slate-600 hover:text-red-600
              ${sidebarCollapsed ? 'lg:justify-center' : 'justify-center lg:justify-start'}
            `}
          >
            <LogOut className={`
              w-5 h-5 text-slate-600 group-hover:text-red-600 flex-shrink-0
              transition-colors duration-200
              ${sidebarCollapsed ? '' : 'lg:mr-2.5'}
            `} />
            <span className={`
              transition-all duration-300 overflow-hidden
              ${sidebarCollapsed ? 'lg:w-0 lg:opacity-0' : 'hidden lg:inline lg:w-auto lg:opacity-100'}
            `}>
              Sign Out
            </span>

            {/* Tooltip */}
            {sidebarCollapsed && (
              <div className="
                hidden lg:block absolute left-full ml-3 px-3 py-2 z-50
                bg-slate-900 text-white text-sm rounded-lg shadow-lg
                opacity-0 invisible group-hover:opacity-100 group-hover:visible
                transition-all duration-200 pointer-events-none whitespace-nowrap
              ">
                Sign Out
                <div className="
                  absolute left-0 top-1/2 w-2 h-2 bg-slate-900
                  transform -translate-x-1 -translate-y-1/2 rotate-45
                " />
              </div>
            )}
          </button>
        </footer>
      </aside>

      {/* Main Content */}
      <main className={`
        min-h-screen transition-all duration-300 ease-in-out
        ${sidebarCollapsed ? 'lg:pl-16' : 'lg:pl-64'}
      `}>
        {/* Top Bar */}
        <header className="bg-white border-b border-slate-200 sticky top-0 z-30 shadow-sm">
          <div className="flex items-center justify-between h-16 px-6 lg:px-8">
            {/* Left side - Mobile menu and search */}
            <div className="flex items-center gap-3">
              <div className="lg:hidden">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setSidebarOpen(!sidebarOpen)}
                  className="h-8 w-8"
                  data-mobile-menu-trigger
                  aria-label={sidebarOpen ? 'Close menu' : 'Open menu'}
                >
                  {sidebarOpen ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
                </Button>
              </div>
              {/* Search */}
              <div className="hidden md:flex items-center gap-2 px-3 py-1.5 bg-slate-50 rounded-lg text-sm w-64">
                <Search className="h-4 w-4 text-slate-400" />
                <input 
                  type="text" 
                  placeholder="Search campaigns, keywords..." 
                  className="bg-transparent outline-none flex-1 text-slate-700 placeholder-slate-400"
                />
              </div>
            </div>
            {/* Client Selector */}
            <div className="relative">
              <button
                onClick={() => setShowClientDropdown(!showClientDropdown)}
                className={`flex items-center gap-2 px-3 py-1.5 rounded-lg transition-colors text-sm ${
                  !userProfile?.google_ads_connected ? 
                  'bg-primary-100 hover:bg-primary-200 border border-primary-300' : 
                  'bg-slate-100 hover:bg-slate-200'
                }`}
              >
                <Building className="h-4 w-4 text-slate-500" />
                <span className="font-medium text-slate-900">
                  {!userProfile?.google_ads_connected ? 
                    'Connect Account' : 
                    (selectedClient ? clientAccounts.find(c => c.id === selectedClient)?.name : 'All Clients')
                  }
                </span>
                <ChevronDown className="h-4 w-4 text-slate-500" />
              </button>
              
              {showClientDropdown && (
                <div className="absolute top-full left-0 mt-2 w-64 bg-white rounded-md shadow-lg border border-grey-100 z-50">
                  <div className="p-2">
                    {clientAccounts.length > 0 && (
                      <>
                        <button
                          onClick={() => {
                            setSelectedClient(null)
                            setShowClientDropdown(false)
                          }}
                          className="w-full text-left px-3 py-2 rounded hover:bg-grey-100 transition-colors"
                        >
                          <div className="flex items-center justify-between">
                            <span className="font-medium">All Clients</span>
                            <span className="text-xs text-text">View all</span>
                          </div>
                        </button>
                        <div className="border-t border-grey-100 my-2"></div>
                      </>
                    )}
                    {loadingAccounts ? (
                      <div className="px-3 py-3 text-sm text-text">
                        Loading accounts...
                      </div>
                    ) : clientAccounts.length === 0 ? (
                      <div className="px-3 py-3">
                        <p className="text-sm text-text mb-2">No accounts connected</p>
                        <Button 
                          onClick={connectGoogleAds}
                          size="sm"
                          className="w-full h-9 text-xs font-medium bg-primary-600 hover:bg-primary-700"
                        >
                          <ExternalLink className="h-3.5 w-3.5 mr-1.5" />
                          Connect Google Ads Account
                        </Button>
                      </div>
                    ) : (
                      clientAccounts.map(client => (
                        <button
                          key={client.id}
                          onClick={() => {
                            setSelectedClient(client.id)
                            setShowClientDropdown(false)
                          }}
                          className="w-full text-left px-3 py-2 rounded hover:bg-grey-100 transition-colors"
                        >
                          <div className="flex items-center justify-between">
                            <span className="font-medium">{client.name}</span>
                            {client.isTestAccount && (
                              <span className="text-xs bg-gold-500/20 text-gold-700 px-2 py-0.5 rounded">TEST</span>
                            )}
                          </div>
                          <span className="text-xs text-text">ID: {client.id}</span>
                        </button>
                      ))
                    )}
                  </div>
                </div>
              )}
            </div>
            {/* Right side - Quick actions */}
            <div className="flex items-center gap-2">
              <Button 
                variant="ghost" 
                size="icon"
                className="h-8 w-8 hidden md:flex items-center justify-center text-slate-500 hover:text-slate-700"
              >
                <Command className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon" className="relative h-8 w-8">
                <Bell className="h-4 w-4 text-slate-500" />
                {userProfile?.notifications_count > 0 && (
                  <span className="absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full"></span>
                )}
              </Button>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <section className="flex-1 overflow-auto">
          {children}
        </section>
      </main>
    </div>
  )
}