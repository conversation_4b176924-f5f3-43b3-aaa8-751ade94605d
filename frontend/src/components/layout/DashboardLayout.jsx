import { useState, useEffect } from 'react'
import { NavLink, Outlet, useNavigate, useLocation } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext.jsx'
import { Button } from '../ui/button.jsx'
import { fetchWithAuth, googleAdsApi } from '../../lib/api.js'
import { CommandPalette } from '../ui/CommandPalette.jsx'
import {
  BarChart3,
  Search,
  Target,
  Zap,
  Settings,
  LogOut,
  User,
  Bell,
  Building,
  Menu,
  X,
  ChevronRight,
  ChevronDown,
  Users,
  Command,
  DollarSign,
  ExternalLink,
  PlusCircle,
  FileText,
  TrendingUp,
  AlertCircle,
  Lightbulb,
  Brain,
  Star,
  Edit3,
  Ban,
  BarChart,
  Maximize2,
  Link,
  Code,
  BookOpen,
  MessageSquare
} from 'lucide-react'

export const DashboardLayout = () => {
  const { user, signOut } = useAuth()
  const [userProfile, setUserProfile] = useState(null)
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [sidebarCollapsed, setSidebarCollapsed] = useState(() => {
    // Initialize from localStorage with proper error handling
    try {
      return localStorage.getItem('sidebarCollapsed') === 'true'
    } catch {
      return false
    }
  })
  const [clientAccounts, setClientAccounts] = useState([])
  const [loadingAccounts, setLoadingAccounts] = useState(true)

  // Helper function to toggle sidebar with proper state management
  const toggleSidebar = (collapsed) => {
    setSidebarCollapsed(collapsed)
    try {
      localStorage.setItem('sidebarCollapsed', collapsed.toString())
    } catch (error) {
      console.warn('Failed to save sidebar state:', error)
    }
  }

  useEffect(() => {
    const fetchUserProfile = async () => {
      try {
        const response = await fetchWithAuth('/api/user/profile')
        if (response.ok) {
          const data = await response.json()
          setUserProfile(data)
        }
      } catch (error) {
        // Failed to fetch user profile
      }
    }
    
    if (user) {
      fetchUserProfile()
    }
  }, [user])

  // Fetch Google Ads accounts
  useEffect(() => {
    const fetchAccounts = async () => {
      try {
        setLoadingAccounts(true)
        const accounts = await googleAdsApi.getAccounts()
        
        // Flatten the account structure for the dropdown
        const allAccounts = []
        accounts.forEach(account => {
          if (account.type === 'MCC' && account.sub_accounts) {
            // Add sub-accounts from MCC
            account.sub_accounts.forEach(subAccount => {
              allAccounts.push({
                id: subAccount.customer_id,
                name: subAccount.descriptive_name,
                active: true,
                isTestAccount: subAccount.test_account || true
              })
            })
          } else if (account.type === 'STANDARD') {
            // Add standard accounts
            allAccounts.push({
              id: account.customer_id,
              name: account.descriptive_name,
              active: true,
              isTestAccount: account.test_account || false
            })
          }
        })
        
        setClientAccounts(allAccounts)
      } catch (error) {
        // Failed to fetch Google Ads accounts
        // Fallback to empty array if not connected
        setClientAccounts([])
      } finally {
        setLoadingAccounts(false)
      }
    }

    if (userProfile?.google_ads_connected) {
      fetchAccounts()
    } else {
      setLoadingAccounts(false)
    }
  }, [userProfile?.google_ads_connected])

  const navigate = useNavigate()
  const location = useLocation()

  // Core Campaign Management (50%)
  const coreNavigationItems = [
    { icon: BarChart3, label: 'Multi-Client Dashboard', to: '/dashboard' },
    { icon: PlusCircle, label: 'Campaign Creation', to: '/campaigns' },
    { icon: DollarSign, label: 'Bid & Budget Manager', to: '/bid-budget' },
    { icon: FileText, label: 'Performance Reports', to: '/reports' },
    { icon: AlertCircle, label: 'Monitoring & Alerts', to: '/monitoring' },
    { icon: Target, label: 'Keywords & Audiences', to: '/keywords' },
  ]

  // AI-Powered Assistance (50%)
  const aiNavigationItems = [
    { icon: Search, label: 'Search Query Mining', to: '/ai/search-mining', badge: 'AI' },
    { icon: Brain, label: 'Intent Classifier', to: '/ai/intent-classifier', badge: 'AI' },
    { icon: Edit3, label: 'Ad Copy Laboratory', to: '/ai/ad-copy-lab', badge: 'AI' },
    { icon: Ban, label: 'Negative Keyword AI', to: '/ai/negative-keywords', badge: 'AI' },
    { icon: TrendingUp, label: 'Bid Intelligence', to: '/ai/bid-intelligence', badge: 'AI' },
    { icon: Maximize2, label: 'Ad Extensions Max', to: '/ai/ad-extensions', badge: 'AI' },
    { icon: Link, label: 'Landing Page Synergy', to: '/ai/landing-page-synergy', badge: 'AI' },
    { icon: Zap, label: 'Search Automation', to: '/ai/search-automation', badge: 'AI' },
    { icon: Code, label: 'Scripts Library', to: '/ai/scripts-library', badge: 'AI' },
    { icon: MessageSquare, label: 'AI Insights Engine', to: '/ai/insights-engine', badge: 'AI' },
  ]

  const settingsItems = [
    { icon: Settings, label: 'Team Settings', to: '/team-settings' },
  ]

  const currentPath = location.pathname

  const [selectedClient, setSelectedClient] = useState(null)
  const [showClientDropdown, setShowClientDropdown] = useState(false)

  // Connect Google Ads OAuth flow
  const connectGoogleAds = async () => {
    try {
      setShowClientDropdown(false) // Close dropdown
      const data = await googleAdsApi.getAuthUrl()
      // Redirect to Google OAuth
      window.location.href = data.auth_url
    } catch (error) {
      // Failed to start OAuth
      alert('Failed to connect Google Ads. Please try again.')
    }
  }

  // Keyboard shortcuts and focus management
  useEffect(() => {
    const handleKeyDown = (event) => {
      // Ctrl/Cmd + B to toggle sidebar
      if ((event.ctrlKey || event.metaKey) && event.key === 'b') {
        event.preventDefault()
        toggleSidebar(!sidebarCollapsed)
      }

      // Escape to close mobile sidebar
      if (event.key === 'Escape' && sidebarOpen) {
        setSidebarOpen(false)
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [sidebarCollapsed, sidebarOpen, toggleSidebar])

  // Close mobile sidebar when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (sidebarOpen && !event.target.closest('aside') && !event.target.closest('[data-mobile-menu-trigger]')) {
        setSidebarOpen(false)
      }
    }

    if (sidebarOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [sidebarOpen])

  return (
    <div className="min-h-screen bg-slate-50">
      {/* Command Palette */}
      <CommandPalette />

      {/* Sidebar Overlay for Mobile */}
      {sidebarOpen && (
        <div
          className="lg:hidden fixed inset-0 bg-black/50 z-30 animate-in fade-in duration-200"
          onClick={() => setSidebarOpen(false)}
          aria-hidden="true"
        />
      )}

      {/* Sidebar */}
      <aside
        className={`
          modern-sidebar fixed inset-y-0 left-0 z-40 bg-white border-r border-slate-200
          transition-all duration-300 ease-in-out
          ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
          ${sidebarCollapsed ? 'lg:w-16 sidebar-collapsed' : 'lg:w-64'}
          lg:translate-x-0 w-64
        `}
        aria-label="Main navigation"
      >
        {/* Header Section */}
        <header className="sidebar-logo">
          {/* Logo */}
          <div className="flex items-center gap-3 min-w-0 flex-1">
            <div className="sidebar-logo-icon">
              <img 
                src="/assets/logos/Brand Wisdom Icon.png" 
                alt="Brand Wisdom Solutions" 
                className="w-full h-full object-cover"
              />
            </div>
            <div className={`
              sidebar-logo-text transition-all duration-300 overflow-hidden
              ${sidebarCollapsed ? 'lg:w-0 lg:opacity-0' : 'lg:w-auto lg:opacity-100'}
            `}>
              <h1 className="font-playfair">Brand Wisdom</h1>
              <p>AdsAI Platform</p>
            </div>
          </div>

          {/* Toggle Button */}
          <button
            onClick={() => toggleSidebar(!sidebarCollapsed)}
            className={`
              sidebar-toggle
              ${sidebarCollapsed ? 'lg:hidden' : 'hidden lg:flex'}
            `}
            aria-label={sidebarCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
          >
            <ChevronRight className={`
              sidebar-toggle-icon h-4 w-4
            `} />
          </button>

          {/* Floating Expand Button (Collapsed State) */}
          {sidebarCollapsed && (
            <button
              onClick={() => toggleSidebar(false)}
              className="sidebar-expand-float"
              aria-label="Expand sidebar"
            >
              <ChevronRight className="h-3 w-3" />
            </button>
          )}
        </header>

        {/* Quick Stats */}
        <section className="sidebar-stats">
          <div className="space-y-3">
            {/* Active Clients */}
            <div className="sidebar-stat-item group">
              <div className="sidebar-stat-label">
                <Users className="h-4 w-4 flex-shrink-0" />
                <span className={`${sidebarCollapsed ? 'lg:hidden' : ''}`}>
                  Active Clients
                </span>
              </div>
              <span className="sidebar-stat-value">24</span>

              {/* Tooltip */}
              {sidebarCollapsed && (
                <div className="sidebar-tooltip">
                  Active Clients: 24
                </div>
              )}
            </div>

            {/* Monthly Spend */}
            <div className="sidebar-stat-item group">
              <div className="sidebar-stat-label">
                <DollarSign className="h-4 w-4 flex-shrink-0" />
                <span className={`${sidebarCollapsed ? 'lg:hidden' : ''}`}>
                  Monthly Spend
                </span>
              </div>
              <span className="sidebar-stat-value success">₹40.4L</span>

              {/* Tooltip */}
              {sidebarCollapsed && (
                <div className="sidebar-tooltip">
                  Monthly Spend: ₹40.4L
                </div>
              )}
            </div>
          </div>
        </section>

        {/* Navigation */}
        <nav className="sidebar-nav-wrapper" role="navigation">
          <ul className="space-y-1" role="list">
            {/* Core Campaign Management */}
            <li className={`pb-2 ${sidebarCollapsed ? 'lg:hidden' : ''}`}>
              <div className="sidebar-section-header">
                Core Campaign Management
              </div>
            </li>
            {coreNavigationItems.map((item) => {
              return (
                <li key={item.to} role="none">
                  <NavLink
                    to={item.to}
                    onClick={() => setSidebarOpen(false)}
                    className={({ isActive }) => `
                      sidebar-nav-item
                      ${isActive ? 'active' : ''}
                    `}
                  >
                    <item.icon className="sidebar-nav-icon" />

                    <span className={`${sidebarCollapsed ? 'lg:hidden' : ''}`}>
                      {item.label}
                    </span>

                    {/* Tooltip for collapsed sidebar */}
                    {sidebarCollapsed && (
                      <div className="sidebar-tooltip">
                        {item.label}
                      </div>
                    )}
                  </NavLink>
                </li>
              )
            })}

            {/* Divider */}
            <li className="pt-4">
              <div className="flex items-center">
                <div className="flex-1 border-t border-slate-200"></div>
                {!sidebarCollapsed && (
                  <>
                    <span className="px-3 text-xs text-slate-500 bg-slate-50">AI Assistance</span>
                    <div className="flex-1 border-t border-slate-200"></div>
                  </>
                )}
              </div>
            </li>

            {/* AI-Powered Assistance */}
            {aiNavigationItems.map((item) => {
              return (
                <li key={item.to} role="none">
                  <NavLink
                    to={item.to}
                    onClick={() => setSidebarOpen(false)}
                    className={({ isActive }) => `
                      sidebar-nav-item
                      ${isActive ? 'active' : ''}
                    `}
                  >
                    <item.icon className="sidebar-nav-icon" />

                    <span className={`${sidebarCollapsed ? 'lg:hidden' : ''}`}>
                      {item.label}
                    </span>
                    {item.badge && !sidebarCollapsed && (
                      <span className="sidebar-ai-badge">
                        {item.badge}
                      </span>
                    )}

                    {/* Tooltip for collapsed sidebar */}
                    {sidebarCollapsed && (
                      <div className="sidebar-tooltip">
                        {item.label}
                      </div>
                    )}
                  </NavLink>
                </li>
              )
            })}

            {/* Settings Section Divider */}
            <li className="pt-4">
              <div className="flex items-center">
                <div className="flex-1 border-t border-slate-200"></div>
              </div>
            </li>

            {/* Settings */}
            {settingsItems.map((item) => {
              return (
                <li key={item.to} role="none">
                  <NavLink
                    to={item.to}
                    onClick={() => setSidebarOpen(false)}
                    className={({ isActive }) => `
                      sidebar-nav-item
                      ${isActive ? 'active' : ''}
                    `}
                  >
                    <item.icon className="sidebar-nav-icon" />

                    <span className={`${sidebarCollapsed ? 'lg:hidden' : ''}`}>
                      {item.label}
                    </span>

                    {/* Tooltip for collapsed sidebar */}
                    {sidebarCollapsed && (
                      <div className="sidebar-tooltip">
                        {item.label}
                      </div>
                    )}
                  </NavLink>
                </li>
              )
            })}
          </ul>
        </nav>

        {/* User & Settings - Icon Only Layout */}
        <footer className="sidebar-profile">
          <div className="flex items-center justify-center gap-2 px-2">
            {/* User Profile */}
            <button className="sidebar-icon-button group relative">
              <User className="sidebar-icon-button-icon" />
              
              {/* Tooltip */}
              <div className="sidebar-tooltip">
                {userProfile?.full_name || user?.email?.split('@')[0]}
              </div>
            </button>

            {/* Settings Button */}
            <button
              onClick={() => window.location.hash = 'settings'}
              className="sidebar-icon-button group relative"
            >
              <Settings className="sidebar-icon-button-icon" />
              
              {/* Tooltip */}
              <div className="sidebar-tooltip">
                Settings
              </div>
            </button>

            {/* Sign Out */}
            <button
              onClick={signOut}
              className="sidebar-icon-button group relative hover:bg-red-50"
            >
              <LogOut className="sidebar-icon-button-icon group-hover:text-red-600" />
              
              {/* Tooltip */}
              <div className="sidebar-tooltip">
                Sign Out
              </div>
            </button>
          </div>
        </footer>
      </aside>

      {/* Main Content */}
      <main className={`
        min-h-screen transition-all duration-300 ease-in-out
        ${sidebarCollapsed ? 'lg:pl-16' : 'lg:pl-64'}
      `}>
        {/* Top Bar */}
        <header className="bg-white border-b border-slate-200 sticky top-0 z-30 shadow-sm">
          <div className="flex items-center justify-between h-16 px-6 lg:px-8">
            {/* Left side - Mobile menu and search */}
            <div className="flex items-center gap-3">
              <div className="lg:hidden">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setSidebarOpen(!sidebarOpen)}
                  className="h-8 w-8"
                  data-mobile-menu-trigger
                  aria-label={sidebarOpen ? 'Close menu' : 'Open menu'}
                >
                  {sidebarOpen ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
                </Button>
              </div>
              {/* Client Selector */}
            <div className="relative">
              <button
                onClick={() => setShowClientDropdown(!showClientDropdown)}
                className={`flex items-center gap-2 px-3 py-1.5 rounded-lg transition-colors text-sm ${
                  !userProfile?.google_ads_connected ? 
                  'bg-primary-100 hover:bg-primary-200 border border-primary-300' : 
                  'bg-slate-100 hover:bg-slate-200'
                }`}
              >
                <Building className="h-4 w-4 text-slate-500" />
                <span className="font-medium text-slate-900">
                  {!userProfile?.google_ads_connected ? 
                    'Connect Account' : 
                    (selectedClient ? clientAccounts.find(c => c.id === selectedClient)?.name : 'All Clients')
                  }
                </span>
                <ChevronDown className="h-4 w-4 text-slate-500" />
              </button>
              
              {showClientDropdown && (
                <div className="absolute top-full left-0 mt-2 w-64 bg-white rounded-md shadow-lg border border-grey-100 z-50">
                  <div className="p-2">
                    {clientAccounts.length > 0 && (
                      <>
                        <button
                          onClick={() => {
                            setSelectedClient(null)
                            setShowClientDropdown(false)
                          }}
                          className="w-full text-left px-3 py-2 rounded hover:bg-grey-100 transition-colors"
                        >
                          <div className="flex items-center justify-between">
                            <span className="font-medium">All Clients</span>
                            <span className="text-xs text-text">View all</span>
                          </div>
                        </button>
                        <div className="border-t border-grey-100 my-2"></div>
                      </>
                    )}
                    {loadingAccounts ? (
                      <div className="px-3 py-3 text-sm text-text">
                        Loading accounts...
                      </div>
                    ) : clientAccounts.length === 0 ? (
                      <div className="px-3 py-3">
                        <p className="text-sm text-text mb-2">No accounts connected</p>
                        <Button 
                          onClick={connectGoogleAds}
                          size="sm"
                          className="w-full h-9 text-xs font-medium bg-primary-600 hover:bg-primary-700"
                        >
                          <ExternalLink className="h-3.5 w-3.5 mr-1.5" />
                          Connect Google Ads Account
                        </Button>
                      </div>
                    ) : (
                      clientAccounts.map(client => (
                        <button
                          key={client.id}
                          onClick={() => {
                            setSelectedClient(client.id)
                            setShowClientDropdown(false)
                          }}
                          className="w-full text-left px-3 py-2 rounded hover:bg-grey-100 transition-colors"
                        >
                          <div className="flex items-center justify-between">
                            <span className="font-medium">{client.name}</span>
                            {client.isTestAccount && (
                              <span className="text-xs bg-gold-500/20 text-gold-700 px-2 py-0.5 rounded">TEST</span>
                            )}
                          </div>
                          <span className="text-xs text-text">ID: {client.id}</span>
                        </button>
                      ))
                    )}
                  </div>
                </div>
              )}
            </div>
              {/* Search */}
              <div className="hidden md:flex items-center gap-2 px-3 py-1.5 bg-slate-50 rounded-lg text-sm w-64">
                <Search className="h-4 w-4 text-slate-400" />
                <input 
                  type="text" 
                  placeholder="Search campaigns, keywords..." 
                  className="bg-transparent outline-none flex-1 text-slate-700 placeholder-slate-400"
                />
              </div>
            </div>
            {/* Right side - Quick actions */}
            <div className="flex items-center gap-2">
              <Button 
                variant="ghost" 
                size="icon"
                className="h-8 w-8 hidden md:flex items-center justify-center text-slate-500 hover:text-slate-700"
              >
                <Command className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon" className="relative h-8 w-8">
                <Bell className="h-4 w-4 text-slate-500" />
                {userProfile?.notifications_count > 0 && (
                  <span className="absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full"></span>
                )}
              </Button>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <section className="flex-1 overflow-auto">
          <Outlet />
        </section>
      </main>
    </div>
  )
}

export default DashboardLayout