import React from 'react'
import { RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, PieChart, Pie, Cell, ResponsiveContainer, Legend, Tooltip } from 'recharts'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card.jsx'

const COLORS = ['#1E429F', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#EC4899', '#6366F1', '#14B8A6']

export const MetricsComparison = ({ data, type = 'radar', title = 'Campaign Comparison' }) => {
  // Generate sample data if none provided
  const chartData = data || generateSampleComparisonData(type)

  const CustomTooltip = ({ active, payload }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 rounded-lg shadow-lg border border-grey-200">
          {payload.map((entry, index) => (
            <p key={index} className="text-body-sm" style={{ color: entry.color || COLORS[index % COLORS.length] }}>
              <span className="font-semibold">{entry.name || entry.dataKey}:</span> {
                typeof entry.value === 'number' ? entry.value.toLocaleString() : entry.value
              }
            </p>
          ))}
        </div>
      )
    }
    return null
  }

  const renderRadarChart = () => (
    <RadarChart data={chartData.metrics}>
      <PolarGrid 
        gridType="polygon" 
        radialLines={true}
        stroke="#E5E7EB"
      />
      <PolarAngleAxis 
        dataKey="metric" 
        tick={{ fontSize: 12 }}
        className="text-grey-700"
      />
      <PolarRadiusAxis 
        domain={[0, 100]}
        tick={{ fontSize: 10 }}
        tickCount={5}
        axisLine={false}
      />
      <Tooltip content={<CustomTooltip />} />
      <Legend 
        verticalAlign="top" 
        height={36}
        wrapperStyle={{ fontSize: '12px' }}
      />
      {chartData.campaigns.map((campaign, index) => (
        <Radar
          key={campaign}
          name={campaign}
          dataKey={campaign}
          stroke={COLORS[index % COLORS.length]}
          fill={COLORS[index % COLORS.length]}
          fillOpacity={0.3}
          strokeWidth={2}
        />
      ))}
    </RadarChart>
  )

  const renderPieChart = () => {
    const pieData = chartData.map((item, index) => ({
      ...item,
      color: COLORS[index % COLORS.length]
    }))

    return (
      <PieChart>
        <Pie
          data={pieData}
          cx="50%"
          cy="50%"
          labelLine={false}
          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
          outerRadius={100}
          fill="#8884d8"
          dataKey="value"
        >
          {pieData.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={entry.color} />
          ))}
        </Pie>
        <Tooltip content={<CustomTooltip />} />
      </PieChart>
    )
  }

  return (
    <Card className="shadow-sm hover:shadow-md transition-shadow duration-200">
      <CardHeader>
        <CardTitle className="text-h4 font-display">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-80 w-full">
          <ResponsiveContainer width="100%" height="100%">
            {type === 'radar' ? renderRadarChart() : renderPieChart()}
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  )
}

function generateSampleComparisonData(type) {
  if (type === 'radar') {
    return {
      campaigns: ['Campaign A', 'Campaign B', 'Campaign C'],
      metrics: [
        { metric: 'CTR', 'Campaign A': 85, 'Campaign B': 72, 'Campaign C': 90 },
        { metric: 'Quality Score', 'Campaign A': 78, 'Campaign B': 88, 'Campaign C': 65 },
        { metric: 'Conversion Rate', 'Campaign A': 92, 'Campaign B': 67, 'Campaign C': 80 },
        { metric: 'ROI', 'Campaign A': 70, 'Campaign B': 85, 'Campaign C': 75 },
        { metric: 'Engagement', 'Campaign A': 88, 'Campaign B': 70, 'Campaign C': 82 }
      ]
    }
  } else {
    return [
      { name: 'Search Ads', value: 4500 },
      { name: 'Display Ads', value: 3200 },
      { name: 'Video Ads', value: 2800 },
      { name: 'Shopping Ads', value: 2100 },
      { name: 'App Ads', value: 1400 }
    ]
  }
}