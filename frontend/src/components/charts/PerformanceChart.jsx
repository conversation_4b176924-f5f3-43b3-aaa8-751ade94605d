import React from 'react'
import { LineChart, Line, AreaChart, Area, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card.jsx'

const CustomTooltip = ({ active, payload, label }) => {
  if (active && payload && payload.length) {
    return (
      <div className="chart-tooltip">
        <p className="tooltip-title">{label}</p>
        {payload.map((entry, index) => (
          <p key={index} className="text-sm flex items-center justify-between gap-3" style={{ color: entry.color }}>
            <span className="font-medium">{entry.name}:</span>
            <span className="font-bold">{
              entry.name.includes('Spend') || entry.name.includes('Cost') 
                ? `$${entry.value.toLocaleString()}` 
                : entry.value.toLocaleString()
            }</span>
          </p>
        ))}
      </div>
    )
  }
  return null
}

export const PerformanceChart = ({ data, type = 'line', metrics = ['impressions', 'clicks'], title = 'Performance Overview' }) => {
  // Generate sample data if none provided
  const chartData = data || generateSampleData()
  
  const metricConfig = {
    impressions: { color: '#3B82F6', name: 'Impressions' },
    clicks: { color: '#10B981', name: 'Clicks' },
    conversions: { color: '#F59E0B', name: 'Conversions' },
    spend: { color: '#EF4444', name: 'Ad Spend' },
    ctr: { color: '#8B5CF6', name: 'CTR %' },
    cpc: { color: '#EC4899', name: 'Avg. CPC' }
  }

  const renderChart = () => {
    switch (type) {
      case 'area':
        return (
          <AreaChart data={chartData}>
            <defs>
              {metrics.map((metric) => (
                <linearGradient key={metric} id={`gradient-${metric}`} x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor={metricConfig[metric]?.color} stopOpacity={0.3}/>
                  <stop offset="95%" stopColor={metricConfig[metric]?.color} stopOpacity={0}/>
                </linearGradient>
              ))}
            </defs>
            <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
            <XAxis 
              dataKey="date" 
              stroke="#6B7280"
              fontSize={11}
              tickLine={false}
              axisLine={{ stroke: '#E5E7EB' }}
            />
            <YAxis 
              stroke="#6B7280"
              fontSize={11}
              tickLine={false}
              axisLine={{ stroke: '#E5E7EB' }}
              tickFormatter={(value) => value > 1000 ? `${(value / 1000).toFixed(0)}k` : value}
            />
            <Tooltip content={<CustomTooltip />} />
            <Legend 
              verticalAlign="top" 
              height={40}
              iconType="circle"
              wrapperStyle={{ fontSize: '13px', paddingTop: '8px' }}
            />
            {metrics.map((metric) => (
              <Area
                key={metric}
                type="monotone"
                dataKey={metric}
                name={metricConfig[metric]?.name}
                stroke={metricConfig[metric]?.color}
                fill={`url(#gradient-${metric})`}
                strokeWidth={2}
              />
            ))}
          </AreaChart>
        )
      
      case 'bar':
        return (
          <BarChart data={chartData}>
            <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
            <XAxis 
              dataKey="date" 
              stroke="#6B7280"
              fontSize={11}
              tickLine={false}
              axisLine={{ stroke: '#E5E7EB' }}
            />
            <YAxis 
              stroke="#6B7280"
              fontSize={11}
              tickLine={false}
              axisLine={{ stroke: '#E5E7EB' }}
              tickFormatter={(value) => value > 1000 ? `${(value / 1000).toFixed(0)}k` : value}
            />
            <Tooltip content={<CustomTooltip />} />
            <Legend 
              verticalAlign="top" 
              height={40}
              iconType="rect"
              wrapperStyle={{ fontSize: '13px', paddingTop: '8px' }}
            />
            {metrics.map((metric, index) => (
              <Bar
                key={metric}
                dataKey={metric}
                name={metricConfig[metric]?.name}
                fill={metricConfig[metric]?.color}
                radius={[4, 4, 0, 0]}
              />
            ))}
          </BarChart>
        )
      
      default: // line
        return (
          <LineChart data={chartData}>
            <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
            <XAxis 
              dataKey="date" 
              stroke="#6B7280"
              fontSize={11}
              tickLine={false}
              axisLine={{ stroke: '#E5E7EB' }}
            />
            <YAxis 
              stroke="#6B7280"
              fontSize={11}
              tickLine={false}
              axisLine={{ stroke: '#E5E7EB' }}
              tickFormatter={(value) => value > 1000 ? `${(value / 1000).toFixed(0)}k` : value}
            />
            <Tooltip content={<CustomTooltip />} />
            <Legend 
              verticalAlign="top" 
              height={40}
              iconType="line"
              wrapperStyle={{ fontSize: '13px', paddingTop: '8px' }}
            />
            {metrics.map((metric) => (
              <Line
                key={metric}
                type="monotone"
                dataKey={metric}
                name={metricConfig[metric]?.name}
                stroke={metricConfig[metric]?.color}
                strokeWidth={2}
                dot={false}
                activeDot={{ r: 5 }}
              />
            ))}
          </LineChart>
        )
    }
  }

  return (
    <Card className="dashboard-card">
      <CardHeader className="p-4 pb-3">
        <CardTitle className="card-title">{title}</CardTitle>
      </CardHeader>
      <CardContent className="p-4 pt-0">
        <div className="h-56 w-full">
          <ResponsiveContainer width="100%" height="100%">
            {renderChart()}
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  )
}

// Generate sample data for demonstration
function generateSampleData() {
  const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
  return days.map((day, index) => ({
    date: day,
    impressions: Math.floor(Math.random() * 50000) + 20000,
    clicks: Math.floor(Math.random() * 2000) + 500,
    conversions: Math.floor(Math.random() * 100) + 20,
    spend: Math.floor(Math.random() * 1000) + 500,
    ctr: (Math.random() * 5 + 1).toFixed(2),
    cpc: (Math.random() * 2 + 0.5).toFixed(2)
  }))
}