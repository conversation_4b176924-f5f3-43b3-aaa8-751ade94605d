import React from 'react'
import { Line<PERSON><PERSON>, Line, ResponsiveContainer } from 'recharts'

export const Sparkline = ({ data, color = '#10B981', height = 40, showTrend = true }) => {
  // Generate sample data if none provided
  const chartData = data || Array.from({ length: 7 }, (_, i) => ({
    value: Math.floor(Math.random() * 100) + 20
  }))

  const trend = showTrend ? calculateTrend(chartData) : 0

  return (
    <div className="flex items-center gap-2">
      <div className="flex-1" style={{ height }}>
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={chartData} margin={{ top: 5, right: 5, bottom: 5, left: 5 }}>
            <Line
              type="monotone"
              dataKey="value"
              stroke={color}
              strokeWidth={2}
              dot={false}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
      {showTrend && (
        <div className={`text-caption font-semibold ${trend >= 0 ? 'text-success' : 'text-error'}`}>
          {trend >= 0 ? '+' : ''}{trend.toFixed(1)}%
        </div>
      )}
    </div>
  )
}

function calculateTrend(data) {
  if (!data || data.length < 2) return 0
  const first = data[0].value
  const last = data[data.length - 1].value
  return ((last - first) / first) * 100
}