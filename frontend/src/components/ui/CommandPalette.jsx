import React, { useState, useEffect, useRef } from 'react'
import { 
  Search, 
  Command, 
  FileText, 
  BarChart3, 
  Users, 
  Settings,
  Zap,
  DollarSign,
  Target,
  Shield,
  Plus,
  Upload,
  Download,
  RefreshCw
} from 'lucide-react'
import { Card } from './card.jsx'

const commands = [
  // Quick Actions
  { 
    id: 'new-campaign', 
    label: 'Create New Campaign', 
    icon: Plus, 
    category: 'Quick Actions',
    shortcut: 'C',
    action: () => window.location.hash = 'campaigns/new'
  },
  { 
    id: 'analyze-search', 
    label: 'Analyze Search Terms', 
    icon: Search, 
    category: 'Quick Actions',
    shortcut: 'S',
    action: () => window.location.hash = 'search-terms'
  },
  { 
    id: 'generate-ad', 
    label: 'Generate Ad Copy', 
    icon: Zap, 
    category: 'Quick Actions',
    shortcut: 'A',
    action: () => window.location.hash = 'ad-generator'
  },
  
  // Navigation
  { 
    id: 'dashboard', 
    label: 'Go to Dashboard', 
    icon: BarChart3, 
    category: 'Navigation',
    action: () => window.location.hash = 'dashboard'
  },
  { 
    id: 'clients', 
    label: 'View All Clients', 
    icon: Users, 
    category: 'Navigation',
    action: () => window.location.hash = 'clients'
  },
  { 
    id: 'campaigns', 
    label: 'View All Campaigns', 
    icon: Target, 
    category: 'Navigation',
    action: () => window.location.hash = 'campaigns'
  },
  
  // Tools
  { 
    id: 'bulk-optimize', 
    label: 'Bulk Optimization', 
    icon: Zap, 
    category: 'Tools',
    action: () => window.location.hash = 'bulk-optimize'
  },
  { 
    id: 'negative-keywords', 
    label: 'Negative Keywords Manager', 
    icon: Shield, 
    category: 'Tools',
    action: () => window.location.hash = 'negative-keywords'
  },
  { 
    id: 'quality-scores', 
    label: 'Review Quality Scores', 
    icon: Target, 
    category: 'Tools',
    action: () => window.location.hash = 'quality-scores'
  },
  
  // Reports
  { 
    id: 'export-report', 
    label: 'Export Performance Report', 
    icon: Download, 
    category: 'Reports',
    shortcut: 'E',
    action: () => console.log('Export report')
  },
  { 
    id: 'import-data', 
    label: 'Import Campaign Data', 
    icon: Upload, 
    category: 'Reports',
    action: () => console.log('Import data')
  },
  
  // System
  { 
    id: 'refresh', 
    label: 'Refresh Dashboard', 
    icon: RefreshCw, 
    category: 'System',
    shortcut: 'R',
    action: () => window.location.reload()
  },
  { 
    id: 'settings', 
    label: 'Settings', 
    icon: Settings, 
    category: 'System',
    action: () => window.location.hash = 'settings'
  }
]

export const CommandPalette = () => {
  const [isOpen, setIsOpen] = useState(false)
  const [search, setSearch] = useState('')
  const [selectedIndex, setSelectedIndex] = useState(0)
  const inputRef = useRef(null)

  // Filter commands based on search
  const filteredCommands = commands.filter(cmd => 
    cmd.label.toLowerCase().includes(search.toLowerCase()) ||
    cmd.category.toLowerCase().includes(search.toLowerCase())
  )

  // Group commands by category
  const groupedCommands = filteredCommands.reduce((acc, cmd) => {
    if (!acc[cmd.category]) acc[cmd.category] = []
    acc[cmd.category].push(cmd)
    return acc
  }, {})

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e) => {
      // Open with Cmd/Ctrl + K
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault()
        setIsOpen(true)
      }
      
      // Close with Escape
      if (e.key === 'Escape' && isOpen) {
        setIsOpen(false)
      }
      
      // Navigate with arrow keys
      if (isOpen) {
        if (e.key === 'ArrowDown') {
          e.preventDefault()
          setSelectedIndex(prev => (prev + 1) % filteredCommands.length)
        } else if (e.key === 'ArrowUp') {
          e.preventDefault()
          setSelectedIndex(prev => (prev - 1 + filteredCommands.length) % filteredCommands.length)
        } else if (e.key === 'Enter' && filteredCommands[selectedIndex]) {
          e.preventDefault()
          filteredCommands[selectedIndex].action()
          setIsOpen(false)
        }
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [isOpen, selectedIndex, filteredCommands])

  // Focus input when opened
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus()
      setSearch('')
      setSelectedIndex(0)
    }
  }, [isOpen])

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div 
        className="fixed inset-0 bg-black/50 backdrop-blur-sm"
        onClick={() => setIsOpen(false)}
      />
      
      <div className="relative min-h-screen flex items-start justify-center pt-20">
        <Card className="relative w-full max-w-2xl overflow-hidden shadow-2xl">
          {/* Search Input */}
          <div className="flex items-center gap-3 p-4 border-b border-grey-100">
            <Search className="h-5 w-5 text-grey-400" />
            <input
              ref={inputRef}
              type="text"
              value={search}
              onChange={(e) => {
                setSearch(e.target.value)
                setSelectedIndex(0)
              }}
              placeholder="Type a command or search..."
              className="flex-1 text-body-md text-primary-900 placeholder-grey-400 outline-none bg-transparent"
            />
            <kbd className="px-2 py-1 text-xs font-semibold text-grey-500 bg-grey-100 rounded">
              ESC
            </kbd>
          </div>

          {/* Commands List */}
          <div className="max-h-96 overflow-y-auto">
            {filteredCommands.length === 0 ? (
              <div className="p-8 text-center">
                <p className="text-body-md text-text">No commands found for "{search}"</p>
              </div>
            ) : (
              Object.entries(groupedCommands).map(([category, categoryCommands]) => (
                <div key={category}>
                  <div className="px-4 py-2 bg-grey-100/50">
                    <p className="text-caption font-semibold text-grey-600 uppercase tracking-wide">
                      {category}
                    </p>
                  </div>
                  <div className="py-2">
                    {categoryCommands.map((cmd, idx) => {
                      const globalIndex = filteredCommands.indexOf(cmd)
                      const isSelected = globalIndex === selectedIndex
                      
                      return (
                        <button
                          key={cmd.id}
                          onClick={() => {
                            cmd.action()
                            setIsOpen(false)
                          }}
                          onMouseEnter={() => setSelectedIndex(globalIndex)}
                          className={`w-full flex items-center gap-3 px-4 py-3 text-left transition-colors ${
                            isSelected 
                              ? 'bg-primary-50 text-primary-900' 
                              : 'hover:bg-grey-100/50'
                          }`}
                        >
                          <div className={`p-2 rounded-lg ${
                            isSelected ? 'bg-primary-100' : 'bg-grey-100'
                          }`}>
                            <cmd.icon className={`h-4 w-4 ${
                              isSelected ? 'text-primary-600' : 'text-grey-500'
                            }`} />
                          </div>
                          <span className="flex-1 text-body-md font-medium">
                            {cmd.label}
                          </span>
                          {cmd.shortcut && (
                            <kbd className={`px-2 py-1 text-xs font-semibold rounded ${
                              isSelected 
                                ? 'bg-primary-100 text-primary-700' 
                                : 'bg-grey-100 text-grey-500'
                            }`}>
                              ⌘{cmd.shortcut}
                            </kbd>
                          )}
                        </button>
                      )
                    })}
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Footer */}
          <div className="p-3 border-t border-grey-100 bg-grey-50">
            <div className="flex items-center justify-between text-caption text-grey-500">
              <div className="flex items-center gap-4">
                <span className="flex items-center gap-1">
                  <kbd className="px-1.5 py-0.5 text-xs bg-white rounded border border-grey-200">↑</kbd>
                  <kbd className="px-1.5 py-0.5 text-xs bg-white rounded border border-grey-200">↓</kbd>
                  Navigate
                </span>
                <span className="flex items-center gap-1">
                  <kbd className="px-1.5 py-0.5 text-xs bg-white rounded border border-grey-200">↵</kbd>
                  Select
                </span>
              </div>
              <span className="flex items-center gap-1">
                <Command className="h-3 w-3" />
                Press <kbd className="px-1.5 py-0.5 text-xs bg-white rounded border border-grey-200">⌘K</kbd> anytime
              </span>
            </div>
          </div>
        </Card>
      </div>
    </div>
  )
}