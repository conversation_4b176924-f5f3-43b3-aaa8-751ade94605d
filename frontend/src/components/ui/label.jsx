import React from 'react'

// Simple label component
export const Label = React.forwardRef(({ className = '', ...props }, ref) => {
  // Base label styles
  const baseStyles = 'text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
  
  return (
    <label
      ref={ref}
      className={`${baseStyles} ${className}`.trim()}
      {...props}
    />
  )
})

Label.displayName = 'Label'