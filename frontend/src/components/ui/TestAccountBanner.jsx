import React from 'react'
import { <PERSON><PERSON> } from './alert.jsx'
import { AlertCircle, ExternalLink } from 'lucide-react'

export const TestAccountBanner = ({ show = true }) => {
  if (!show) return null
  
  return (
    <Alert className="mb-6 bg-gold-500/10 border-gold-500">
      <div className="flex items-start gap-3">
        <AlertCircle className="h-5 w-5 text-gold-700 mt-0.5" />
        <div className="flex-1">
          <h3 className="font-semibold text-gold-700 mb-2">Test Account Access Level</h3>
          <p className="text-sm text-gold-600 mb-3">
            Your developer token currently has "Test Account Access" level, which means:
          </p>
          <ul className="text-sm text-gold-600 space-y-1 mb-3">
            <li>• Cannot create real campaigns or ads</li>
            <li>• Cannot access performance metrics</li>
            <li>• Limited API functionality (GRPC errors)</li>
            <li>• Cannot serve ads to real users</li>
          </ul>
          <div className="flex items-center gap-4 pt-2">
            <a 
              href="https://ads.google.com/aw/apicenter" 
              target="_blank" 
              rel="noopener noreferrer"
              className="inline-flex items-center gap-2 text-sm font-medium text-primary-600 hover:text-primary-700"
            >
              Apply for Basic Access
              <ExternalLink className="h-3 w-3" />
            </a>
            <a 
              href="https://developers.google.com/google-ads/api/docs/access-levels" 
              target="_blank" 
              rel="noopener noreferrer"
              className="inline-flex items-center gap-2 text-sm font-medium text-primary-600 hover:text-primary-700"
            >
              Learn More
              <ExternalLink className="h-3 w-3" />
            </a>
          </div>
        </div>
      </div>
    </Alert>
  )
}