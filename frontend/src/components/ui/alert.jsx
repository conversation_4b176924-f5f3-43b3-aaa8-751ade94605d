import React from 'react'

// Simple alert components
export const Alert = React.forwardRef(({ className = '', variant = 'default', ...props }, ref) => {
  // Base alert styles with Brand Wisdom design
  const baseStyles = 'relative w-full rounded-md border p-4'
  
  // Variant styles
  const variantStyles = {
    default: 'bg-grey-100 border-grey-100 text-primary-900',
    destructive: 'bg-error/5 border-error text-error',
    success: 'bg-success/5 border-success text-success',
    warning: 'bg-gold-500/10 border-gold-500 text-primary-900',
  }
  
  return (
    <div
      ref={ref}
      role="alert"
      className={`${baseStyles} ${variantStyles[variant] || variantStyles.default} ${className}`.trim()}
      {...props}
    />
  )
})
Alert.displayName = 'Alert'

export const AlertTitle = React.forwardRef(({ className = '', ...props }, ref) => (
  <h5
    ref={ref}
    className={`mb-1 font-medium leading-none tracking-tight ${className}`.trim()}
    {...props}
  />
))
AlertTitle.displayName = 'AlertTitle'

export const AlertDescription = React.forwardRef(({ className = '', ...props }, ref) => (
  <div
    ref={ref}
    className={`text-sm [&_p]:leading-relaxed ${className}`.trim()}
    {...props}
  />
))
AlertDescription.displayName = 'AlertDescription'