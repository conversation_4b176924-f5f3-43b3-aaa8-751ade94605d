import React from 'react'
import { Loader2 } from 'lucide-react'

export const LoadingSpinner = ({ size = 'default', text = 'Loading...' }) => {
  const sizeClasses = {
    small: 'h-4 w-4',
    default: 'h-8 w-8',
    large: 'h-12 w-12'
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-[200px]">
      <Loader2 className={`${sizeClasses[size]} animate-spin text-primary-600 mb-4`} />
      {text && <p className="text-sm text-gray-600">{text}</p>}
    </div>
  )
}

export const FullPageLoader = () => {
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50">
      <div className="text-center">
        <Loader2 className="h-12 w-12 animate-spin text-primary-600 mx-auto mb-4" />
        <p className="text-lg text-gray-600">Loading AdsAI Platform...</p>
      </div>
    </div>
  )
}

export const RouteLoadingSpinner = () => {
  return (
    <div className="flex items-center justify-center min-h-[calc(100vh-4rem)]">
      <Loader2 className="h-8 w-8 animate-spin text-primary-600" />
    </div>
  )
}