import React from 'react'

export const Skeleton = ({ className = '', ...props }) => {
  return (
    <div
      className={`animate-pulse rounded-md bg-slate-200 ${className}`}
      {...props}
    />
  )
}

export const SkeletonCard = ({ lines = 3 }) => {
  return (
    <div className="bg-white border border-slate-200 rounded-lg p-3 space-y-2">
      <div className="flex items-center justify-between">
        <Skeleton className="h-4 w-24" />
        <Skeleton className="h-4 w-12 rounded" />
      </div>
      <div className="space-y-1.5">
        {Array.from({ length: lines }).map((_, i) => (
          <Skeleton key={i} className={`h-3 ${i === lines - 1 ? 'w-2/3' : 'w-full'}`} />
        ))}
      </div>
    </div>
  )
}

export const SkeletonStats = () => {
  return (
    <div className="bg-white border border-slate-200 rounded-md p-3">
      <div className="flex items-center justify-between mb-1">
        <Skeleton className="h-3 w-20" />
        <Skeleton className="h-3 w-12" />
      </div>
      <Skeleton className="h-6 w-16" />
    </div>
  )
}

export const SkeletonTable = ({ rows = 5, columns = 4 }) => {
  return (
    <div className="bg-white rounded-lg border border-slate-200 shadow-sm p-0 overflow-hidden">
      {/* Header */}
      <div className="border-b border-grey-100 p-4 bg-grey-50">
        <div className="grid grid-cols-4 gap-4">
          {Array.from({ length: columns }).map((_, i) => (
            <Skeleton key={i} className="h-4 w-24" />
          ))}
        </div>
      </div>
      {/* Rows */}
      <div className="divide-y divide-grey-100">
        {Array.from({ length: rows }).map((_, rowIndex) => (
          <div key={rowIndex} className="p-4">
            <div className="grid grid-cols-4 gap-4">
              {Array.from({ length: columns }).map((_, colIndex) => (
                <Skeleton 
                  key={colIndex} 
                  className={`h-4 ${colIndex === 0 ? 'w-32' : 'w-20'}`} 
                />
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export const SkeletonChart = ({ height = 'h-48' }) => {
  return (
    <div className="bg-white border border-slate-200 rounded-lg">
      <div className="p-3 pb-2">
        <Skeleton className="h-4 w-32" />
      </div>
      <div className="p-3 pt-0">
        <div className={`relative ${height} bg-slate-100 rounded overflow-hidden`}>
          <div className="absolute bottom-0 left-0 right-0 flex items-end justify-between px-2 pb-2 gap-1">
            {Array.from({ length: 7 }).map((_, i) => (
              <div 
                key={i} 
                className="flex-1 bg-slate-200 rounded-t animate-pulse"
                style={{ 
                  height: `${Math.random() * 60 + 20}%`,
                  animationDelay: `${i * 100}ms`
                }}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}