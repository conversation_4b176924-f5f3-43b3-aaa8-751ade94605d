import React from 'react'

// Simple input component
export const Input = React.forwardRef(
  ({ className = '', type = 'text', ...props }, ref) => {
    // Base input styles with Brand Wisdom design
    const baseStyles = 'flex h-12 w-full rounded-md border border-grey-100 bg-white px-4 py-3 text-body-md font-primary placeholder:text-grey-400 focus:outline-none focus:border-primary-500 focus:ring-2 focus:ring-primary-500/20 disabled:cursor-not-allowed disabled:opacity-50 disabled:bg-grey-100 transition-all'
    
    return (
      <input
        type={type}
        className={`${baseStyles} ${className}`.trim()}
        ref={ref}
        {...props}
      />
    )
  }
)

Input.displayName = 'Input'