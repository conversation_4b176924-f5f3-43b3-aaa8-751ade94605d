import React, { useState } from 'react'
import { But<PERSON> } from '../ui/button.jsx'
import { Input } from '../ui/input.jsx'
import { Label } from '../ui/label.jsx'
import { Alert } from '../ui/alert.jsx'
import { X, AlertCircle, CheckCircle } from 'lucide-react'
import { fetchWithAuth } from '../../lib/api.js'

export const CreateCampaignModal = ({ isOpen, onClose, accountId, accountName, isTestAccount }) => {
  const [formData, setFormData] = useState({
    name: '',
    budget_amount: '',
    campaign_type: 'SEARCH'
  })
  
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState(null)
  
  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setResult(null)
    
    try {
      const response = await fetchWithAuth(
        `/api/google-ads/accounts/${accountId}/campaigns`,
        {
          method: 'POST',
          body: JSON.stringify({
            ...formData,
            budget_amount: parseFloat(formData.budget_amount)
          })
        }
      )
      
      const data = await response.json()
      setResult(data)
      
      if (data.success) {
        // Reset form on success
        setFormData({
          name: '',
          budget_amount: '',
          campaign_type: 'SEARCH'
        })
        
        // Close modal after 3 seconds if successful
        setTimeout(() => {
          onClose()
          setResult(null)
        }, 3000)
      }
    } catch (error) {
      setResult({
        success: false,
        message: 'Failed to create campaign',
        details: { error: error.message }
      })
    } finally {
      setLoading(false)
    }
  }
  
  if (!isOpen) return null
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg max-w-md w-full p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-h3 font-display text-primary-900">Create Campaign</h2>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
        
        {isTestAccount && (
          <Alert className="mb-4 bg-gold-500/10 border-gold-500">
            <AlertCircle className="h-4 w-4 text-gold-700" />
            <div className="ml-2">
              <p className="font-semibold text-gold-700">Test Account Limitations</p>
              <p className="text-sm text-gold-600 mt-1">
                Test accounts cannot create real campaigns. This will simulate the process.
              </p>
            </div>
          </Alert>
        )}
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="account">Account</Label>
            <Input
              id="account"
              value={`${accountName} (${accountId})`}
              disabled
              className="bg-grey-100"
            />
          </div>
          
          <div>
            <Label htmlFor="name">Campaign Name</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder="e.g., Summer Sale Campaign"
              required
            />
          </div>
          
          <div>
            <Label htmlFor="budget">Daily Budget (USD)</Label>
            <Input
              id="budget"
              type="number"
              step="0.01"
              min="1"
              value={formData.budget_amount}
              onChange={(e) => setFormData({ ...formData, budget_amount: e.target.value })}
              placeholder="50.00"
              required
            />
          </div>
          
          <div>
            <Label htmlFor="type">Campaign Type</Label>
            <select
              id="type"
              value={formData.campaign_type}
              onChange={(e) => setFormData({ ...formData, campaign_type: e.target.value })}
              className="w-full px-3 py-2 border border-grey-200 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="SEARCH">Search</option>
              <option value="DISPLAY">Display</option>
              <option value="SHOPPING">Shopping</option>
              <option value="VIDEO">Video</option>
            </select>
          </div>
          
          {result && (
            <Alert className={result.success ? 'bg-success/10 border-success' : 'bg-error/10 border-error'}>
              <div className="flex items-start gap-2">
                {result.success ? (
                  <CheckCircle className="h-4 w-4 text-success mt-0.5" />
                ) : (
                  <AlertCircle className="h-4 w-4 text-error mt-0.5" />
                )}
                <div className="flex-1">
                  <p className={`font-semibold ${result.success ? 'text-success' : 'text-error'}`}>
                    {result.message}
                  </p>
                  {result.details && (
                    <div className="mt-2 text-sm">
                      {result.details.note && (
                        <p className="text-text">{result.details.note}</p>
                      )}
                      {result.details.solution && (
                        <p className="mt-1 text-primary-600">{result.details.solution}</p>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </Alert>
          )}
          
          <div className="flex gap-3 pt-2">
            <Button
              type="submit"
              disabled={loading}
              className="flex-1"
            >
              {loading ? 'Creating...' : 'Create Campaign'}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={loading}
              className="flex-1"
            >
              Cancel
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}