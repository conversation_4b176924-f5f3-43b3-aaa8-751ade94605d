import { z } from 'zod'

// User validation schemas
export const userSchema = z.object({
  id: z.string().uuid(),
  email: z.string().email(),
  full_name: z.string().optional(),
  role: z.enum(['specialist', 'team_lead', 'admin']).default('specialist')
})

// Auth validation schemas
export const loginSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters')
})

export const signupSchema = loginSchema.extend({
  full_name: z.string().min(2, 'Name must be at least 2 characters'),
  confirmPassword: z.string()
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
})

// Campaign validation schemas
export const campaignSchema = z.object({
  id: z.string(),
  name: z.string().min(3, 'Campaign name must be at least 3 characters'),
  type: z.enum(['SEARCH', 'DISPLAY', 'SHOPPING', 'PERFORMANCE_MAX', 'LOCAL', 'VIDEO', 'DEMAND_GEN']),
  status: z.enum(['ENABLED', 'PAUSED', 'REMOVED']).default('ENABLED'),
  budget: z.number().positive('Budget must be positive'),
  budgetType: z.enum(['DAILY', 'CAMPAIGN_TOTAL']).default('DAILY'),
  startDate: z.string(),
  endDate: z.string().optional()
})

// Google Ads Account validation
export const googleAdsAccountSchema = z.object({
  id: z.string(),
  name: z.string(),
  customerId: z.string().regex(/^\d{3}-\d{3}-\d{4}$/, 'Invalid customer ID format'),
  accountType: z.enum(['MCC', 'STANDARD']),
  canManageClients: z.boolean().default(false),
  isTestAccount: z.boolean().default(false),
  currency: z.string().default('INR')
})

// Dashboard Stats validation
export const dashboardStatsSchema = z.object({
  activeClients: z.number().int().nonnegative(),
  totalCampaigns: z.number().int().nonnegative(),
  monthlySpend: z.number().nonnegative(),
  optimizationsSaved: z.number().nonnegative(),
  totalImpressions: z.number().int().nonnegative(),
  totalClicks: z.number().int().nonnegative(),
  averageCTR: z.number().min(0).max(100),
  averageCPC: z.number().nonnegative()
})

// API Response validation
export const apiResponseSchema = z.object({
  success: z.boolean(),
  data: z.any().optional(),
  error: z.string().optional(),
  message: z.string().optional()
})

// Search Term validation
export const searchTermSchema = z.object({
  id: z.string(),
  term: z.string(),
  impressions: z.number().int().nonnegative(),
  clicks: z.number().int().nonnegative(),
  cost: z.number().nonnegative(),
  conversions: z.number().nonnegative(),
  ctr: z.number().min(0).max(100),
  cpc: z.number().nonnegative(),
  conversionRate: z.number().min(0).max(100),
  qualityScore: z.number().int().min(1).max(10).optional()
})

// Keyword validation
export const keywordSchema = z.object({
  id: z.string(),
  keyword: z.string().min(1),
  matchType: z.enum(['EXACT', 'PHRASE', 'BROAD', 'BROAD_MODIFIER']),
  status: z.enum(['ENABLED', 'PAUSED', 'REMOVED']).default('ENABLED'),
  maxCpc: z.number().positive().optional(),
  qualityScore: z.number().int().min(1).max(10).optional()
})

// Environment variable validation
export const envSchema = z.object({
  VITE_API_URL: z.string().url(),
  VITE_SUPABASE_URL: z.string().url(),
  VITE_SUPABASE_ANON_KEY: z.string().min(1),
  VITE_BRAND_NAME: z.string().default('Brand Wisdom Solutions')
})

// Utility function to validate data
export function validate(schema, data) {
  try {
    return { success: true, data: schema.parse(data) }
  } catch (error) {
    return { success: false, error: error.errors }
  }
}

// Utility function for safe parsing
export function safeParse(schema, data) {
  return schema.safeParse(data)
}