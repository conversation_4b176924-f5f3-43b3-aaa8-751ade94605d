// Global error handler for unhandled errors

// Error types
export const ErrorTypes = {
  NETWORK: 'NETWORK_ERROR',
  AUTH: 'AUTH_ERROR',
  VALIDATION: 'VALIDATION_ERROR',
  API: 'API_ERROR',
  UNKNOWN: 'UNKNOWN_ERROR'
}

// Error class for consistent error handling
export class AppError extends Error {
  constructor(message, type = ErrorTypes.UNKNOWN, details = null) {
    super(message)
    this.name = 'AppError'
    this.type = type
    this.details = details
    this.timestamp = new Date().toISOString()
  }
}

// Global error handler setup
export const setupGlobalErrorHandlers = () => {
  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason)
    
    // Prevent the default browser behavior
    event.preventDefault()
    
    // You could show a toast notification here
    // showErrorToast('An unexpected error occurred. Please try again.')
  })

  // Handle general JavaScript errors
  window.addEventListener('error', (event) => {
    console.error('Global error:', event.error)
    
    // Log to error tracking service in production
    if (import.meta.env.PROD) {
      // Send to error tracking service
    }
  })
}

// Utility function to handle API errors
export const handleApiError = (error) => {
  if (error.response) {
    // Server responded with error
    const status = error.response.status
    const data = error.response.data
    
    switch (status) {
      case 400:
        throw new AppError(
          data.message || 'Invalid request',
          ErrorTypes.VALIDATION,
          data
        )
      case 401:
        throw new AppError(
          'Authentication required',
          ErrorTypes.AUTH,
          data
        )
      case 403:
        throw new AppError(
          'Access denied',
          ErrorTypes.AUTH,
          data
        )
      case 404:
        throw new AppError(
          'Resource not found',
          ErrorTypes.API,
          data
        )
      case 500:
        throw new AppError(
          'Server error. Please try again later.',
          ErrorTypes.API,
          data
        )
      default:
        throw new AppError(
          data.message || 'An error occurred',
          ErrorTypes.API,
          data
        )
    }
  } else if (error.request) {
    // Request made but no response
    throw new AppError(
      'Network error. Please check your connection.',
      ErrorTypes.NETWORK,
      error
    )
  } else {
    // Something else happened
    throw new AppError(
      error.message || 'An unexpected error occurred',
      ErrorTypes.UNKNOWN,
      error
    )
  }
}

// Retry logic for network errors
export const retryWithBackoff = async (fn, maxRetries = 3, delay = 1000) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await fn()
    } catch (error) {
      if (i === maxRetries - 1) throw error
      
      // Exponential backoff
      const waitTime = delay * Math.pow(2, i)
      await new Promise(resolve => setTimeout(resolve, waitTime))
    }
  }
}