// API helper for making authenticated requests
import { supabase } from './supabase.js'

const API_BASE_URL = 'http://localhost:8000'

export async function fetchWithAuth(endpoint, options = {}) {
  // Get the current session
  const { data: { session } } = await supabase.auth.getSession()
  
  if (!session) {
    throw new Error('No active session')
  }

  // Prepare headers
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${session.access_token}`,
    ...options.headers,
  }

  // Make the request
  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    ...options,
    headers,
  })

  // Handle 401 errors
  if (response.status === 401) {
    // Try to refresh the session
    const { data: { session: newSession }, error } = await supabase.auth.refreshSession()
    
    if (error || !newSession) {
      throw new Error('Session expired. Please log in again.')
    }

    // Retry with new token
    headers.Authorization = `Bearer ${newSession.access_token}`
    return fetch(`${API_BASE_URL}${endpoint}`, {
      ...options,
      headers,
    })
  }

  return response
}

// Google Ads specific API calls
export const googleAdsApi = {
  async getAuthUrl() {
    const response = await fetchWithAuth('/api/google-ads/auth/url')
    if (!response.ok) {
      const errorText = await response.text()
      console.error('Failed to get auth URL:', response.status, errorText)
      throw new Error('Failed to get authorization URL')
    }
    return response.json()
  },

  async testConnection(refreshToken) {
    const response = await fetchWithAuth(
      `/api/google-ads/test-connection?refresh_token=${encodeURIComponent(refreshToken)}`
    )
    if (!response.ok) {
      throw new Error('Connection test failed')
    }
    return response.json()
  },

  async getAccounts() {
    const response = await fetchWithAuth('/api/google-ads/accounts')
    
    if (!response.ok) {
      const error = await response.text()
      console.error('Failed to fetch accounts:', error)
      throw new Error(error || 'Failed to fetch accounts')
    }
    return response.json()
  },
  
  async getCampaigns(customerId, refreshToken) {
    const headers = {}
    if (refreshToken && refreshToken !== 'null') {
      headers['X-Google-Ads-Token'] = refreshToken
    }
    
    const response = await fetchWithAuth(
      `/api/google-ads/accounts/${customerId}/campaigns`,
      { headers }
    )
    
    if (!response.ok) {
      throw new Error('Failed to fetch campaigns')
    }
    return response.json()
  }
}