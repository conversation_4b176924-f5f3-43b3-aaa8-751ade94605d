import { envSchema } from './validations.js'

// Validate environment variables
const env = envSchema.safeParse({
  VITE_API_URL: import.meta.env.VITE_API_URL,
  VITE_SUPABASE_URL: import.meta.env.VITE_SUPABASE_URL,
  VITE_SUPABASE_ANON_KEY: import.meta.env.VITE_SUPABASE_ANON_KEY,
  VITE_BRAND_NAME: import.meta.env.VITE_BRAND_NAME
})

if (!env.success) {
  // Log error details in development
  if (import.meta.env.DEV) {
    console.error('❌ Environment validation failed:')
    console.error(env.error.format())
  }
  throw new Error('Invalid environment variables')
}

// Export validated config
export const config = {
  api: {
    url: env.data.VITE_API_URL
  },
  supabase: {
    url: env.data.VITE_SUPABASE_URL,
    anonKey: env.data.VITE_SUPABASE_ANON_KEY
  },
  brand: {
    name: env.data.VITE_BRAND_NAME
  }
}

// Export individual values for convenience
export const API_URL = config.api.url
export const SUPABASE_URL = config.supabase.url
export const SUPABASE_ANON_KEY = config.supabase.anonKey
export const BRAND_NAME = config.brand.name