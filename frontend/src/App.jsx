import React from 'react'
import { RouterProvider } from 'react-router-dom'
import { ErrorBoundary } from 'react-error-boundary'
import { AuthProvider } from './contexts/AuthContext.jsx'
import { router } from './routes/index.jsx'
import { ErrorFallback, logError } from './components/ErrorFallback.jsx'
import './App.css'

function App() {
  return (
    <ErrorBoundary 
      FallbackComponent={ErrorFallback} 
      onError={logError}
      onReset={() => window.location.href = '/'}
    >
      <AuthProvider>
        <RouterProvider router={router} />
      </AuthProvider>
    </ErrorBoundary>
  )
}

export default App