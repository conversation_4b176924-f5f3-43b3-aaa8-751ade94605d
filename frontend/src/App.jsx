import React, { useState, useEffect } from 'react'
import { AuthProvider, useAuth } from './contexts/AuthContext.jsx'
import { AuthPage } from './pages/AuthPage.jsx'
import { DashboardPage } from './pages/DashboardPage.jsx'
import { GoogleAdsSetupPage } from './pages/GoogleAdsSetupPage.jsx'
import { OnboardingFlow } from './components/onboarding/OnboardingFlow.jsx'
import { useOnboarding } from './hooks/useOnboarding.js'
import './App.css'

const AppContent = () => {
  const { user, loading } = useAuth()
  const [currentRoute, setCurrentRoute] = useState('dashboard')
  const { hasCompletedOnboarding, isLoading: onboardingLoading, completeOnboarding } = useOnboarding()
  const [userProfile, setUserProfile] = useState(null)

  // Debug mode - set to true to bypass authentication temporarily
  const DEBUG_MODE = false

  // Simple routing based on URL hash
  useEffect(() => {
    const handleHashChange = () => {
      const hash = window.location.hash.slice(1)
      const validRoutes = [
        'dashboard', 'google-ads-setup', 'clients', 'search-mining', 
        'bulk-optimize', 'ad-generator', 'negative-keywords', 'settings', 'team-settings', 'onboarding'
      ]
      if (hash && validRoutes.includes(hash)) {
        setCurrentRoute(hash)
      }
    }

    // Set initial route
    handleHashChange()

    // Listen for hash changes
    window.addEventListener('hashchange', handleHashChange)
    return () => window.removeEventListener('hashchange', handleHashChange)
  }, [])

  // Fetch user profile
  useEffect(() => {
    if (user) {
      // Mock user profile for now
      setUserProfile({
        full_name: user.email?.split('@')[0],
        email: user.email,
        organization: 'Brand Wisdom Agency'
      })
    }
  }, [user])

  // Global navigation function (currently unused but will be used for navigation)
  // const navigate = (route) => {
  //   window.location.hash = route
  //   setCurrentRoute(route)
  // }

  if (loading && !DEBUG_MODE) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading authentication...</p>
          <p className="mt-2 text-sm text-gray-500">
            If this takes too long, check the browser console for errors
          </p>
        </div>
      </div>
    )
  }

  if (!user && !DEBUG_MODE) {
    return <AuthPage />
  }

  // Show onboarding for new team members
  if (user && !hasCompletedOnboarding && !onboardingLoading) {
    return (
      <OnboardingFlow 
        onComplete={completeOnboarding}
        userProfile={userProfile}
      />
    )
  }

  // Render the appropriate page based on current route
  const renderPage = () => {
    switch (currentRoute) {
      case 'google-ads-setup':
        return <GoogleAdsSetupPage />
      case 'onboarding':
        return (
          <OnboardingFlow 
            onComplete={() => window.location.hash = 'dashboard'}
            userProfile={userProfile}
          />
        )
      case 'clients':
        return <DashboardPage /> // TODO: Create ClientsPage
      case 'search-mining':
        return <DashboardPage /> // TODO: Create SearchMiningPage
      case 'bulk-optimize':
        return <DashboardPage /> // TODO: Create BulkOptimizePage
      case 'ad-generator':
        return <DashboardPage /> // TODO: Create AdGeneratorPage
      case 'negative-keywords':
        return <DashboardPage /> // TODO: Create NegativeKeywordsPage
      case 'settings':
      case 'team-settings':
        return <DashboardPage /> // TODO: Create TeamSettingsPage
      case 'dashboard':
      default:
        return <DashboardPage />
    }
  }

  return (
    <div>
      {renderPage()}
    </div>
  )
}

function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  )
}

export default App