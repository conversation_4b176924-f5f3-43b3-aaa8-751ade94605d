/* Enhanced Component Styles */

/* Modern Card Component */
.dashboard-card {
  background: var(--brand-white);
  border-radius: var(--radius-lg);
  padding: var(--space-5);
  box-shadow: var(--shadow-sm);
  border: 1px solid rgba(0, 0, 0, 0.04);
  transition: all var(--transition-base) cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.dashboard-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, 
    var(--brand-primary-500) 0%, 
    var(--brand-primary-600) 50%, 
    var(--brand-primary-500) 100%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
  opacity: 0;
}

.dashboard-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-hover);
  border-color: rgba(65, 114, 245, 0.1);
}

.dashboard-card:hover::before {
  transform: translateX(0);
  opacity: 1;
}

/* Card Header */
.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-4);
}

.card-title {
  font-family: var(--font-heading);
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--brand-primary-900);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.card-icon {
  width: 20px;
  height: 20px;
  color: var(--brand-primary-600);
}

/* Stat Cards */
.stat-card {
  background: var(--brand-white);
  border-radius: var(--radius-md);
  padding: var(--space-4);
  box-shadow: var(--shadow-xs);
  border: 1px solid rgba(0, 0, 0, 0.04);
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.stat-value {
  font-size: 2rem;
  font-weight: 800;
  color: var(--brand-primary-900);
  font-family: var(--font-heading);
  line-height: 1.2;
  margin-bottom: var(--space-1);
}

.stat-label {
  font-size: 0.875rem;
  color: var(--brand-gray-400);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.stat-trend {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  margin-top: var(--space-2);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 600;
}

.stat-trend.positive {
  background: rgba(39, 192, 132, 0.1);
  color: var(--brand-success);
}

.stat-trend.negative {
  background: rgba(239, 94, 94, 0.1);
  color: var(--brand-error);
}

/* Enhanced Buttons */
.btn-primary {
  background: var(--brand-primary-600);
  color: var(--brand-white);
  padding: var(--space-3) var(--space-5);
  border-radius: var(--radius-sm);
  font-weight: 600;
  font-size: 0.875rem;
  border: none;
  cursor: pointer;
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-full);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.btn-primary:hover {
  background: var(--brand-primary-700);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-primary:hover::before {
  width: 300px;
  height: 300px;
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

/* Secondary Button */
.btn-secondary {
  background: transparent;
  color: var(--brand-primary-600);
  padding: var(--space-3) var(--space-5);
  border: 1px solid var(--brand-primary-600);
  border-radius: var(--radius-sm);
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all var(--transition-base);
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
}

.btn-secondary:hover {
  background: var(--brand-primary-50);
  border-color: var(--brand-primary-700);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

/* Ghost Button */
.btn-ghost {
  background: transparent;
  color: var(--brand-gray-400);
  padding: var(--space-2) var(--space-3);
  border: none;
  border-radius: var(--radius-sm);
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all var(--transition-base);
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
}

.btn-ghost:hover {
  background: var(--brand-gray-100);
  color: var(--brand-primary-600);
}

/* Form Inputs */
.form-input {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  background: var(--brand-white);
  border: 1px solid #E5E7EB;
  border-radius: var(--radius-sm);
  font-family: var(--font-primary);
  font-size: 0.875rem;
  color: var(--brand-gray-900);
  transition: all var(--transition-fast);
  position: relative;
}

.form-input:focus {
  outline: none;
  border-color: var(--brand-primary-500);
  box-shadow: 0 0 0 3px rgba(65, 114, 245, 0.1);
}

.form-input::placeholder {
  color: var(--brand-gray-400);
}

/* Select Dropdown */
.form-select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

/* Campaign Overview */
.campaign-item {
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  padding: 1rem;
  background: var(--bg-primary);
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

.campaign-item:hover {
  background: var(--bg-tertiary);
  box-shadow: var(--shadow-hover);
  transform: translateY(-1px);
}

.performance-trend {
  margin-bottom: 0.75rem;
  padding: 0.75rem;
  background: var(--bg-secondary);
  border-radius: 0.5rem;
}

/* Loading States */
.skeleton-box {
  background: linear-gradient(90deg, 
    var(--brand-gray-100) 25%, 
    rgba(65, 114, 245, 0.05) 50%, 
    var(--brand-gray-100) 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: var(--radius-sm);
}

.skeleton-text {
  height: 1em;
  margin-bottom: var(--space-2);
}

.skeleton-title {
  height: 1.5em;
  width: 60%;
  margin-bottom: var(--space-3);
}

/* Status Badge */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.status-badge.active {
  background: rgba(39, 192, 132, 0.1);
  color: var(--brand-success);
}

.status-badge.inactive {
  background: rgba(107, 114, 128, 0.1);
  color: var(--brand-gray-400);
}

.status-badge.warning {
  background: rgba(254, 205, 121, 0.1);
  color: var(--brand-warning);
}

.status-badge.error {
  background: rgba(239, 94, 94, 0.1);
  color: var(--brand-error);
}

/* Pulse Indicator */
.pulse-dot {
  display: inline-block;
  width: 6px;
  height: 6px;
  background: currentColor;
  border-radius: var(--radius-full);
  position: relative;
}

.pulse-dot::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: currentColor;
  border-radius: inherit;
  animation: pulse 2s ease-in-out infinite;
}

/* Table Styles */
.data-table {
  width: 100%;
  background: var(--brand-white);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  border: 1px solid rgba(0, 0, 0, 0.04);
}

.data-table thead {
  background: var(--brand-gray-100);
}

.data-table th {
  padding: var(--space-3) var(--space-4);
  text-align: left;
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--brand-gray-400);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.data-table td {
  padding: var(--space-4);
  border-top: 1px solid var(--brand-gray-100);
  font-size: 0.875rem;
  color: var(--brand-gray-900);
}

.data-table tr {
  transition: all var(--transition-fast);
}

.data-table tbody tr:hover {
  background: var(--brand-gray-100);
}

.table-header {
  border-bottom: 1px solid var(--brand-gray-100);
  padding: 1rem;
  background: var(--bg-secondary);
}

.table-body {
  position: relative;
}

.table-body > div {
  border-bottom: 1px solid var(--brand-gray-100);
  padding: 1rem;
}

.table-body > div:last-child {
  border-bottom: none;
}

/* Quick Action Cards */
.quick-action {
  background: var(--brand-white);
  border: 2px solid transparent;
  border-radius: var(--radius-md);
  padding: var(--space-4);
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}

.quick-action:hover {
  border-color: var(--brand-primary-500);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.quick-action-icon {
  width: 48px;
  height: 48px;
  margin: 0 auto var(--space-3);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-md);
  background: var(--brand-primary-50);
  color: var(--brand-primary-600);
  transition: all var(--transition-base);
}

.quick-action:hover .quick-action-icon {
  transform: scale(1.1);
  background: var(--brand-primary-100);
}

.quick-action-title {
  font-weight: 600;
  color: var(--brand-gray-900);
  margin-bottom: var(--space-1);
}

.quick-action-desc {
  font-size: 0.75rem;
  color: var(--brand-gray-400);
}

/* Notification Bell Animation */
.notification-bell {
  position: relative;
}

.notification-bell.has-notifications::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 8px;
  height: 8px;
  background: var(--brand-error);
  border-radius: var(--radius-full);
  border: 2px solid var(--brand-white);
  animation: bellPulse 2s ease-in-out infinite;
}

@keyframes bellPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}

/* Activity Feed */
.activity-item {
  display: flex;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 0.5rem;
  border: 1px solid transparent;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

.activity-item:hover {
  box-shadow: var(--shadow-sm);
  transform: translateX(2px);
}

.activity-message {
  font-weight: 500;
  font-size: 0.875rem;
  color: var(--brand-gray-900);
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.activity-impact {
  font-size: 0.75rem;
  color: var(--brand-gray-400);
  margin-top: 0.25rem;
}

.activity-time {
  font-size: 0.75rem;
  color: var(--brand-gray-400);
  display: flex;
  align-items: center;
  gap: 0.25rem;
  margin-top: 0.25rem;
}

/* Account Manager */
.account-card {
  overflow: hidden;
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease-in-out;
}

.account-card:hover {
  box-shadow: var(--shadow-md);
}

.account-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--brand-gray-900);
}

.account-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 1.25rem;
}

.metric-card {
  background: var(--brand-white);
  border-radius: 0.5rem;
  padding: 1rem;
  text-align: center;
  border: 1px solid var(--border-color);
  transition: all 0.2s ease-in-out;
}

.metric-card:hover {
  box-shadow: var(--shadow-sm);
  transform: translateY(-1px);
}

.sub-account-card {
  padding: 1.25rem;
  background: var(--brand-white);
  border-radius: 0.5rem;
  border: 1px solid var(--border-color);
  transition: all 0.2s ease-in-out;
}

.sub-account-card:hover {
  box-shadow: var(--shadow-sm);
}

/* Error States */
.error-card {
  border-color: rgba(239, 94, 94, 0.2);
  background-color: rgba(239, 94, 94, 0.05);
}

/* Chart Styles */
.chart-tooltip {
  background: var(--brand-white);
  padding: 1rem;
  border-radius: 0.5rem;
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-color);
}

.tooltip-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--brand-gray-400);
  margin-bottom: 0.5rem;
  font-family: var(--font-primary);
}