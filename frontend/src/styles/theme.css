/* Brand Wisdom Theme System - Light Mode Only */

:root {
  /* Brand Wisdom Primary Colors */
  --brand-primary-50: #F4F7FF;
  --brand-primary-100: #E7EEFF;
  --brand-primary-300: #B9CBFF;
  --brand-primary-500: #4172F5;
  --brand-primary-600: #3E5CE7;
  --brand-primary-700: #324ECF;
  --brand-primary-900: #07153F;
  
  /* Brand Wisdom Secondary Colors */
  --brand-secondary: #444751;
  --brand-text: #6F7176;
  --brand-gold-500: #FECD79;
  
  /* Neutral Colors */
  --brand-white: #FFFFFF;
  --brand-gray-100: #F3F6FA;
  --brand-gray-400: #6F7176;
  --brand-gray-900: #1A1E29;
  
  /* Layout Colors */
  --bg-primary: #FFFFFF;
  --bg-secondary: #F8F9FA;
  --bg-tertiary: #F3F6FA;
  --border-color: #E5E7EB;
  
  /* Status Colors */
  --brand-success: #27C084;
  --brand-error: #EF5E5E;
  --brand-warning: #FECD79;
  --brand-info: #4172F5;
  
  /* Shadows - Inspired by UI Sample */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.04);
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.04);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.12);
  --shadow-xl: 0 16px 32px rgba(0, 0, 0, 0.16);
  --shadow-hover: 0 12px 24px rgba(0, 0, 0, 0.15);
  
  /* Border Radius */
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 24px;
  --radius-full: 9999px;
  
  /* Typography */
  --font-primary: 'Jost', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  --font-heading: 'Playfair Display', Georgia, serif;
  --font-system: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  
  /* Transitions */
  --transition-fast: 150ms ease;
  --transition-base: 200ms ease;
  --transition-slow: 300ms ease;
  
  /* Spacing */
  --space-1: 4px;
  --space-2: 8px;
  --space-3: 12px;
  --space-4: 16px;
  --space-5: 24px;
  --space-6: 32px;
  --space-8: 64px;
  
  /* Z-index layers */
  --z-dropdown: 50;
  --z-sticky: 100;
  --z-fixed: 200;
  --z-modal-backdrop: 300;
  --z-modal: 400;
  --z-popover: 500;
  --z-tooltip: 600;
}

/* Global Styles */
* {
  box-sizing: border-box;
}

body {
  font-family: var(--font-primary);
  color: var(--brand-gray-900);
  background-color: var(--brand-gray-100);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color var(--transition-slow), color var(--transition-slow);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  color: var(--brand-primary-900);
  font-weight: 700;
  letter-spacing: -0.02em;
  line-height: 1.2;
}

/* Modern Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--brand-gray-100);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--brand-gray-400);
  border-radius: var(--radius-sm);
  transition: background var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--brand-secondary);
}

/* Card Styles */
.modern-card {
  background: var(--brand-white);
  border-radius: var(--radius-lg);
  padding: var(--space-5);
  box-shadow: var(--shadow-sm);
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all var(--transition-base);
}

.modern-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-hover);
  border-color: rgba(65, 114, 245, 0.1);
}

/* Button Base Styles */
.modern-button {
  font-family: var(--font-primary);
  font-weight: 600;
  padding: var(--space-3) var(--space-5);
  border-radius: var(--radius-sm);
  transition: all var(--transition-base);
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  border: none;
  outline: none;
  position: relative;
  overflow: hidden;
}

.modern-button:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.modern-button:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

/* Primary Button */
.modern-button-primary {
  background: var(--brand-primary-600);
  color: var(--brand-white);
}

.modern-button-primary:hover {
  background: var(--brand-primary-700);
}

/* Input Styles */
.modern-input {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  background: var(--brand-white);
  border: 1px solid #E5E7EB;
  border-radius: var(--radius-sm);
  font-family: var(--font-primary);
  font-size: 0.9375rem;
  color: var(--brand-gray-900);
  transition: all var(--transition-fast);
}

.modern-input:focus {
  outline: none;
  border-color: var(--brand-primary-500);
  box-shadow: 0 0 0 3px rgba(65, 114, 245, 0.1);
}

/* Navigation Item Hover Effect */
.nav-item-hover {
  position: relative;
  transition: all var(--transition-base);
  border-radius: var(--radius-sm);
}

.nav-item-hover:hover {
  background: rgba(65, 114, 245, 0.05);
  color: var(--brand-primary-600);
  transform: translateX(4px);
}

/* Status Indicators */
.status-pulse {
  display: inline-block;
  width: 8px;
  height: 8px;
  background: var(--brand-success);
  border-radius: var(--radius-full);
  position: relative;
}

.status-pulse::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: inherit;
  border-radius: inherit;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0;
    transform: scale(1.5);
  }
  100% {
    opacity: 0;
    transform: scale(1.5);
  }
}

/* Loading Skeleton */
.skeleton {
  background: linear-gradient(90deg, 
    var(--brand-gray-100) 25%, 
    rgba(65, 114, 245, 0.05) 50%, 
    var(--brand-gray-100) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: var(--radius-sm);
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Gradient Effects */
.gradient-border {
  position: relative;
  background: var(--brand-white);
  border-radius: var(--radius-lg);
  padding: 2px;
}

.gradient-border::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--brand-primary-500) 0%, var(--brand-primary-700) 100%);
  border-radius: inherit;
  z-index: -1;
  opacity: 0;
  transition: opacity var(--transition-base);
}

.gradient-border:hover::before {
  opacity: 1;
}

/* AI Badge Style */
.ai-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  background: rgba(65, 114, 245, 0.1);
  color: var(--brand-primary-600);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Fade In Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn var(--transition-slow) ease-out;
}

/* Hover Glow Effect */
.hover-glow {
  position: relative;
  overflow: hidden;
}

.hover-glow::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(65, 114, 245, 0.1) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: all var(--transition-slow);
  pointer-events: none;
}

.hover-glow:hover::after {
  width: 200%;
  height: 200%;
}