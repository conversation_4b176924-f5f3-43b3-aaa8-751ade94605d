import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card';
import { Shield, AlertTriangle, Filter, TrendingDown } from 'lucide-react';

export default function NegativeKeywordsPage() {
  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <div className="flex items-center gap-2 mb-2">
          <Shield className="h-8 w-8 text-red-600" />
          <h1 className="text-3xl font-bold">Negative Keywords AI</h1>
          <span className="bg-purple-100 text-purple-800 text-xs font-semibold px-2 py-1 rounded-full">AI</span>
        </div>
        <p className="text-gray-600">
          Automatically identify and eliminate wasteful search terms to optimize your ad spend
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-600" />
            Wasted Spend Detection
          </CardTitle>
          <CardDescription>
            AI-powered analysis to identify irrelevant search terms draining your budget
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
            <Filter className="h-12 w-12 text-red-400 mx-auto mb-3" />
            <p className="text-gray-600 mb-2">Negative Keywords AI features coming soon</p>
            <p className="text-sm text-gray-500">
              • Automated negative keyword discovery<br />
              • Wasted spend analysis<br />
              • Bulk negative keyword lists<br />
              • Real-time monitoring
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}