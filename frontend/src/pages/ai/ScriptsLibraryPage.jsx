import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card';
import { Code2, Library, FileCode, Rocket } from 'lucide-react';

export default function ScriptsLibraryPage() {
  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <div className="flex items-center gap-2 mb-2">
          <Code2 className="h-8 w-8 text-gray-700" />
          <h1 className="text-3xl font-bold">Scripts Library</h1>
          <span className="bg-purple-100 text-purple-800 text-xs font-semibold px-2 py-1 rounded-full">AI</span>
        </div>
        <p className="text-gray-600">
          Pre-built and AI-generated scripts for advanced Google Ads automation
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Library className="h-5 w-5 text-gray-700" />
            Automation Scripts
          </CardTitle>
          <CardDescription>
            Ready-to-use scripts for common optimization tasks
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 text-center">
            <FileCode className="h-12 w-12 text-gray-400 mx-auto mb-3" />
            <p className="text-gray-600 mb-2">Scripts Library features coming soon</p>
            <p className="text-sm text-gray-500">
              • Budget monitoring scripts<br />
              • Performance alert scripts<br />
              • Automated reporting<br />
              • Custom script generation
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}