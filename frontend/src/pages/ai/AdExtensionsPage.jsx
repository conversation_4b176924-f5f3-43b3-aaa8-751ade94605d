import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card';
import { Maximize2, Link, Phone, MapPin } from 'lucide-react';

export default function AdExtensionsPage() {
  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <div className="flex items-center gap-2 mb-2">
          <Maximize2 className="h-8 w-8 text-orange-600" />
          <h1 className="text-3xl font-bold">Ad Extensions Max</h1>
          <span className="bg-purple-100 text-purple-800 text-xs font-semibold px-2 py-1 rounded-full">AI</span>
        </div>
        <p className="text-gray-600">
          Maximize ad real estate with AI-optimized extensions for better visibility and CTR
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Link className="h-5 w-5 text-orange-600" />
            Extension Optimization
          </CardTitle>
          <CardDescription>
            Automatically generate and optimize ad extensions for maximum impact
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-6 text-center">
            <div className="flex justify-center gap-4 mb-3">
              <Link className="h-8 w-8 text-orange-400" />
              <Phone className="h-8 w-8 text-orange-400" />
              <MapPin className="h-8 w-8 text-orange-400" />
            </div>
            <p className="text-gray-600 mb-2">Ad Extensions Max features coming soon</p>
            <p className="text-sm text-gray-500">
              • Sitelink optimization<br />
              • Callout extension generation<br />
              • Structured snippet suggestions<br />
              • Extension performance tracking
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}