import React from 'react'
import { <PERSON>, <PERSON>Header, CardTitle, CardDescription } from '../../components/ui/card'
import { Search, TrendingUp, Filter, Download } from 'lucide-react'
import { Button } from '../../components/ui/button'

export const SearchMiningPage = () => {
  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-playfair font-bold text-gray-900">Search Query Mining</h1>
          <p className="text-gray-600 mt-1">Discover hidden opportunities in search queries</p>
        </div>
        <div className="flex gap-3">
          <Button variant="secondary">
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </Button>
          <Button variant="primary">
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardDescription>New Opportunities</CardDescription>
                <CardTitle className="text-2xl font-bold">1,234</CardTitle>
              </div>
              <Search className="h-8 w-8 text-emerald-500" />
            </div>
          </CardHeader>
        </Card>
        
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardDescription>Potential Revenue</CardDescription>
                <CardTitle className="text-2xl font-bold">₹45.2L</CardTitle>
              </div>
              <TrendingUp className="h-8 w-8 text-blue-500" />
            </div>
          </CardHeader>
        </Card>

        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardDescription>Avg. CPC Reduction</CardDescription>
                <CardTitle className="text-2xl font-bold">-23%</CardTitle>
              </div>
              <TrendingUp className="h-8 w-8 text-green-500" />
            </div>
          </CardHeader>
        </Card>
      </div>

      {/* Main Content */}
      <Card>
        <CardHeader>
          <CardTitle>Search Query Analysis</CardTitle>
          <CardDescription>
            AI-powered analysis of your search terms to find hidden opportunities
          </CardDescription>
        </CardHeader>
        <div className="p-6">
          <div className="text-center py-12 text-gray-500">
            <Search className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <p className="text-lg font-medium">No data available</p>
            <p className="text-sm mt-2">Connect your Google Ads account to start mining search queries</p>
          </div>
        </div>
      </Card>
    </div>
  )
}

export default SearchMiningPage