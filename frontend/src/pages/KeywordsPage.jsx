import React from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle, CardDescription } from '../components/ui/card'
import { Button } from '../components/ui/button'
import { Search, Users, Plus, Filter, Upload, Target } from 'lucide-react'

export const KeywordsPage = () => {
  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Keywords & Audiences</h1>
        <p className="text-gray-600">Manage keywords and audience targeting across all campaigns</p>
      </div>

      <div className="flex gap-4 mb-6">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search keywords..."
              className="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
            />
          </div>
        </div>
        <Button variant="outline">
          <Filter className="h-4 w-4 mr-2" />
          Filter
        </Button>
        <Button variant="outline">
          <Upload className="h-4 w-4 mr-2" />
          Import
        </Button>
        <Button variant="primary">
          <Plus className="h-4 w-4 mr-2" />
          Add Keywords
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Keyword Management</CardTitle>
            <CardDescription>Active keywords across all campaigns</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center py-12 text-gray-500">
              <Search className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <p className="text-lg font-medium mb-2">No keywords found</p>
              <p className="text-sm mb-4">Add keywords to start optimizing your campaigns</p>
              <Button variant="primary">
                <Plus className="h-4 w-4 mr-2" />
                Add Keywords
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Audience Targeting</CardTitle>
            <CardDescription>Custom and in-market audiences</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center py-12 text-gray-500">
              <Users className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <p className="text-lg font-medium mb-2">No audiences configured</p>
              <p className="text-sm mb-4">Create audience segments for better targeting</p>
              <Button variant="primary">
                <Plus className="h-4 w-4 mr-2" />
                Create Audience
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Keyword Opportunities</CardTitle>
          <CardDescription>Suggested keywords based on your campaigns</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="p-4 border rounded-lg hover:bg-gray-50 transition-colors">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">best running shoes 2025</h4>
                  <div className="flex gap-4 mt-1">
                    <span className="text-sm text-gray-600">Search Volume: 12.5K</span>
                    <span className="text-sm text-gray-600">Competition: Medium</span>
                    <span className="text-sm text-gray-600">CPC: ₹15-25</span>
                  </div>
                </div>
                <Button variant="outline" size="sm">
                  <Plus className="h-4 w-4 mr-1" />
                  Add
                </Button>
              </div>
            </div>
            <div className="p-4 border rounded-lg hover:bg-gray-50 transition-colors">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">marathon training shoes</h4>
                  <div className="flex gap-4 mt-1">
                    <span className="text-sm text-gray-600">Search Volume: 8.2K</span>
                    <span className="text-sm text-gray-600">Competition: Low</span>
                    <span className="text-sm text-gray-600">CPC: ₹10-18</span>
                  </div>
                </div>
                <Button variant="outline" size="sm">
                  <Plus className="h-4 w-4 mr-1" />
                  Add
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default KeywordsPage