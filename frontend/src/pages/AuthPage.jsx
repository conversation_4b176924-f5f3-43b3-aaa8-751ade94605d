import React, { useState } from 'react'
import { LoginForm } from '../components/auth/LoginForm.jsx'
import { SignUpForm } from '../components/auth/SignUpForm.jsx'

export const AuthPage = () => {
  const [isLogin, setIsLogin] = useState(true)

  const toggleMode = () => {
    setIsLogin(!isLogin)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Brand Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Brand Wisdom Solutions
          </h1>
          <p className="text-gray-600">
            Google Ads AI Search Optimization Platform
          </p>
        </div>

        {/* Auth Forms */}
        {isLogin ? (
          <LoginForm onToggleMode={toggleMode} />
        ) : (
          <SignUpForm onToggleMode={toggleMode} />
        )}

        {/* Footer */}
        <div className="mt-8 text-center text-sm text-gray-500">
          <p>© 2025 Brand Wisdom Solutions. All rights reserved.</p>
        </div>
      </div>
    </div>
  )
}