import React from 'react'
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, Card<PERSON>itle, CardDescription } from '../components/ui/card'
import { Button } from '../components/ui/button'
import { FileText, Download, Calendar, Filter, Plus, BarChart3 } from 'lucide-react'

export const ReportsPage = () => {
  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Performance Reports</h1>
        <p className="text-gray-600">Generate and schedule comprehensive campaign reports</p>
      </div>

      <div className="flex justify-between items-center mb-6">
        <div className="flex gap-3">
          <Button variant="outline">
            <Calendar className="h-4 w-4 mr-2" />
            Date Range
          </Button>
          <Button variant="outline">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
        </div>
        <Button variant="primary">
          <Plus className="h-4 w-4 mr-2" />
          Create Report
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <Card className="hover:shadow-lg transition-shadow cursor-pointer">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between mb-4">
              <FileText className="h-8 w-8 text-primary-600" />
              <span className="text-sm text-gray-500">Weekly</span>
            </div>
            <h3 className="font-semibold mb-1">Campaign Performance</h3>
            <p className="text-sm text-gray-600">All campaigns overview</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow cursor-pointer">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between mb-4">
              <BarChart3 className="h-8 w-8 text-emerald-600" />
              <span className="text-sm text-gray-500">Monthly</span>
            </div>
            <h3 className="font-semibold mb-1">Search Terms Analysis</h3>
            <p className="text-sm text-gray-600">Top performing keywords</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow cursor-pointer">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between mb-4">
              <FileText className="h-8 w-8 text-blue-600" />
              <span className="text-sm text-gray-500">Daily</span>
            </div>
            <h3 className="font-semibold mb-1">Budget Utilization</h3>
            <p className="text-sm text-gray-600">Spend tracking report</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Recent Reports</CardTitle>
          <CardDescription>Previously generated reports</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12 text-gray-500">
            <FileText className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <p className="text-lg font-medium mb-2">No reports generated</p>
            <p className="text-sm mb-4">Create your first report to track performance</p>
            <Button variant="primary">
              <Plus className="h-4 w-4 mr-2" />
              Generate Report
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default ReportsPage