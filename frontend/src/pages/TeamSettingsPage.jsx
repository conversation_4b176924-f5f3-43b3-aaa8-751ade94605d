import React from 'react'
import { <PERSON>, <PERSON>Header, CardTitle, CardDescription } from '../components/ui/card'
import { Users, UserPlus, Shield, Activity } from 'lucide-react'
import { Button } from '../components/ui/button'

export const TeamSettingsPage = () => {
  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-playfair font-bold text-gray-900">Team Settings</h1>
          <p className="text-gray-600 mt-1">Manage team members and permissions</p>
        </div>
        <Button variant="primary">
          <UserPlus className="h-4 w-4 mr-2" />
          Invite Team Member
        </Button>
      </div>

      {/* Team Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader>
            <CardDescription>Team Members</CardDescription>
            <CardTitle className="text-2xl font-bold">8</CardTitle>
          </CardHeader>
        </Card>

        <Card>
          <CardHeader>
            <CardDescription>Active Users</CardDescription>
            <CardTitle className="text-2xl font-bold">6</CardTitle>
          </CardHeader>
        </Card>

        <Card>
          <CardHeader>
            <CardDescription>Pending Invites</CardDescription>
            <CardTitle className="text-2xl font-bold">2</CardTitle>
          </CardHeader>
        </Card>
      </div>

      {/* Team Management Sections */}
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Team Members</CardTitle>
            <CardDescription>View and manage team access</CardDescription>
          </CardHeader>
          <div className="p-6">
            <div className="text-center py-8 text-gray-500">
              <Users className="h-10 w-10 mx-auto mb-3 text-gray-400" />
              <p className="text-sm">Team member management coming soon</p>
            </div>
          </div>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Roles & Permissions</CardTitle>
            <CardDescription>Configure access levels and permissions</CardDescription>
          </CardHeader>
          <div className="p-6">
            <div className="text-center py-8 text-gray-500">
              <Shield className="h-10 w-10 mx-auto mb-3 text-gray-400" />
              <p className="text-sm">Permission management coming soon</p>
            </div>
          </div>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Activity Log</CardTitle>
            <CardDescription>Track team activity and changes</CardDescription>
          </CardHeader>
          <div className="p-6">
            <div className="text-center py-8 text-gray-500">
              <Activity className="h-10 w-10 mx-auto mb-3 text-gray-400" />
              <p className="text-sm">Activity tracking coming soon</p>
            </div>
          </div>
        </Card>
      </div>
    </div>
  )
}

export default TeamSettingsPage