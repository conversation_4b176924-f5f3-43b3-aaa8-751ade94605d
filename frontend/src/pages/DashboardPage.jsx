import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card.jsx'
import { Button } from '../components/ui/button.jsx'
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Users, 
  Search,
  Target,
  Zap,
  Shield,
  AlertCircle,
  RefreshCw,
  ArrowUp,
  ArrowDown,
  Calendar,
  ChevronDown,
  Plus
} from 'lucide-react'
import { useAuth } from '../contexts/AuthContext.jsx'
import { useDashboard } from '../hooks/useDashboard.js'
import { AccountManager } from '../components/dashboard/AccountManager.jsx'
import { CampaignOverview } from '../components/dashboard/CampaignOverview.jsx'
import { RecentActivity } from '../components/dashboard/RecentActivity.jsx'
import { PerformanceChart } from '../components/charts/PerformanceChart.jsx'
import { MetricsComparison } from '../components/charts/MetricsComparison.jsx'
import { Sparkline } from '../components/charts/Sparkline.jsx'
import { SkeletonCard, SkeletonStats, SkeletonChart } from '../components/ui/skeleton.jsx'

export const DashboardPage = () => {
  const { user } = useAuth()
  const { stats, campaigns, recentActivity, loading, error, refetch } = useDashboard()
  const [dateRange, setDateRange] = useState('Last 7 days')
  
  // Check if we just completed Google Ads OAuth
  React.useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search)
    if (urlParams.get('google_ads_connected') === 'true') {
      // Clear the URL parameter
      window.history.replaceState({}, document.title, window.location.pathname + window.location.hash)
      
      // Show success message (you might want to use a toast notification)
      console.log('Google Ads connected successfully!')
      
      // Trigger a refresh
      refetch()
    }
  }, [])

  const getGreeting = () => {
    const hour = new Date().getHours()
    if (hour < 12) return 'Good morning'
    if (hour < 18) return 'Good afternoon'
    return 'Good evening'
  }

  const formatStat = (value, type) => {
    if (type === 'currency') {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(value)
    }
    return value.toLocaleString()
  }

  const statsData = stats ? [
    {
      title: 'Active Clients',
      value: stats.total_campaigns,
      change: stats.performance_change?.campaigns || '+0%',
      trend: stats.performance_change?.campaigns?.startsWith('+') ? 'up' : 'down',
      icon: Users,
      type: 'number',
      color: 'text-primary-600'
    },
    {
      title: 'Total Ad Spend',
      value: stats.monthly_spend,
      change: stats.performance_change?.spend || '+0%',
      trend: stats.performance_change?.spend?.startsWith('+') ? 'up' : 'down',
      icon: DollarSign,
      type: 'currency',
      color: 'text-gold-500'
    },
    {
      title: 'Avg. Quality Score',
      value: stats.avg_quality_score,
      change: stats.performance_change?.quality_score || '+0%',
      trend: stats.performance_change?.quality_score?.startsWith('+') ? 'up' : 'down',
      icon: Target,
      type: 'number',
      color: 'text-success'
    },
    {
      title: 'Active Keywords',
      value: stats.active_keywords,
      change: stats.performance_change?.keywords || '+0%',
      trend: stats.performance_change?.keywords?.startsWith('+') ? 'up' : 'down',
      icon: Search,
      type: 'number',
      color: 'text-primary-500'
    },
  ] : []

  const features = [
    {
      title: 'Search Query Mining',
      description: 'Find high-performing search terms and eliminate wasted spend across all client accounts',
      icon: Search,
      status: 'Active',
      statusColor: 'bg-success text-white',
      action: () => window.location.hash = 'search-mining'
    },
    {
      title: 'Bulk Optimization',
      description: 'Apply optimization strategies across multiple client accounts simultaneously',
      icon: Target,
      status: 'Active',
      statusColor: 'bg-success text-white',
      action: () => window.location.hash = 'bulk-optimize'
    },
    {
      title: 'Ad Copy Generator',
      description: 'Create high-converting ad copy with AI assistance for any client or industry',
      icon: Zap,
      status: 'Active',
      statusColor: 'bg-success text-white',
      action: () => window.location.hash = 'ad-generator'
    },
    {
      title: 'Negative Keywords',
      description: 'Build and maintain negative keyword lists across all client accounts',
      icon: Shield,
      status: 'Active',
      statusColor: 'bg-success text-white',
      action: () => window.location.hash = 'negative-keywords'
    },
  ]

  if (loading) {
    return (
      <div className="min-h-screen bg-slate-50">
          {/* Loading skeleton for top bar */}
          <div className="bg-white border-b border-slate-200">
            <div className="px-6 lg:px-8 py-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="h-6 w-48 bg-slate-200 rounded mb-1 animate-pulse"></div>
                  <div className="h-4 w-64 bg-slate-200 rounded animate-pulse"></div>
                </div>
                <div className="flex items-center gap-3">
                  <div className="h-10 w-32 bg-slate-200 rounded animate-pulse"></div>
                  <div className="h-10 w-36 bg-slate-200 rounded animate-pulse"></div>
                </div>
              </div>
            </div>
          </div>
          
          <div className="px-6 lg:px-8 py-6">
            {/* Loading skeleton for stats grid */}
            <div className="mb-6">
              <div className="h-5 w-40 bg-slate-200 rounded mb-4 animate-pulse"></div>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                {[1, 2, 3, 4].map((i) => (
                  <div key={i} className="bg-white rounded-lg border border-slate-200 p-4">
                    <div className="h-4 w-24 bg-slate-200 rounded mb-2 animate-pulse"></div>
                    <div className="h-8 w-20 bg-slate-200 rounded animate-pulse"></div>
                  </div>
                ))}
              </div>
            </div>

            {/* Loading skeleton for main content */}
            <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
              <div className="lg:col-span-8 space-y-6">
                <div>
                  <div className="h-5 w-40 bg-slate-200 rounded mb-4 animate-pulse"></div>
                  <SkeletonCard lines={3} />
                </div>
                <div>
                  <div className="h-5 w-40 bg-slate-200 rounded mb-4 animate-pulse"></div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <SkeletonChart height="h-48" />
                    <SkeletonChart height="h-48" />
                  </div>
                </div>
              </div>
              <div className="lg:col-span-4 space-y-6">
                <div>
                  <div className="h-5 w-40 bg-slate-200 rounded mb-4 animate-pulse"></div>
                  <SkeletonCard lines={4} />
                </div>
                <div>
                  <div className="h-5 w-40 bg-slate-200 rounded mb-4 animate-pulse"></div>
                  <SkeletonCard lines={4} />
                </div>
              </div>
            </div>
          </div>
        </div>
    )
  }

  if (error) {
    return (
        <div className="p-6">
          <Card className="border-red-200 bg-red-50">
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <AlertCircle className="h-6 w-6 text-red-600" />
                <div className="flex-1">
                  <h3 className="font-semibold text-slate-900">Error loading dashboard</h3>
                  <p className="text-sm text-slate-600 mt-1">{error}</p>
                </div>
                <Button variant="outline" size="sm" onClick={refetch}>
                  <RefreshCw className="h-4 w-4 mr-1" />
                  Retry
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
    )
  }

  return (
    <div className="min-h-screen bg-slate-50">
        {/* Top Action Bar */}
        <div className="bg-white border-b border-slate-200">
          <div className="max-w-screen-2xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-playfair font-bold text-primary-900">
                  Dashboard Overview
                </h1>
                <p className="text-sm text-gray-400 font-jost">
                  {new Date().toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" className="h-8 text-xs">
                  <Calendar className="h-3.5 w-3.5 mr-1.5" />
                  {dateRange}
                  <ChevronDown className="h-3.5 w-3.5 ml-1" />
                </Button>
                <Button size="sm" className="bg-primary-600 hover:bg-primary-700 h-8 text-xs">
                  <Plus className="h-3.5 w-3.5 mr-1" />
                  New Campaign
                </Button>
              </div>
            </div>
          </div>
        </div>
        
        {/* Main Content */}
        <div className="max-w-screen-2xl mx-auto px-4 sm:px-6 lg:px-8 py-4">

          {/* Key Metrics - Prioritized at top */}
          <div className="mb-4">
            <h2 className="text-sm font-jost font-semibold text-gray-400 uppercase tracking-wider mb-3">Key Performance Metrics</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              {statsData.map((stat, index) => (
                <div
                  key={stat.title}
                  className="stat-card"
                >
                  <div className="flex items-center justify-between mb-2">
                    <span className="stat-label">{stat.title}</span>
                    <stat.icon className="card-icon" />
                  </div>
                  <p className="stat-value font-playfair">
                    {stat.type === 'currency' ? formatStat(stat.value, 'currency') : stat.value}
                  </p>
                  <div className={`stat-trend ${stat.trend === 'up' ? 'positive' : 'negative'}`}>
                    {stat.trend === 'up' ? <ArrowUp className="h-3 w-3" /> : <ArrowDown className="h-3 w-3" />}
                    {stat.change}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Two Column Layout for Main Content */}
          <div className="grid grid-cols-1 xl:grid-cols-3 gap-4">
            {/* Left Column - Campaigns and Accounts */}
            <div className="xl:col-span-2 space-y-4">
              {/* Campaign Performance */}
              <div>
                <h2 className="text-sm font-jost font-semibold text-gray-400 uppercase tracking-wider mb-3">Campaign Performance</h2>
                <CampaignOverview campaigns={campaigns} />
              </div>
              
              {/* Performance Charts */}
              <div>
                <h2 className="text-sm font-jost font-semibold text-gray-400 uppercase tracking-wider mb-3">Performance Trends</h2>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
                  <PerformanceChart
                    type="area"
                    metrics={['impressions', 'clicks']}
                    title="Impressions & Clicks"
                  />
                  <PerformanceChart
                    type="bar"
                    metrics={['spend', 'conversions']}
                    title="Spend & Conversions"
                  />
                </div>
              </div>
            </div>
            
            {/* Right Column - Activity and Quick Actions */}
            <div className="space-y-4">
              {/* Recent Activity */}
              <div>
                <h2 className="text-sm font-jost font-semibold text-gray-400 uppercase tracking-wider mb-3">Recent Activity</h2>
                <RecentActivity activities={recentActivity} />
              </div>
              
              {/* Quick Actions */}
              <div>
                <h2 className="text-sm font-jost font-semibold text-gray-400 uppercase tracking-wider mb-3">Quick Actions</h2>
                <Card className="border-slate-200">
                  <CardContent className="p-3">
                    <div className="grid grid-cols-1 gap-1.5">
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full justify-start h-10 text-sm font-medium hover:bg-slate-50"
                        onClick={() => window.location.hash = 'search-terms'}
                      >
                        <Search className="h-3.5 w-3.5 mr-2 text-slate-500" />
                        Analyze Search Terms
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full justify-start h-10 text-sm font-medium hover:bg-slate-50"
                        onClick={() => window.location.hash = 'quality-scores'}
                      >
                        <Target className="h-3.5 w-3.5 mr-2 text-slate-500" />
                        Review Quality Scores
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full justify-start h-10 text-sm font-medium hover:bg-slate-50"
                        onClick={() => window.location.hash = 'ad-copy'}
                      >
                        <Zap className="h-3.5 w-3.5 mr-2 text-slate-500" />
                        Generate Ad Copy
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full justify-start h-10 text-sm font-medium hover:bg-slate-50"
                        onClick={() => window.location.hash = 'negative-keywords'}
                      >
                        <Shield className="h-3.5 w-3.5 mr-2 text-slate-500" />
                        Manage Negative Keywords
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>

          {/* Account Manager Section - Full Width */}
          <div className="mt-6">
            <div className="flex items-center justify-between mb-2">
              <h2 className="text-xs font-medium text-slate-600 uppercase tracking-wider">
                Client Accounts Overview
              </h2>
              <Button variant="outline" size="sm" className="h-8 text-xs">
                <Users className="h-3.5 w-3.5 mr-1.5" />
                Add Client
              </Button>
            </div>
            <AccountManager />
          </div>




        </div>
      </div>
  )
}

export default DashboardPage