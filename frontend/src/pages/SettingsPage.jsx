import React from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON>er, CardTitle, CardDescription } from '../components/ui/card'
import { <PERSON><PERSON><PERSON>, User, Bell, Shield, Database } from 'lucide-react'
import { Button } from '../components/ui/button'

export const SettingsPage = () => {
  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-playfair font-bold text-gray-900">Settings</h1>
        <p className="text-gray-600 mt-1">Manage your account preferences and configuration</p>
      </div>

      {/* Settings Sections */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <div className="flex items-center gap-3">
              <User className="h-5 w-5 text-primary-600" />
              <div>
                <CardTitle>Account Settings</CardTitle>
                <CardDescription>Manage your profile and preferences</CardDescription>
              </div>
            </div>
          </CardHeader>
        </Card>

        <Card>
          <CardHeader>
            <div className="flex items-center gap-3">
              <Bell className="h-5 w-5 text-primary-600" />
              <div>
                <CardTitle>Notifications</CardTitle>
                <CardDescription>Configure alerts and notifications</CardDescription>
              </div>
            </div>
          </CardHeader>
        </Card>

        <Card>
          <CardHeader>
            <div className="flex items-center gap-3">
              <Shield className="h-5 w-5 text-primary-600" />
              <div>
                <CardTitle>Security</CardTitle>
                <CardDescription>Manage security and API access</CardDescription>
              </div>
            </div>
          </CardHeader>
        </Card>

        <Card>
          <CardHeader>
            <div className="flex items-center gap-3">
              <Database className="h-5 w-5 text-primary-600" />
              <div>
                <CardTitle>Data Management</CardTitle>
                <CardDescription>Export and backup settings</CardDescription>
              </div>
            </div>
          </CardHeader>
        </Card>
      </div>

      {/* Coming Soon */}
      <Card>
        <div className="p-12 text-center">
          <Settings className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <p className="text-lg font-medium text-gray-500">Settings page coming soon</p>
          <p className="text-sm text-gray-400 mt-2">Configuration options will be available here</p>
        </div>
      </Card>
    </div>
  )
}

export default SettingsPage