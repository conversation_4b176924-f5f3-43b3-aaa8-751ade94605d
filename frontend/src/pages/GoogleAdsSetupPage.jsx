import React, { useState, useEffect } from 'react'
import { useAuth } from '../contexts/AuthContext.jsx'
import { DashboardLayout } from '../components/layout/DashboardLayout.jsx'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card.jsx'
import { Button } from '../components/ui/button.jsx'
import { Alert, AlertDescription } from '../components/ui/alert.jsx'
import { googleAdsApi } from '../lib/api.js'
import { 
  CheckCircle, 
  AlertCircle, 
  ExternalLink, 
  Loader2,
  Shield,
  Zap,
  BarChart3
} from 'lucide-react'

export const GoogleAdsSetupPage = () => {
  const { user } = useAuth()
  const [loading, setLoading] = useState(false)
  const [authUrl, setAuthUrl] = useState(null)
  const [accounts, setAccounts] = useState([])
  const [error, setError] = useState(null)
  const [refreshToken, setRefreshToken] = useState('')
  const [connected, setConnected] = useState(false)

  // Check URL parameters for connection status
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search)
    if (urlParams.get('google_ads_connected') === 'true') {
      setConnected(true)
      // Clear the URL parameter
      window.history.replaceState({}, document.title, window.location.pathname)
    }
  }, [])

  const generateAuthUrl = async () => {
    setLoading(true)
    setError(null)

    try {
      const data = await googleAdsApi.getAuthUrl()
      setAuthUrl(data.auth_url)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to generate authorization URL')
    } finally {
      setLoading(false)
    }
  }

  const testConnection = async () => {
    if (!refreshToken) {
      setError('Please enter a refresh token')
      return
    }

    setLoading(true)
    setError(null)

    try {
      const data = await googleAdsApi.testConnection(refreshToken)
      setAccounts(data.accounts)
      setConnected(true)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Connection test failed')
    } finally {
      setLoading(false)
    }
  }

  const fetchAccounts = async () => {
    if (!refreshToken) {
      setError('Please enter a refresh token')
      return
    }

    setLoading(true)
    setError(null)

    try {
      const data = await googleAdsApi.getAccounts(refreshToken)
      setAccounts(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch accounts')
    } finally {
      setLoading(false)
    }
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Google Ads Integration</h1>
          <p className="text-gray-600 mt-2">
            Connect your Google Ads account to start optimizing your campaigns with AI
          </p>
        </div>

        {/* Connection Status */}
        {connected && (
          <Alert className="border-green-200 bg-green-50">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800">
              Google Ads account successfully connected! You can now access all optimization features.
            </AlertDescription>
          </Alert>
        )}

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Setup Steps */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Step 1: Authorization */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Shield className="h-5 w-5 text-primary" />
                <span>Step 1: Authorize Access</span>
              </CardTitle>
              <CardDescription>
                Generate an authorization URL to grant access to your Google Ads account
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button 
                onClick={generateAuthUrl} 
                disabled={loading}
                className="w-full"
              >
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Generating...
                  </>
                ) : (
                  'Generate Authorization URL'
                )}
              </Button>

              {authUrl && (
                <div className="space-y-2">
                  <p className="text-sm text-gray-600">
                    Click the link below to authorize access:
                  </p>
                  <Button 
                    variant="outline" 
                    className="w-full"
                    onClick={() => window.open(authUrl, '_blank')}
                  >
                    <ExternalLink className="mr-2 h-4 w-4" />
                    Open Authorization Page
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Step 2: Test Connection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Zap className="h-5 w-5 text-primary" />
                <span>Step 2: Test Connection</span>
              </CardTitle>
              <CardDescription>
                Enter your refresh token to test the connection
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Refresh Token
                </label>
                <textarea
                  value={refreshToken}
                  onChange={(e) => setRefreshToken(e.target.value)}
                  placeholder="Enter your Google Ads refresh token..."
                  className="w-full p-3 border border-gray-300 rounded-md resize-none"
                  rows={3}
                />
              </div>

              <Button 
                onClick={testConnection} 
                disabled={loading || !refreshToken}
                className="w-full"
              >
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Testing...
                  </>
                ) : (
                  'Test Connection'
                )}
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Connected Accounts */}
        {accounts.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <BarChart3 className="h-5 w-5 text-primary" />
                <span>Connected Accounts</span>
              </CardTitle>
              <CardDescription>
                Google Ads accounts accessible with your credentials
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {accounts.map((account) => (
                  <div 
                    key={account.id}
                    className="flex items-center justify-between p-4 border border-gray-200 rounded-lg"
                  >
                    <div>
                      <h3 className="font-medium text-gray-900">{account.name}</h3>
                      <p className="text-sm text-gray-500">
                        ID: {account.id} • {account.currency_code} • {account.time_zone}
                      </p>
                      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                        account.status === 'ENABLED' 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {account.status}
                      </span>
                    </div>
                    <Button variant="outline" size="sm">
                      View Campaigns
                    </Button>
                  </div>
                ))}

                <Button 
                  onClick={fetchAccounts} 
                  variant="outline" 
                  disabled={loading}
                  className="w-full"
                >
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Refreshing...
                    </>
                  ) : (
                    'Refresh Accounts'
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Next Steps */}
        {connected && (
          <Card>
            <CardHeader>
              <CardTitle>🎉 Ready for AI Optimization!</CardTitle>
              <CardDescription>
                Your Google Ads account is connected. Here's what you can do next:
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button variant="outline" className="h-auto p-4 flex flex-col items-center space-y-2">
                  <BarChart3 className="h-6 w-6" />
                  <span>View Dashboard</span>
                </Button>
                <Button variant="outline" className="h-auto p-4 flex flex-col items-center space-y-2">
                  <Zap className="h-6 w-6" />
                  <span>Start Search Mining</span>
                </Button>
                <Button variant="outline" className="h-auto p-4 flex flex-col items-center space-y-2">
                  <Shield className="h-6 w-6" />
                  <span>Analyze Keywords</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  )
}