import React from 'react'
import { <PERSON>, <PERSON>Header, CardTitle, CardDescription } from '../components/ui/card'
import { Building, TrendingUp, DollarSign, Users } from 'lucide-react'
import { Button } from '../components/ui/button'

export const ClientsPage = () => {
  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-playfair font-bold text-gray-900">All Clients</h1>
          <p className="text-gray-600 mt-1">Manage your client accounts and campaigns</p>
        </div>
        <Button variant="primary">
          <Building className="h-4 w-4 mr-2" />
          Add Client
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader>
            <CardDescription>Total Clients</CardDescription>
            <CardTitle className="text-2xl font-bold">24</CardTitle>
          </CardHeader>
        </Card>
        
        <Card>
          <CardHeader>
            <CardDescription>Active Campaigns</CardDescription>
            <CardTitle className="text-2xl font-bold">156</CardTitle>
          </CardHeader>
        </Card>

        <Card>
          <CardHeader>
            <CardDescription>Monthly Spend</CardDescription>
            <CardTitle className="text-2xl font-bold">₹40.4L</CardTitle>
          </CardHeader>
        </Card>

        <Card>
          <CardHeader>
            <CardDescription>Avg. ROAS</CardDescription>
            <CardTitle className="text-2xl font-bold">3.8x</CardTitle>
          </CardHeader>
        </Card>
      </div>

      {/* Client List */}
      <Card>
        <CardHeader>
          <CardTitle>Client Accounts</CardTitle>
          <CardDescription>
            View and manage all client accounts
          </CardDescription>
        </CardHeader>
        <div className="p-6">
          <div className="text-center py-12 text-gray-500">
            <Users className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <p className="text-lg font-medium">No clients found</p>
            <p className="text-sm mt-2">Connect your Google Ads MCC account to view clients</p>
          </div>
        </div>
      </Card>
    </div>
  )
}

export default ClientsPage