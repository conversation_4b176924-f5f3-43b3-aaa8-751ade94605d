import React from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from '../components/ui/card'
import { Button } from '../components/ui/button'
import { Bell, AlertCircle, CheckCircle, Info, Settings, Plus } from 'lucide-react'

export const MonitoringPage = () => {
  const alerts = [
    {
      id: 1,
      type: 'warning',
      title: 'Budget Exhaustion Alert',
      description: 'Campaign "Summer Sale 2025" is at 95% of daily budget',
      time: '5 minutes ago',
      icon: AlertCircle,
      color: 'text-amber-600 bg-amber-50'
    },
    {
      id: 2,
      type: 'info',
      title: 'Quality Score Improvement',
      description: 'Keyword "running shoes" improved from 6/10 to 8/10',
      time: '1 hour ago',
      icon: CheckCircle,
      color: 'text-emerald-600 bg-emerald-50'
    },
    {
      id: 3,
      type: 'info',
      title: 'New Search Terms Available',
      description: '23 new search terms detected for review',
      time: '2 hours ago',
      icon: Info,
      color: 'text-blue-600 bg-blue-50'
    }
  ]

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Monitoring & Alerts</h1>
        <p className="text-gray-600">Real-time monitoring and alert management for your campaigns</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Active Alerts</p>
                <p className="text-2xl font-bold">3</p>
              </div>
              <Bell className="h-8 w-8 text-amber-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Monitoring Rules</p>
                <p className="text-2xl font-bold">12</p>
              </div>
              <Settings className="h-8 w-8 text-primary-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Resolved Today</p>
                <p className="text-2xl font-bold">8</p>
              </div>
              <CheckCircle className="h-8 w-8 text-emerald-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Critical Alerts</p>
                <p className="text-2xl font-bold">0</p>
              </div>
              <AlertCircle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="mb-6 flex justify-between items-center">
        <h2 className="text-xl font-semibold">Recent Alerts</h2>
        <Button variant="primary">
          <Plus className="h-4 w-4 mr-2" />
          Create Alert Rule
        </Button>
      </div>

      <div className="space-y-4">
        {alerts.map((alert) => {
          const Icon = alert.icon
          return (
            <Card key={alert.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-4">
                <div className="flex items-start gap-4">
                  <div className={`p-2 rounded-lg ${alert.color}`}>
                    <Icon className="h-5 w-5" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold mb-1">{alert.title}</h3>
                    <p className="text-sm text-gray-600 mb-2">{alert.description}</p>
                    <p className="text-xs text-gray-500">{alert.time}</p>
                  </div>
                  <Button variant="outline" size="sm">
                    View Details
                  </Button>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Alert Rules</CardTitle>
          <CardDescription>Configure monitoring rules and thresholds</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h4 className="font-medium">Budget Exhaustion</h4>
                <p className="text-sm text-gray-600">Alert when budget reaches 90%</p>
              </div>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h4 className="font-medium">Quality Score Drop</h4>
                <p className="text-sm text-gray-600">Alert when QS drops below 5</p>
              </div>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default MonitoringPage