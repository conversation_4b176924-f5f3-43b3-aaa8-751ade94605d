import { useState, useEffect } from 'react'
import { useAuth } from '../contexts/AuthContext.jsx'

export const useOnboarding = () => {
  const { user } = useAuth()
  const [hasCompletedOnboarding, setHasCompletedOnboarding] = useState(true)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const checkOnboardingStatus = () => {
      if (!user) {
        setIsLoading(false)
        return
      }

      // Check localStorage for onboarding completion
      const onboardingKey = `onboarding_completed_${user.id}`
      const completed = localStorage.getItem(onboardingKey)
      
      // If no record exists, this is a new user
      if (!completed) {
        setHasCompletedOnboarding(false)
      } else {
        setHasCompletedOnboarding(true)
      }
      
      setIsLoading(false)
    }

    checkOnboardingStatus()
  }, [user])

  const completeOnboarding = () => {
    if (user) {
      const onboardingKey = `onboarding_completed_${user.id}`
      localStorage.setItem(onboardingKey, 'true')
      setHasCompletedOnboarding(true)
    }
  }

  const resetOnboarding = () => {
    if (user) {
      const onboardingKey = `onboarding_completed_${user.id}`
      localStorage.removeItem(onboardingKey)
      setHasCompletedOnboarding(false)
    }
  }

  return {
    hasCompletedOnboarding,
    isLoading,
    completeOnboarding,
    resetOnboarding
  }
}