import { useState, useEffect } from 'react'
import { fetchWithAuth } from '../lib/api.js'

export const useDashboard = () => {
  const [stats, setStats] = useState(null)
  const [campaigns, setCampaigns] = useState([])
  const [recentActivity, setRecentActivity] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  const fetchDashboardData = async () => {
    try {
      setLoading(true)
      setError(null)

      // Fetch all dashboard data in parallel
      const [statsRes, campaignsRes, activityRes] = await Promise.all([
        fetchWithAuth('/api/dashboard/stats'),
        fetchWithAuth('/api/dashboard/campaigns/overview'),
        fetchWithAuth('/api/dashboard/recent-activity')
      ])

      if (!statsRes.ok || !campaignsRes.ok || !activityRes.ok) {
        throw new Error('Failed to fetch dashboard data')
      }

      const [statsData, campaignsData, activityData] = await Promise.all([
        statsRes.json(),
        campaignsRes.json(),
        activityRes.json()
      ])

      setStats(statsData)
      setCampaigns(campaignsData.campaigns || [])
      setRecentActivity(activityData.activities || [])
    } catch (err) {
      console.error('Dashboard fetch error:', err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchDashboardData()
  }, [])

  return {
    stats,
    campaigns,
    recentActivity,
    loading,
    error,
    refetch: fetchDashboardData
  }
}