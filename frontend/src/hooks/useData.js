import { useState, useEffect } from 'react'

export function useData(url, options = {}) {
  const [data, setData] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)

  const { enabled = true } = options

  useEffect(() => {
    if (!url || !enabled) {
      return
    }

    let ignore = false
    setLoading(true)
    setError(null)

    const fetchData = async () => {
      try {
        const response = await fetch(url)
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
        const result = await response.json()
        
        if (!ignore) {
          setData(result)
        }
      } catch (err) {
        if (!ignore) {
          setError(err instanceof Error ? err : new Error('An error occurred'))
        }
      } finally {
        if (!ignore) {
          setLoading(false)
        }
      }
    }

    fetchData()

    return () => {
      ignore = true
    }
  }, [url, enabled])

  return { data, loading, error }
}