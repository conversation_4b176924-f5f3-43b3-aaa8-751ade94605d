import { useState, useEffect } from 'react'
import { googleAdsApi } from '../lib/api.js'

export const useGoogleAdsAccounts = () => {
  const [accounts, setAccounts] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  const fetchAccounts = async () => {
    try {
      setLoading(true)
      setError(null)
      const data = await googleAdsApi.getAccounts()
      setAccounts(data || [])
    } catch (err) {
      // Failed to fetch Google Ads accounts
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchAccounts()
  }, [])

  return {
    accounts,
    loading,
    error,
    refetch: fetchAccounts
  }
}