@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS Variables */
:root {
  --c-primary-50: #F4F7FF;
  --c-primary-100: #E7EEFF;
  --c-primary-300: #B9CBFF;
  --c-primary-500: #4172F5;
  --c-primary-600: #3E5CE7;
  --c-primary-700: #324ECF;
  --c-primary-900: #07153F;

  --c-secondary: #444751;
  --c-text: #6F7176;
  --c-grey-100: #F3F6FA;
  --c-grey-400: #6F7176;
  --c-grey-900: #1A1E29;

  --c-gold-500: #FECD79;
  --c-success: #27C084;
  --c-error: #EF5E5E;

  /* Font Families */
  --font-primary: 'Inter', sans-serif;
}

/* Base layer overrides */
@layer base {
  body {
    @apply antialiased;
  }
}

/* Custom component styles */
@layer components {
  .btn-primary {
    @apply bg-primary-600 text-white px-4 py-2 rounded-lg font-medium 
           hover:bg-primary-700 transition-colors duration-200
           focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .btn-secondary {
    @apply bg-transparent text-primary-600 px-4 py-2 rounded-lg font-medium
           border-2 border-primary-600 hover:bg-primary-50 transition-colors duration-200
           focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .card {
    @apply bg-white rounded-lg border border-slate-200 shadow-sm;
  }

  .card-interactive {
    @apply hover:shadow-md transition-shadow duration-200 cursor-pointer;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-primary-600 to-primary-700 bg-clip-text text-transparent;
  }

  .bg-brand-gradient {
    background-image: linear-gradient(135deg, #4172F5 0%, #285CF7 100%);
  }
}

/* Utilities layer */
@layer utilities {
  .animate-in {
    animation: animateIn 0.2s ease-out;
  }
  
  .fade-in {
    animation: fadeIn 0.2s ease-out;
  }
}

@keyframes animateIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}