{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@supabase/supabase-js": "^2.49.9", "axios": "^1.9.0", "lucide-react": "^0.515.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-error-boundary": "^6.0.0", "react-router-dom": "^7.6.3", "recharts": "^2.15.3", "zod": "^4.0.2"}, "devDependencies": {"@eslint/js": "^9.25.0", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.6", "rollup-plugin-visualizer": "^6.0.3", "tailwindcss": "^3.4.17", "vite": "^6.3.5"}}