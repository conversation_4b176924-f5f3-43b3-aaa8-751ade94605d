# Google Ads AI Optimization Platform
## Brand Wisdom Solutions

AI-powered platform for managing and optimizing multiple Google Ads accounts.

<img src="frontend/public/assets/Brandwisdomlogo-1.webp" alt="Brand Wisdom Solutions" height="40">

## Features

- ��� AI-powered optimization recommendations
- ��� Multi-account dashboard
- ��� One-click implementation
- ��� Performance analytics
- ��� Real-time alerts

## Tech Stack

- **Frontend**: Vite + React + TypeScript + Tailwind CSS
- **Backend**: FastAPI + Python
- **Database**: Supabase (PostgreSQL)
- **AI/ML**: LangChain + OpenAI/Anthropic
- **Task Queue**: Celery + Redis
- **Infrastructure**: Docker + Vercel + Railway

## Brand Guidelines

This project follows the Brand Wisdom Solutions style guide:
- Primary Color: #3E5CE7
- Typography: <PERSON><PERSON> (body), Playfair Display (headings)
- 8pt spacing grid system

## Getting Started

### Prerequisites

- Node.js 18+
- Python 3.12+
- Docker & Docker Compose
- Supabase account

### Quick Start

1. Clone and setup environment files
2. Add your logo: `frontend/public/assets/Brandwisdomlogo-1.webp`
3. Configure Supabase credentials
4. Run with Docker: `docker compose up`
5. Access:
   - Frontend: http://localhost:5173
   - Backend: http://localhost:8000
   - API Docs: http://localhost:8000/docs
   - Flower: http://localhost:5555

## License

© 2025 Brand Wisdom Solutions - All rights reserved
