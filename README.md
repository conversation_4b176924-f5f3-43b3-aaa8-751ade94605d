# AdsAI Platform - Google Ads Campaign Management
**Brand Wisdom Solutions - Internal Agency Tool**

## Quick Overview
AdsAI is an internal platform for Brand Wisdom Solutions to manage 20+ client Google Ads accounts through their MCC (************). The platform combines traditional campaign management with AI-powered optimization features.

## Key Features
- **Multi-Client Management**: Unified dashboard for all client accounts
- **Campaign Operations**: Create and manage all Google Ads campaign types
- **AI-Powered Optimization**: Search query mining, ad copy generation, bid intelligence
- **Performance Reporting**: Automated client reports and analytics
- **Team Collaboration**: Shared workflows and standardized processes

## Documentation
**🧹 Clean, Consolidated Documentation Structure**
- **[PROJECT_PRD.md](PROJECT_PRD.md)** - Complete product requirements and features (consolidated from 5+ files)
- **[TECH_STACK.md](TECH_STACK.md)** - Technical architecture and setup details (consolidated from 4+ files)
- **[API_REQUIREMENTS.md](API_REQUIREMENTS.md)** - Google Ads API integration guide (consolidated from 3+ files)
- **[docs/api-docs/GOOGLE_ADS_API_APPLICATION.md](docs/api-docs/GOOGLE_ADS_API_APPLICATION.md)** - Original API application (approved)
- **[PRD Files/Brand-Style-Guide.md](PRD Files/Brand-Style-Guide.md)** - Brand guidelines and assets
- **[api-application-mockups/](api-application-mockups/)** - Working HTML mockups with all features

## Quick Start
```bash
# Frontend (React + Vite)
cd frontend
npm install
npm run dev          # localhost:5173

# Backend (FastAPI)
cd backend
source venv/bin/activate
python3 run_server.py    # localhost:8000
```

## Project Status
- **API Access**: ✅ Approved (Google Ads API Basic Access)
- **Development**: Active on `new-version` branch
- **Documentation**: 🧹 Consolidated and cleaned up
- **Frontend**: React 19.1.0 + Tailwind CSS 3.4.0
- **Backend**: FastAPI 0.104.1 + Supabase
- **Database**: 57 tables deployed on Supabase
- **Project Structure**: Clean and organized for focused development

## Contact
- **Website**: https://brandwisdom.in/
- **Email**: <EMAIL>
- **MCC Account**: ************

## Architecture
- **Frontend**: React + Vite (simplified, no TypeScript)
- **Backend**: FastAPI + Supabase integration
- **Database**: Supabase PostgreSQL
- **API**: Google Ads API for campaign management
- **Deployment**: Ready for Vercel (frontend) + Railway (backend)