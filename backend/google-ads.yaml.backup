# Google Ads API Configuration
developer_token: USJoZ_CN_pYY2MP-jlhjqA

# OAuth2 Credentials
client_id: *************-2hsoao5p2sbdnfa2hi1tkvq6ulf3hofs.apps.googleusercontent.com
client_secret: 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

# Refresh token (will be set dynamically from user OA<PERSON>)
refresh_token: PLACEHOLDER_WILL_BE_SET_DYNAMICALLY

# For Test Account Access
login_customer_id: **********  # Test Manager ID

# Optional settings
use_proto_plus: True