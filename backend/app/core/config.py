"""
Simplified configuration for Google Ads AI Platform
"""

from pydantic_settings import BaseSettings
from typing import List
from pathlib import Path

class Settings(BaseSettings):
    # Core settings
    PROJECT_NAME: str = "Google Ads AI Platform"
    ENVIRONMENT: str = "development"
    
    # CORS
    CORS_ORIGINS: List[str] = [
        "http://localhost:5173",
        "http://localhost:3000",
        "http://127.0.0.1:5173",
        "http://127.0.0.1:3000"
    ]
    
    # Supabase (Required)
    SUPABASE_URL: str
    SUPABASE_KEY: str
    SUPABASE_SERVICE_KEY: str
    
    # Google Ads Configuration
    GOOGLE_ADS_DEVELOPER_TOKEN: str = "USJoZ_CN_pYY2MP-jlhjqA"
    GOOGLE_ADS_CLIENT_ID: str = "*************-qj2cokqle91qjnb9ulba5n5fqild9m8f.apps.googleusercontent.com"
    GOOGLE_ADS_CLIENT_SECRET: str = "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"
    # Login customer ID will be set dynamically based on the authenticated user's manager account
    GOOGLE_ADS_LOGIN_CUSTOMER_ID: str = ""
    
    # URLs
    GOOGLE_OAUTH_REDIRECT_URI: str = "http://localhost:8000/api/google-ads/auth/callback"
    FRONTEND_URL: str = "http://localhost:5173"
    
    class Config:
        env_file = Path(__file__).parent.parent.parent / ".env"
        case_sensitive = True
        extra = "ignore"  # Ignore extra fields in .env

settings = Settings()