#!/usr/bin/env python3
"""Quick test to verify the setup is working"""

import sys
import os
import json
import time
import subprocess # Not used, can be removed
from pathlib import Path # For more robust path handling

# Determine project root assuming this script is in backend/scripts/diagnostics/
project_root = Path(__file__).parent.parent.parent.parent
backend_root_for_sys_path = project_root / 'backend'
sys.path.insert(0, str(backend_root_for_sys_path))

print("🔍 Testing Google Ads AI Platform Setup...")
print("=" * 50)

# Test 1: Check configuration files
print("\n1️⃣ Checking configuration files...")
backend_dir = project_root / 'backend' # Corrected path relative to project_root

# Check credentials.json
creds_file = backend_dir / 'credentials.json' # Use Path object
if creds_file.exists():
    print("✅ credentials.json found")
else:
    print(f"❌ credentials.json NOT found at {creds_file}")

# Check .env
env_file = backend_dir / '.env' # Use Path object
if env_file.exists():
    print("✅ .env file found")
    with open(env_file, 'r') as f:
        env_content = f.read()
        if 'USJoZ_CN_pYY2MP-jlhjqA' in env_content:
            print("✅ Developer token configured correctly")
        if '************' in env_content:
            print("✅ Test Manager ID configured correctly")
else:
    print(f"❌ .env file NOT found at {env_file}")

# Test 2: Try importing backend modules
print("\n2️⃣ Testing Python imports...")
try:
    from app.main import app # app.main should be importable due to sys.path modification
    from app.core.config import settings
    print("✅ Backend imports successful")
    print(f"   Project: {settings.PROJECT_NAME}")
    print(f"   Supabase: {settings.SUPABASE_URL}")
except Exception as e:
    print(f"❌ Import error: {e}")
    print("   Run: cd backend && pip install -r requirements.txt (ensure venv is active)")

# Test 3: Check frontend
print("\n3️⃣ Checking frontend...")
frontend_dir = project_root / 'frontend' # Corrected path
package_json = frontend_dir / 'package.json'
if package_json.exists():
    print("✅ Frontend package.json found")
    node_modules = frontend_dir / 'node_modules'
    if node_modules.exists():
        print("✅ Frontend dependencies installed")
    else:
        print("⚠️  Frontend dependencies not installed")
        print("   Run: cd frontend && npm install")
else:
    print(f"❌ Frontend package.json NOT found at {package_json}")


# Test 4: Test backend server
print("\n4️⃣ Testing backend server...")
print("   Starting test server on port 8001...")
server_started_successfully = False
try:
    # Try to start a test instance
    import uvicorn
    import threading
    import requests
    
    # Ensure 'app' is available from the import test
    if 'app' not in locals() and 'app' not in globals():
        raise ImportError("FastAPI 'app' object not imported successfully for server test.")

    def run_server():
        # Ensure uvicorn runs from the context of the backend directory
        # or that app.main:app is correctly resolvable.
        # The sys.path modification should help, but for uvicorn CLI-like behavior,
        # it's often best if uvicorn is run with cwd as backend_dir or app_module specified fully.
        # For programmatic uvicorn.run, passing the app object directly is fine.
        uvicorn.run(app, host="127.0.0.1", port=8001, log_level="error")
    
    server_thread = threading.Thread(target=run_server, daemon=True)
    server_thread.start()
    
    # Wait for server to start
    time.sleep(3) # Increased sleep slightly
    
    # Test the API - changed to /health
    health_endpoint = "http://127.0.0.1:8001/health"
    print(f"   Pinging {health_endpoint}...")
    response = requests.get(health_endpoint)
    if response.status_code == 200:
        print(f"✅ Backend API is working! (Status: {response.status_code})")
        server_started_successfully = True
    else:
        print(f"❌ Backend API returned error (Status: {response.status_code}) from {health_endpoint}")
        try:
            print(f"   Response: {response.json()}")
        except requests.exceptions.JSONDecodeError:
            print(f"   Response: {response.text}")
        
except ImportError as ie:
    print(f"⚠️  Could not test backend due to import error: {ie}")
    print("   Ensure backend dependencies are installed and imports are correct.")
except Exception as e:
    print(f"⚠️  Could not test backend: {e}")
    print("   Test manually after starting the server using 'python backend/run_server.py'")


# Summary
print("\n" + "=" * 50)
print("📋 SETUP SUMMARY")
print("=" * 50)
print("\n✅ Configuration:")
print(f"   Developer Token: USJoZ_CN_pYY2MP-jlhjqA") # Assuming this is static for test display
print(f"   Test Manager: ************")
print(f"   OAuth Client (verified): ...qj2cokqle91qjnb9ulba5n5fqild9m8f") # Updated to current

print("\n📝 To run the application (Manual method recommended):")
print("   Terminal 1 (Backend):")
print("     cd backend")
print("     source venv/Scripts/activate  # (or source venv/bin/activate for Mac/Linux)")
print("     python run_server.py")
print("   Terminal 2 (Frontend):")
print("     cd frontend")
print("     npm run dev")
print("   (See README.md or docs/QUICK_START.md for more details and alternative run scripts like run-app.sh/bat)")


print("\n🧪 Test accounts ready:")
print("   Manager: <EMAIL>")
print("   Clients: 3 test accounts available")

print("\n" + "=" * 50)
if server_started_successfully:
    print("✅ Basic backend server test passed.")
else:
    print("❌ Backend server test encountered issues or could not run.")
