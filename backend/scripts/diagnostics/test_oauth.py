#!/usr/bin/env python3
"""
Test script to verify Google Ads OAuth URL generation
"""
import sys
import os
from pathlib import Path

# Determine project root assuming this script is in backend/scripts/diagnostics/
project_root = Path(__file__).resolve().parent.parent.parent
backend_dir_for_sys_path = project_root / "backend"
sys.path.insert(0, str(backend_dir_for_sys_path))

# Load environment variables from backend/.env
# This is mainly for the os.getenv calls below for printing.
# GoogleAdsService itself will use pydantic_settings to load from backend/.env
from dotenv import load_dotenv
env_path = backend_dir_for_sys_path / ".env"
if env_path.exists():
    load_dotenv(env_path)
else:
    print(f"Warning: .env file not found at {env_path}")

def test_oauth_url_generation():
    """Test if we can generate a Google Ads OAuth URL"""
    try:
        # This import should work due to sys.path modification
        from app.services.google_ads import GoogleAdsService
        
        print("🔧 Testing Google Ads OAuth URL Generation...")
        print("-" * 50)
        
        # Check environment variables (as seen by os.getenv after dotenv attempt)
        print("📋 Environment Variables (from os.getenv after dotenv load):")
        print(f"  GOOGLE_ADS_CLIENT_ID: {os.getenv('GOOGLE_ADS_CLIENT_ID', 'NOT SET')}")
        print(f"  GOOGLE_ADS_CLIENT_SECRET: {'SET' if os.getenv('GOOGLE_ADS_CLIENT_SECRET') else 'NOT SET'}")
        print(f"  GOOGLE_ADS_DEVELOPER_TOKEN: {'SET' if os.getenv('GOOGLE_ADS_DEVELOPER_TOKEN') else 'NOT SET'}")
        # WISEADS_REDIRECT_URI is old, GOOGLE_OAUTH_REDIRECT_URI is current in .env
        print(f"  GOOGLE_OAUTH_REDIRECT_URI (expected by service): {os.getenv('GOOGLE_OAUTH_REDIRECT_URI', 'NOT SET')}") 
        print()
        
        # Initialize service (this will use pydantic_settings from app.core.config)
        # Change current working directory to backend for GoogleAdsService if it relies on relative paths for credentials.json
        original_cwd = Path.cwd()
        os.chdir(backend_dir_for_sys_path)
        print(f"Temporarily changed CWD to: {Path.cwd()} for service initialization")
        service = GoogleAdsService()
        os.chdir(original_cwd) # Change back
        print(f"Restored CWD to: {Path.cwd()}")
        print("✅ GoogleAdsService initialized successfully (using its internal settings)")
        
        # Generate OAuth URL
        state = "test-state-123"
        auth_url = service.get_oauth_url(state=state)
        
        print("✅ OAuth URL generated successfully!")
        print(f"🔗 Authorization URL: {auth_url}")
        print()
        print("📝 URL Components:")
        from urllib.parse import urlparse, parse_qs
        parsed = urlparse(auth_url)
        params = parse_qs(parsed.query)
        
        for key, value in params.items():
            print(f"  {key}: {value[0] if value else 'None'}")
        
        print()
        print("🎉 OAuth URL generation test PASSED!")
        print("✅ You can use this URL to test the OAuth flow manually")
        
        return True
        
    except Exception as e:
        print(f"❌ OAuth URL generation test FAILED!")
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Ensure the script is run from project root or adjust paths accordingly if run from elsewhere
    print(f"Running 'test_oauth.py' from: {Path.cwd()}")
    print(f"Attempting to use backend/.env from: {env_path}")
    
    success = test_oauth_url_generation()
    sys.exit(0 if success else 1)
