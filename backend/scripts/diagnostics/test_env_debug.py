#!/usr/bin/env python3
"""
Debug script to check environment variables for Google Ads API
"""

import os
import sys
from pathlib import Path

# Determine project root assuming this script is in backend/scripts/diagnostics/
project_root = Path(__file__).resolve().parent.parent.parent
backend_dir_for_sys_path = project_root / "backend"
sys.path.insert(0, str(backend_dir_for_sys_path))

# Load environment variables from backend/.env
# This is mainly for the os.getenv calls below for printing.
# GoogleAdsService itself will use pydantic_settings to load from backend/.env
from dotenv import load_dotenv
env_path = backend_dir_for_sys_path / ".env"
if env_path.exists():
    load_dotenv(env_path)
else:
    print(f"Warning: .env file not found at {env_path}")


def check_env_vars():
    """Check all required environment variables"""
    print("🔍 Environment Variables Debug")
    print("=" * 50)
    
    # Required variables
    required_vars = [
        "GOOGLE_ADS_DEVELOPER_TOKEN",
        "GOOGLE_ADS_CLIENT_ID", 
        "GOOGLE_ADS_CLIENT_SECRET",
        "GOOGLE_ADS_LOGIN_CUSTOMER_ID",
        "GOOGLE_ADS_CREDENTIALS_PATH",
        "GOOGLE_OAUTH_REDIRECT_URI"
    ]
    
    all_set = True
    print("📋 Environment Variables (from os.getenv after dotenv load):")
    for var in required_vars:
        value = os.getenv(var)
        if value:
            if "SECRET" in var or "TOKEN" in var: # Basic obfuscation for secrets/tokens
                display_value = f"SET (length: {len(value)})"
            else:
                display_value = value
            print(f"✅ {var}: {display_value}")
        else:
            print(f"❌ {var}: NOT SET")
            all_set = False
    
    print("\n🔍 File System Check")
    print("=" * 50)
    
    # Check credentials file
    # GOOGLE_ADS_CREDENTIALS_PATH from .env is relative to the backend directory
    creds_path_from_env = os.getenv("GOOGLE_ADS_CREDENTIALS_PATH", "credentials.json") 
    # Construct full path from backend_dir_for_sys_path
    full_creds_path = backend_dir_for_sys_path / creds_path_from_env
    
    print(f"📁 Script Working Directory: {Path.cwd()}") # Where this script is run from
    print(f"🎯 Assumed Project Root: {project_root}")
    print(f"📁 Assumed Backend Path: {backend_dir_for_sys_path}")
    print(f"📄 Credentials Path (from .env): {creds_path_from_env}")
    print(f"📄 Credentials Path (calculated full): {full_creds_path}")
    
    file_exists = full_creds_path.exists()
    print(f"📄 File Exists: {'✅ YES' if file_exists else '❌ NO'}")
    
    if file_exists:
        print(f"📄 File Size: {full_creds_path.stat().st_size} bytes")
    
    print("\n🧪 Test GoogleAdsService Initialization")
    print("=" * 50)
    
    original_cwd = Path.cwd()
    try:
        # Change to backend directory for service initialization,
        # as GoogleAdsService might use relative paths for credentials.json
        # and pydantic-settings in config.py expects .env to be in CWD or its default path.
        os.chdir(backend_dir_for_sys_path)
        print(f"📁 Temporarily changed CWD to: {Path.cwd()} for service initialization")
        
        from app.services.google_ads import GoogleAdsService # Import should work due to sys.path
        service = GoogleAdsService() # This will use its internal settings (pydantic_settings)
        print("✅ GoogleAdsService initialized successfully!")
        
        # Test OAuth URL generation
        print("\n🔗 Testing OAuth URL Generation...")
        auth_url = service.get_oauth_url(state="test-state")
        print(f"✅ OAuth URL generated successfully!")
        print(f"🔗 URL: {auth_url[:100]}...") # Print only a part of the URL
        
    except Exception as e:
        print(f"❌ Error during GoogleAdsService test: {e}")
        import traceback
        traceback.print_exc()
    finally:
        os.chdir(original_cwd) # Change back CWD
        print(f"📁 Restored CWD to: {Path.cwd()}")
    
    return all_set

if __name__ == "__main__":
    print(f"Running 'test_env_debug.py' from: {Path.cwd()}")
    print(f"Attempting to use backend/.env from: {env_path}")
    check_env_vars()
