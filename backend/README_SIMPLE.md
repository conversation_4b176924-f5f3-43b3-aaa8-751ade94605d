# Simplified Google Ads AI Platform Backend

A minimal FastAPI backend for the Google Ads AI Platform.

## Quick Start

1. **Install dependencies:**
   ```bash
   pip install -r requirements_simple.txt
   ```

2. **Set up environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your Supabase credentials
   ```

3. **Run the server:**
   ```bash
   python run_simple.py
   # or
   uvicorn app.main_simple:app --reload
   ```

## Features

- **Single file architecture** - Everything in `main_simple.py`
- **Supabase authentication** - JWT verification
- **Google Ads OAuth flow** - Works with mock data if not configured
- **Minimal dependencies** - Only essential packages

## API Endpoints

### Health
- `GET /` - Root endpoint
- `GET /health` - Health check

### Authentication
- `GET /api/test` - Test endpoint (no auth)
- `GET /api/user/profile` - Get user profile (requires auth)

### Dashboard
- `GET /api/dashboard/stats` - Dashboard statistics
- `GET /api/dashboard/recent-activity` - Recent activities
- `GET /api/dashboard/campaigns/overview` - Campaigns overview

### Google Ads
- `GET /api/google-ads/auth/url` - Get OAuth URL
- `GET /api/google-ads/auth/callback` - OAuth callback
- `GET /api/google-ads/accounts` - List accounts (mock data)
- `GET /api/google-ads/accounts/{id}/campaigns` - Get campaigns (mock data)
- `GET /api/google-ads/accounts/{id}/search-analysis` - Search term analysis (mock data)

## Environment Variables

Required:
- `SUPABASE_URL` - Your Supabase project URL
- `SUPABASE_KEY` - Supabase anon key
- `SUPABASE_SERVICE_KEY` - Supabase service key

Optional (for Google Ads):
- `GOOGLE_ADS_CLIENT_ID` - OAuth client ID
- `GOOGLE_ADS_CLIENT_SECRET` - OAuth client secret
- `GOOGLE_ADS_DEVELOPER_TOKEN` - Developer token
- `GOOGLE_ADS_LOGIN_CUSTOMER_ID` - Login customer ID

## Development

The backend will work with mock data if Google Ads credentials are not configured. This allows for frontend development without needing a Google Ads account.