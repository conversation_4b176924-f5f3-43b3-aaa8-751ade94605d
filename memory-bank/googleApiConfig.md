# Google API Configuration: Google Ads AI Campaign Management Platform

## Google Ads API Basic Access Application Status
**Status**: ✅ **SUBMITTED** - Application submitted with comprehensive platform details
**Application Date**: December 2024
**Scope**: Campaign Creation, Campaign Management, Reporting, Keyword Planning Services

### Application Details Submitted:
- **Company**: Brand Wisdom Solutions (Digital Marketing Agency)
- **Website**: https://brandwisdom.in/
- **Business Model**: Managing 20+ client accounts through MCC structure
- **Campaign Types Supported**: Search Network, Display Network, Shopping, Performance Max, Local Services, Call-only, Demand Gen, Display Expansion on Search
- **Tool Purpose**: Internal AI-powered platform for agency staff to enhance service delivery

## Google Ads Manager Account Configuration

### Brand Wisdom Solutions - Manager Account
**Status**: ✅ **ACTIVE MANAGER ACCOUNT**

| Field | Value |
|-------|-------|
| **Account Name** | Brand Wisdom Solutions - Manager |
| **Account ID** | `310-946-3592` |
| **Account Type** | Manager Account |
| **User Email** | <EMAIL> |
| **Date Range** | May 7 - Jun 3, 2025 |
| **Status** | Active |

### Test Manager Account Configuration
**Status**: ✅ **ACTIVE TEST ENVIRONMENT**

| Field | Value |
|-------|-------|
| **Account Name** | Test Manager |
| **Account ID** | `605-234-4141` |
| **Account Type** | Test Manager Account |
| **User Email** | <EMAIL> |
| **Currency** | Indian Rupees (₹) |
| **Current Date** | June 4, 2025 |
| **Total Client Accounts** | 3 |

#### Managed Test Accounts
1. **Test Client Account 1**
   - Account ID: `690-798-5586`
   - Status: Active
   - Performance: All metrics at 0 (test environment)

2. **Test Client Account 2**
   - Account ID: `406-000-1758`
   - Status: Active
   - Performance: All metrics at 0 (test environment)

3. **Test Dental Clinic**
   - Account ID: `788-496-2297`
   - Status: Active
   - Performance: All metrics at 0 (test environment)

## OAuth 2.0 Client Configuration

### Current Active Web Application Client (VERIFIED)
**Status**: ✅ **VERIFIED IN GOOGLE CLOUD** - OAuth client confirmed active June 4, 2025

| Field | Value |
|-------|-------|
| **Client Name** | Google Ads AI Platform Web Client |
| **Client Type** | Web application |
| **Project ID** | `gothic-doodad-437305-f7` |
| **Client ID** | `*************-qj2cokqle91qjnb9ulba5n5fqild9m8f.apps.googleusercontent.com` |
| **Client Secret** | `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` |
| **Created** | June 4, 2025 |
| **Status** | ✅ Active in Google Cloud Console |
| **Verification** | ✅ Confirmed by user in Google Cloud setup |

### Legacy Web Application Client (OLD)
**Status**: 🔄 **REPLACED** - Do not use

| Field | Value |
|-------|-------|
| **Client Name** | Google ads AI 2.0 |
| **Client Type** | Web application |
| **Client ID (Console)** | `*************-18sm3igmedqgmc3ln5ctnpg4jgsrmvq1.apps.googleusercontent.com` |
| **Client ID (JSON)** | `*************-185m5ligmeu6gmcsfn5ctnpg4jg8mvq1.apps.googleusercontent.com` |
| **Issue** | Client ID discrepancy between console and JSON - REPLACED WITH NEW CLIENT |

### Complete OAuth 2.0 JSON Configuration (NEW)
**File**: ✅ **CONFIGURED** - `backend/credentials.json`

```json
{
  "web": {
    "client_id": "*************-qj2cokqle91qjnb9ulba5n5fqild9m8f.apps.googleusercontent.com",
    "project_id": "gothic-doodad-437305-f7",
    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
    "token_uri": "https://oauth2.googleapis.com/token",
    "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
    "client_secret": "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",
    "redirect_uris": [
      "http://localhost:8000/api/google-ads/auth/callback",
      "http://localhost:5173/auth/callback",
      "http://127.0.0.1:8000/api/google-ads/auth/callback"
    ],
    "javascript_origins": [
      "http://localhost:5173",
      "http://localhost:3000",
      "http://localhost:8000"
    ]
  }
}
```

**Status**: ✅ **FRESHLY CREATED** - New credentials file with correct OAuth client configuration

### OAuth Endpoints
| Endpoint | URL |
|----------|-----|
| **Auth URI** | `https://accounts.google.com/o/oauth2/auth` |
| **Token URI** | `https://oauth2.googleapis.com/token` |
| **Cert URL** | `https://www.googleapis.com/oauth2/v1/certs` |

### OAuth Testing Instructions (Updated June 15, 2025)
**Status**: ✅ **OAuth Flow Fully Functional**

#### How to Test OAuth:
1. **Start Both Servers**:
   ```bash
   # Terminal 1
   cd backend && python run_server.py
   
   # Terminal 2
   cd frontend && npm run dev
   ```

2. **Connect Google Ads**:
   - Click the dropdown in top navigation (shows "Connect Account")
   - Click "Connect Google Ads Account" button
   - You'll be redirected to Google OAuth consent screen
   - Authorize with a test user account
   - Returns to dashboard with connection confirmed

3. **OAuth Flow Status**:
   - ✅ OAuth URL generation working
   - ✅ Connect button properly triggers flow
   - ✅ Tokens stored in Supabase database
   - ✅ UI updates to show connected status

### Legacy Desktop Client
**Status**: 🔄 **LEGACY** - CLI/testing only

| Field | Value |
|-------|-------|
| **Client Name** | Test Google Ads AI 1 |
| **Client Type** | Desktop |
| **Client ID** | `*************-2hsoao5p2sbdnfa2hi1tkvq6ulf3hofs.apps.googleusercontent.com` |
| **Client Secret** | `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` |
| **Created** | April 23, 2025 |

## Google Ads API Credentials

### API Configuration
| Setting | Value |
|---------|-------|
| **Developer Token** | `USJoZ_CN_pYY2MP-jlhjqA` |
| **Access Level** | **Test Account** (⚠️ Need to apply for Basic Access) |
| **Status** | Token enabled for Test Account Access only |
| **Login Customer ID** | `605-234-4141` (Test Manager Account) |
| **Production Manager ID** | `310-946-3592` (Brand Wisdom Solutions) |
| **API Version** | Latest (v17+) |

### Developer Details
| Setting | Value |
|---------|-------|
| **API Contact Email** | <EMAIL> |
| **Company Name** | Brand Wisdom Solutions |
| **Company URL** | https://brandwisdom.in/ |
| **Company Type** | Agency/SEM |
| **Intended Use** | Ad creation/management and Reporting |
| **Principal Place of Business** | India |

## Google Cloud OAuth Configuration

### Project Information
| Setting | Value |
|---------|-------|
| **Project Name** | Google Ads AI Optimization |
| **Google Cloud Account** | <EMAIL> |
| **Platform** | Google Auth Platform |
| **Free Trial** | 20+ always-free products available |

### OAuth Consent Screen (VERIFIED)
| Setting | Value |
|---------|-------|
| **Publishing Status** | Testing (⚠️ Need to move to Production) |
| **User Type** | External |
| **User Cap** | 100 users (current: 2 test users) |
| **Status** | "Make Internal" option available |
| **Authorized Test Users** | `<EMAIL>`, `<EMAIL>` |
| **Authorized Domains** | Automatically added from redirect URIs |
| **Verification** | ✅ Confirmed active in Google Cloud Console |

### Important Limitations
- **Testing Mode**: Only test users can access the app
- **User Cap**: 100 user limit prior to app verification
- **Production Access**: Requires moving from Testing to Production status

### Required Scopes
| Scope | Purpose | Sensitivity |
|-------|---------|-------------|
| `https://www.googleapis.com/auth/adwords` | Full Google Ads account access | Sensitive |

## Authorized Redirect URIs

### Active Redirect URIs (UPDATED)
**Status**: ✅ **FRESHLY CONFIGURED** - New OAuth client redirect URIs

```
http://localhost:8000/api/google-ads/auth/callback    # Primary Backend OAuth callback - RECOMMENDED
http://localhost:5173/auth/callback                   # Frontend callback (if needed)
http://127.0.0.1:8000/api/google-ads/auth/callback   # Alternative backend callback
```

### JavaScript Origins (NEW)
```
http://localhost:5173    # Frontend (Vite dev server)
http://localhost:3000    # Alternative frontend port
http://localhost:8000    # Backend (FastAPI)
```

### Development Environment Ports
- **Port 5173**: Vite development server (active)
- **Port 3000**: Typical React/Node.js port (configured)
- **Port 8000**: Common API/backend port (active)
- **Port 80**: Default localhost (configured)

**Recommended**: Use `http://localhost:8000/auth/callback` for backend-handled OAuth flow.

### Security Warnings
- **Inactivity Warning**: OAuth clients deleted if unused for 6 months
- **Deletion Notice**: 30-day restoration period after deletion
- **Settings Update**: Changes may take 5 minutes to few hours to take effect

## Critical Configuration Notes

### Client ID Discrepancy Resolution
**Issue**: Two different Client IDs found in documentation
- **Console Display**: `*************-18sm3igmedqgmc3ln5ctnpg4jgsrmvq1.apps.googleusercontent.com`
- **JSON Credentials**: `*************-185m5ligmeu6gmcsfn5ctnpg4jg8mvq1.apps.googleusercontent.com`

**Resolution**: ✅ **UPDATED** - Using JSON credentials version in all configurations
- Backend `.env` file updated with correct Client ID
- Google Ads service configured with JSON credentials
- All OAuth flows will use the JSON file version

### Contact Information
- **Primary Developer**: <EMAIL>
- **API Contact**: <EMAIL>
- **Company Website**: https://brandwisdom.in/

## Environment Configuration

### Backend Environment Variables (VERIFIED June 4, 2025)
```bash
# Google Ads API Configuration
GOOGLE_ADS_DEVELOPER_TOKEN=USJoZ_CN_pYY2MP-jlhjqA
GOOGLE_ADS_CLIENT_ID=*************-qj2cokqle91qjnb9ulba5n5fqild9m8f.apps.googleusercontent.com
GOOGLE_ADS_CLIENT_SECRET=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
GOOGLE_ADS_CREDENTIALS_PATH=credentials.json

# Account Configuration
GOOGLE_ADS_LOGIN_CUSTOMER_ID=605-234-4141  # Test Manager Account
GOOGLE_ADS_PRODUCTION_MANAGER_ID=310-946-3592  # Brand Wisdom Solutions Manager

# OAuth Configuration
GOOGLE_OAUTH_REDIRECT_URI=http://localhost:8000/api/google-ads/auth/callback

# Optional: Refresh token (obtained after first auth)
GOOGLE_ADS_REFRESH_TOKEN=
```

**✅ VERIFIED**: Configuration matches Google Cloud OAuth client and backend .env file

### Frontend Environment Variables (Optional)
```bash
# Google OAuth (if implementing frontend auth flow)
VITE_GOOGLE_CLIENT_ID=*************-18sm3igmedqgmc3ln5ctnpg4jgsrmvq1.apps.googleusercontent.com
VITE_GOOGLE_REDIRECT_URI=http://localhost:5173/auth/callback
```

### Google Ads API Configuration File
Create `backend/google-ads.yaml`:

```yaml
developer_token: USJoZ_CN_pYY2MP-jlhjqA
client_id: *************-18sm3igmedqgmc3ln5ctnpg4jgsrmvq1.apps.googleusercontent.com
client_secret: 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
login_customer_id: 605-234-4141  # Test Manager Account
refresh_token: # Will be populated after OAuth flow
```

## OAuth 2.0 Implementation Pattern

### Authentication Flow
1. **Generate Authorization URL**: Backend creates OAuth URL with proper scopes
2. **User Authorization**: User grants permissions via Google OAuth consent
3. **Handle Callback**: Backend receives authorization code
4. **Exchange for Tokens**: Code exchanged for access/refresh tokens
5. **Store Refresh Token**: Securely store for API access
6. **Initialize Client**: Use tokens to create Google Ads API client

### Key Implementation Points
- **Scope**: `https://www.googleapis.com/auth/adwords` for full access
- **Access Type**: `offline` to receive refresh token
- **Prompt**: `consent` to ensure refresh token generation
- **State Parameter**: Include for CSRF protection
- **Token Storage**: Encrypt and store refresh tokens securely

## API Client Initialization

### Service Configuration
```python
from google.ads.googleads.client import GoogleAdsClient

client = GoogleAdsClient.load_from_dict({
    "developer_token": "USJoZ_CN_pYY2MP-jlhjqA",
    "client_id": "*************-185m5ligmeu6gmcsfn5ctnpg4jg8mvq1.apps.googleusercontent.com",
    "client_secret": "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",
    "refresh_token": user_refresh_token,
    "login_customer_id": "6052344141",
    "use_proto_plus": True
})
```

## Security Considerations

### Token Management
- **Refresh Token Storage**: Encrypt in database, never in logs
- **Access Token Rotation**: Implement automatic refresh logic
- **Scope Limitation**: Only request necessary permissions
- **Token Expiration**: Handle expired tokens gracefully

### API Security
- **Rate Limiting**: Respect Google Ads API quotas
- **Error Handling**: Comprehensive exception handling
- **Audit Logging**: Log all API interactions
- **User Permissions**: Map Google accounts to application users

## Testing Configuration

### Test Users
- **Primary**: `<EMAIL>`
- **Secondary**: `<EMAIL>`
- **Limitation**: 100 users maximum in testing mode

### Development Workflow
1. Use test users for OAuth flow testing
2. Verify token generation and storage
3. Test API client initialization
4. Validate account access and data retrieval
5. Test error handling and token refresh

## Production Readiness Requirements

### Critical Actions Needed for Production
1. **⚠️ Google Ads API Access Level**
   - **Current**: Test Account Access only
   - **Required**: Apply for Basic Access in "Access level" section
   - **Impact**: Cannot interact with production accounts until upgraded

2. **⚠️ OAuth Consent Screen Status**
   - **Current**: Testing mode (100 user cap)
   - **Required**: Move to Production status
   - **Impact**: Only test users can access app in testing mode

3. **⚠️ Account Configuration**
   - **Test Environment**: Use Test Manager Account (605-234-4141)
   - **Production Environment**: Use Brand Wisdom Manager (310-946-3592)
   - **Test Accounts Available**: 3 test client accounts ready for development

### Publishing Requirements
- **OAuth Consent Screen**: Must be verified for production
- **Domain Verification**: Required for non-localhost redirects
- **Privacy Policy**: Required for consent screen
- **Terms of Service**: Required for consent screen
- **App Verification**: May be required for sensitive scopes

### Scaling Considerations
- **Rate Limits**: Monitor API usage quotas
- **Token Management**: Implement token refresh queues
- **Error Handling**: Robust retry mechanisms
- **Monitoring**: Track API performance and errors

## Common Issues & Solutions

### Authentication Issues
- **Invalid Client**: Verify Client ID/Secret match exactly
- **Redirect URI Mismatch**: Ensure exact match with authorized URIs
- **Scope Issues**: Verify `adwords` scope is requested
- **Test User Access**: Ensure user is added to OAuth consent screen

### API Access Issues
- **Developer Token**: Verify token is active and valid
- **Customer ID**: Ensure login customer ID has proper access
- **Permissions**: Verify user has access to requested accounts
- **Rate Limits**: Implement exponential backoff for quota errors

## Monitoring & Maintenance

### Regular Tasks
- **Token Rotation**: Monitor refresh token validity
- **API Quota**: Track usage against limits
- **Error Rates**: Monitor authentication failures
- **User Access**: Audit user permissions regularly

### Performance Metrics
- **Authentication Success Rate**: Target 99%+
- **API Response Times**: Monitor for degradation
- **Error Rates**: Track and investigate spikes
- **Token Refresh Success**: Ensure automatic renewal works
