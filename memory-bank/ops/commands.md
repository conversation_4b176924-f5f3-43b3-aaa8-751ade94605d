# Commands: Google Ads AI Campaign Management Platform

## Development Commands

### Frontend Development
```bash
# Start frontend development server
cd frontend && npm run dev
# URL: http://localhost:5173

# Install frontend dependencies
cd frontend && npm install

# Build for production
cd frontend && npm run build
```

### Backend Development
```bash
# Start backend development server (WSL/Linux)
cd backend && source venv/bin/activate && python3 run_server.py
# URL: http://localhost:8000

# Create Python virtual environment
cd backend && python3 -m venv venv

# Install backend dependencies
cd backend && source venv/bin/activate && pip install -r requirements.txt

# View API documentation
# Navigate to: http://localhost:8000/docs
```

### Both Servers (Development)
```bash
# Terminal 1: Frontend
cd frontend && npm run dev

# Terminal 2: Backend
cd backend && source venv/bin/activate && python3 run_server.py
```

## Google Ads API Commands

### OAuth Flow Testing
```bash
# Test OAuth URL generation
curl -X GET "http://localhost:8000/api/google-ads/auth/url?user_id=USER_ID"

# Test OAuth callback
# Visit the generated URL and complete OAuth flow
```

### API Testing
```bash
# Test accounts endpoint
curl -X GET "http://localhost:8000/api/google-ads/accounts" \
  -H "Authorization: Bearer YOUR_SUPABASE_JWT"

# Test campaigns endpoint
curl -X GET "http://localhost:8000/api/google-ads/campaigns?account_id=ACCOUNT_ID" \
  -H "Authorization: Bearer YOUR_SUPABASE_JWT"
```

## Supabase Commands

### Database Operations
```bash
# Connect to Supabase project
supabase link --project-ref irftzijnouubcjkyeuxj

# Run database migrations (if using Supabase CLI)
supabase db push

# Generate TypeScript types (if needed)
supabase gen types typescript --project-id irftzijnouubcjkyeuxj
```

## Troubleshooting Commands

### Google Ads API Issues
```bash
# Check Google Ads API quota
# Monitor in Google Cloud Console: https://console.cloud.google.com/apis/api/googleads.googleapis.com/quotas

# Debug OAuth token issues
# Check refresh token in Supabase database: user_google_ads_tokens table

# Test API connection
curl -X GET "http://localhost:8000/api/google-ads/health"
```

### Development Environment Issues
```bash
# Check if ports are in use
lsof -i :5173  # Frontend port
lsof -i :8000  # Backend port

# Kill processes using ports
kill -9 $(lsof -t -i:5173)
kill -9 $(lsof -t -i:8000)

# Check Python virtual environment
which python3  # Should show venv path when activated
pip list       # Show installed packages
```

### Tailwind CSS Issues
```bash
# CDN is used in development - check index.html for:
# <script src="https://cdn.tailwindcss.com"></script>

# For production builds, Tailwind is processed via PostCSS
cd frontend && npm run build
```

## Performance Monitoring

### Check Application Health
```bash
# Frontend build size
cd frontend && npm run build && du -sh dist/

# Backend memory usage
ps aux | grep python

# Database connections
# Check in Supabase dashboard: https://irftzijnouubcjkyeuxj.supabase.co
```

## Deployment Commands

### Production Deployment
```bash
# Deploy frontend to Vercel
vercel --prod

# Deploy backend to Railway
railway deploy

# Environment variables to set:
# - SUPABASE_URL
# - SUPABASE_KEY
# - GOOGLE_ADS_DEVELOPER_TOKEN
# - GOOGLE_ADS_CLIENT_ID
# - GOOGLE_ADS_CLIENT_SECRET
```