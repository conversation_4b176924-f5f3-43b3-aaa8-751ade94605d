# Credentials: Google Ads AI Campaign Management Platform

## ⚠️ SECURITY NOTICE
This file contains sensitive credentials. Keep secure and never commit to version control.

## Supabase Configuration

### Project Credentials
```bash
SUPABASE_URL=https://irftzijnouubcjkyeuxj.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlyZnR6aWpub3V1YmNqa3lldXhqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg5NzkwMTUsImV4cCI6MjA2NDU1NTAxNX0.5aeJ0716sJgQX0s8vRZDg2FwCq63RiHUIwU4ZzX0u9I
SUPABASE_SERVICE_KEY=[SERVICE_KEY_HERE]
```

### Database Details
- **Project ID**: irftz<PERSON><PERSON><PERSON><PERSON>cjkyeuxj
- **Database Host**: db.irftzijnouubcjkyeuxj.supabase.co
- **Database Name**: postgres
- **Port**: 5432

## Google Ads API Configuration

### Developer Token & OAuth
```bash
GOOGLE_ADS_DEVELOPER_TOKEN=USJoZ_CN_pYY2MP-jlhjqA
GOOGLE_ADS_CLIENT_ID=*************-185m5ligmeu6gmcsfn5ctnpg4jg8mvq1.apps.googleusercontent.com
GOOGLE_ADS_CLIENT_SECRET=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
GOOGLE_ADS_LOGIN_CUSTOMER_ID=**********
```

### OAuth Redirect URIs
- Development: `http://localhost:8000/auth/callback`
- Production: `https://your-domain.com/auth/callback`

### Manager Account Details
- **Manager Account ID**: 310-946-3592
- **Account Name**: Brand Wisdom Solutions - Manager
- **Email**: <EMAIL>

## AI Services (Future Implementation)

### OpenRouter API
```bash
OPENROUTER_API_KEY=[TO_BE_ADDED]
OPENROUTER_MODELS={
  "ad_copy_generation": "anthropic/claude-3-opus",
  "keyword_analysis": "openai/gpt-4-turbo",
  "bulk_operations": "anthropic/claude-3-haiku",
  "insights_generation": "anthropic/claude-3-sonnet"
}
```

### OpenAI API (Alternative)
```bash
OPENAI_API_KEY=[TO_BE_ADDED]
```

### Anthropic API (Alternative)
```bash
ANTHROPIC_API_KEY=[TO_BE_ADDED]
```

## Production Deployment

### Vercel Environment Variables
```bash
VITE_API_URL=https://your-backend-domain.railway.app
VITE_SUPABASE_URL=https://irftzijnouubcjkyeuxj.supabase.co
VITE_SUPABASE_ANON_KEY=[ANON_KEY_HERE]
VITE_BRAND_NAME=Brand Wisdom Solutions
```

### Railway Environment Variables
```bash
SECRET_KEY=[GENERATE_NEW_SECRET_KEY]
ENVIRONMENT=production
SUPABASE_URL=https://irftzijnouubcjkyeuxj.supabase.co
SUPABASE_KEY=[SERVICE_KEY_HERE]
GOOGLE_ADS_DEVELOPER_TOKEN=USJoZ_CN_pYY2MP-jlhjqA
GOOGLE_ADS_CLIENT_ID=[CLIENT_ID_HERE]
GOOGLE_ADS_CLIENT_SECRET=[CLIENT_SECRET_HERE]
GOOGLE_ADS_LOGIN_CUSTOMER_ID=**********
```

## Security Best Practices

### Key Rotation Schedule
- Google Ads OAuth credentials: Every 90 days
- Supabase service keys: Every 180 days
- AI API keys: Every 90 days
- Secret keys: Every deployment

### Access Control
- Google Ads API: Test account access level (apply for Basic Access)
- Supabase: Row Level Security enabled
- AI APIs: Usage monitoring and rate limiting

### Monitoring
- Track API usage and quotas
- Monitor for unauthorized access attempts
- Set up alerts for credential rotation deadlines