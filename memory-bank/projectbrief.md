# Project Brief: Google Ads Comprehensive Campaign Management Platform (AdsAI)
**Brand Wisdom Solutions Internal Agency Tool**
**Source**: Google Ads API Basic Access Application (MCC: ************)
**Contact**: <EMAIL> | **Website**: https://brandwisdom.in/

## Project Overview
**Internal agency platform designed for comprehensive Google Ads campaign management across ALL campaign types with AI-powered assistance when needed.** This tool enables Brand Wisdom Solutions team to efficiently manage 20+ client accounts through their MCC structure (************), providing core campaign management capabilities with intelligent optimization assistance.

**Business Model**: Digital marketing agency specializing in Google Ads management for e-commerce, B2B, and local businesses.

**Supported Campaign Types**: Search Network, Display Network, Shopping, Performance Max, Local Services, Call-only, Demand Gen, and Display Expansion on Search.

## Primary Goals (Based on API Application):
1. **Efficient Multi-Client Management**: Streamline management of 20+ client accounts through MCC structure
2. **Comprehensive Campaign Support**: Full creation, management, reporting across ALL campaign types
3. **Daily Operations Enhancement**: Improve bid/budget adjustments, performance monitoring, and client reporting
4. **Search Query Analysis**: More efficient analysis to eliminate wasted spend and improve Quality Scores
5. **Accelerated Testing**: A/B testing ad copy and landing pages for better conversion rates
6. **Team Productivity**: Internal tool exclusively for agency staff to enhance service delivery

## Key Value Propositions (Exact from PRD Section 1):
- **40% reduction** in wasted ad spend through intelligent search query mining and negative keyword automation
- **50% improvement** in Quality Scores through AI-powered relevance optimization
- **3x faster** ad copy testing and optimization cycles
- **Real-time competitor intelligence** with automated response strategies
- **Natural language insights** that explain performance changes in plain English

## Core Platform Features (50% Programmatic + 50% AI Assistance):

### Programmatic Campaign Management (Core Daily Operations):

#### 1. Multi-Client Dashboard 📊
- Overview of all 20+ client accounts through MCC structure
- Real-time performance monitoring and alerts
- Quick account switching and comparison tools

#### 2. Campaign Creation & Management 🚀
- Campaign creation wizard for ALL campaign types (Search, Display, Shopping, Performance Max, Local Services, Call-only, Demand Gen)
- Bulk campaign operations and template management
- Campaign settings optimization and budget management

#### 3. Bid & Budget Management 💰
- Daily bid adjustments based on performance data
- Budget monitoring with automated alerts for overspending
- ROI-focused bidding strategies across campaign types

#### 4. Performance Reporting 📈
- Client-ready performance reports with customizable metrics
- Automated report generation and scheduling
- Performance comparisons across campaigns and time periods

#### 5. Monitoring & Alerts System 🔔
- Real-time alerts for budget exhaustion, performance drops, approval issues
- Daily digest emails for account managers
- Campaign health monitoring with issue prioritization

#### 6. Keyword & Audience Management 🎯
- Keyword research and planning tools
- Audience creation and management across Display and Shopping campaigns
- Search term analysis and negative keyword management

### AI-Powered Optimization Assistance (When Needed):

#### 7. Smart Search Query Analysis 🔍
- AI-powered search term analysis to identify wasted spend
- Automated negative keyword suggestions with confidence scoring
- Profitable query discovery for keyword expansion

#### 8. Quality Score Enhancement 📊
- AI analysis of ad relevance and Quality Score factors
- Automated recommendations for improving keyword-to-ad alignment
- Landing page relevance analysis and suggestions

#### 9. Ad Copy Testing Acceleration ✍️
- AI-assisted ad copy generation for A/B testing
- Performance prediction and winning variation identification
- Creative asset suggestions for Display and Shopping campaigns

#### 10. Intelligent Optimization Insights 🧠
- Natural language explanations of performance changes
- AI-powered trend analysis and forecasting
- Automated optimization recommendations with impact estimates

## Success Metrics (Exact from PRD Section 9):
### Primary KPIs:
1. **Efficiency Metrics**: Time saved 6+ hours/week per user, 2x accounts per manager, 10x optimization velocity
2. **Performance Metrics**: 40% wasted spend reduction, +2 points Quality Score improvement, 25% CTR increase, 30% CPA reduction
3. **Business Metrics**: 95%+ client retention, +35% revenue per account, 10:1 platform ROI

## Technical Architecture (Exact from PRD Section 6):

### Technology Stack:
```yaml
Frontend:
  - Framework: Vite + React + TypeScript
  - UI: Tailwind CSS + shadcn/ui (Brand Wisdom themed)
  - State: Zustand + TanStack Query
  - Charts: Recharts
  - Tables: TanStack Table

Backend:
  - API: FastAPI (Python)
  - Task Queue: Celery + Redis
  - Scheduler: Celery Beat
  - AI/ML: LangChain + OpenRouter API
  - Google Ads: google-ads-python

Database & Services:
  - Database: Supabase (PostgreSQL)
  - Vector Store: Supabase vector extension (latest HNSW indexes)
  - Cache: Redis
  - File Storage: Supabase Storage
  - Real-time: Supabase Realtime

Infrastructure:
  - Frontend: Vercel
  - Backend: Railway
  - Containers: Docker
  - Monitoring: Sentry
  - CI/CD: GitHub Actions
```

### OpenRouter AI Models (Exact from PRD):
```python
OPENROUTER_MODELS = {
    "ad_copy_generation": "anthropic/claude-3-opus",
    "keyword_analysis": "openai/gpt-4-turbo",
    "bulk_operations": "anthropic/claude-3-haiku",
    "competitor_analysis": "google/gemini-pro",
    "insights_generation": "anthropic/claude-3-sonnet",
    "quick_classification": "mistralai/mixtral-8x7b"
}
```

### Google Ads API Integration:
- **Core Services**: GoogleAdsService, SearchTermViewService, KeywordPlanService, AdService, BiddingStrategyService, ExtensionFeedItemService, BatchJobService, ReportingService
- **Rate Limits**: 15,000 operations/day, 15,000 get requests/day, 5,000 mutate operations/request

## User Personas (Exact from PRD Section 4):
1. **Search Campaign Specialist (Emma)**: Manages 15-20 search accounts daily
2. **PPC Team Lead (Marcus)**: Oversees team of 5 specialists, strategic planning
3. **Agency Director (Sarah)**: Client relationship management, strategic oversight

## MVP Phasing (Exact from PRD Section 8):
- **Phase 1 (Months 1-2)**: Google Ads API integration, Basic dashboard, Search Query Mining Engine, Negative Keyword AI (basic), Simple bid management, User authentication
- **Phase 2 (Months 3-4)**: Search Intent Classifier, Ad Copy Laboratory, Quality Score tracking, Ad Extensions Maximizer, Match Type Optimizer
- **Phase 3 (Months 5-6)**: Advanced Search Automation, Scripts Library, AI Insights Engine, Landing Page Synergy, Competitive intelligence
- **Phase 4 (Months 7+)**: Advanced bid strategies, Workflow builder, Custom reporting, White-label options, Enterprise features
