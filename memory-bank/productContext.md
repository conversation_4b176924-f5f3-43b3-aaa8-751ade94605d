# Product Context: Google Ads Comprehensive Campaign Management Platform (AdsAI)
**Source**: Google Ads API Basic Access Application (MCC: 310-946-3592)
**Contact**: <EMAIL> | **Website**: https://brandwisdom.in/

## Problem Statement (Based on API Application & Real Agency Workflow)
### Current Campaign Management Challenges for Brand Wisdom Solutions:
1. **Multi-Client Account Management**: Managing 20+ client accounts through MCC requires extensive manual coordination and switching between accounts
2. **Campaign Type Diversity**: Supporting ALL campaign types (Search, Display, Shopping, Performance Max, Local Services, Call-only, Demand Gen) with different optimization needs
3. **Daily Monitoring Burden**: Manual performance monitoring, bid adjustments, and budget management across diverse client portfolios
4. **Client Reporting Overhead**: Time-intensive process of generating custom performance reports for different client needs and business models
5. **Search Query Analysis**: Manual review of search terms across client accounts to identify waste and optimization opportunities
6. **Campaign Creation Workflow**: Repetitive setup processes for new campaigns across different networks and client requirements

### Business Impact for Brand Wisdom Solutions:
- Average 25% of client budget wasted across campaign types
- 40% higher CPCs due to poor optimization across networks
- 6+ hours daily on manual optimization tasks per account manager across 20+ client accounts
- Difficulty scaling agency operations due to manual campaign management processes
- Inconsistent performance across different campaign types and client accounts

## Solution Overview (Brand Wisdom Solutions Agency Platform)
**Comprehensive internal agency platform designed for managing 20+ client Google Ads accounts across ALL campaign types from a unified dashboard.** This AI-powered tool enables Brand Wisdom Solutions team members to efficiently create, manage, optimize, and report on campaigns across Search Network, Display Network, Shopping, Performance Max, Local Services, Call-only, and Demand Gen campaigns. The platform transforms manual campaign management into intelligent, automated workflows that improve client ROI while reducing management time by 90%.

### Agency Business Model Context:
- **Digital Marketing Agency**: Brand Wisdom Solutions specializes in Google Ads management and optimization
- **Client Base**: Serving e-commerce, B2B, and local businesses with diverse campaign needs
- **Scale**: Managing 20+ client accounts through Manager Customer Center (MCC) structure
- **Services**: Campaign creation, optimization, performance monitoring, bid/budget management, reporting

### Key Platform Characteristics:
- **Internal Tool**: Built exclusively for Brand Wisdom Solutions agency staff
- **Comprehensive Campaign Support**: All Google Ads campaign types (Search, Display, Shopping, Performance Max, Local Services, Call-only, Demand Gen)
- **Full API Capabilities**: Campaign Creation, Campaign Management, Reporting, and Keyword Planning Services
- **Multi-Client Management**: Unified dashboard for 20+ client accounts accessed through MCC (310-946-3592)
- **Team Collaboration**: Shared resources and consistent optimization approaches across all campaign types
- **No External Access**: Internal tool with no billing, subscriptions, or external user access

## Core Features - Comprehensive Campaign Management & Optimization

### 1. Search Query Mining Engine 🔍
- **Purpose**: Automatically analyze search terms to find profitable opportunities and eliminate waste
- **Profitable Query Discovery**: Identify high-converting search terms not yet added as keywords
- **Waste Elimination**: Flag search terms with 0 conversions after threshold spend
- **Implementation**: SearchTermView, KeywordPlanService, SharedSetService APIs
- **UI**: Interactive search term explorer with one-click add as keyword or negative

### 2. Search Intent Classifier 🎯
- **Purpose**: Categorize search queries by user intent to optimize messaging and bidding
- **Intent Categories**: Transactional, Informational, Navigational, Commercial Investigation
- **AI Classification**: NLP analysis with confidence scoring and historical conversion correlation
- **Optimization Actions**: Adjust bids, tailor ad copy, select landing pages based on intent

### 3. Advanced Ad Copy Laboratory ✍️
- **Purpose**: Generate, test, and optimize ad copy using psychological triggers and AI
- **Psychological Triggers**: Urgency, Scarcity, Social proof, Authority signals, Emotion-based appeals
- **AI Copy Generation**: Generate 15 headlines + 4 descriptions for RSAs with keyword inclusion
- **Performance Analysis**: Element-level tracking and winning combination identification

### 4. Negative Keyword AI 🚫
- **Purpose**: Proactively identify and implement negative keywords to eliminate wasted spend
- **Intelligent Detection**: Pattern recognition, industry-specific templates, conflict detection
- **Automated Implementation**: Confidence-based auto-negation with impact tracking
- **Cost Savings**: Calculate prevented clicks and historical waste prevention

### 5. Match Type Optimizer 🎲
- **Purpose**: Dynamically optimize keyword match types based on performance data
- **Performance Migration**: Broad to phrase to exact progression with conversion thresholds
- **Automated Transitions**: Gradual match type tightening with rollback capabilities

### 6. Additional Core Features
- **Ad Relevance Maximizer**: Improve Quality Scores through keyword-to-ad relevance optimization
- **Search Bid Intelligence**: Micro-bidding engine with hour-by-hour optimization
- **Ad Extensions Maximizer**: Maximize SERP real estate and CTR through intelligent extension optimization
- **Landing Page Synergy**: Ensure message consistency from ad click to conversion
- **Advanced Search Automation**: Automate complex optimization workflows and campaign structures
- **Search Ads Scripts Library**: Pre-built automation scripts for common optimization tasks
- **AI-Powered Insights Engine**: Transform data into actionable insights using natural language

## User Personas (Updated for Agency Context)

### Primary Users:
1. **Campaign Manager (Agency Team Member)**:
   - Manages portion of 20+ total client accounts across multiple campaign types
   - Daily campaign management across Search, Display, Shopping, and Performance Max campaigns
   - Needs efficient bulk optimization tools for diverse campaign types
   - Values consistent workflows across all clients and campaign networks
   - Requires quick client switching and aggregate performance views

2. **Senior Strategist (Team Lead)**:
   - Oversees multiple campaign managers
   - Needs performance insights across all clients
   - Ensures best practices are followed
   - Mentors new team members on platform usage

3. **New Team Member (Onboarding)**:
   - Learning agency processes and tools
   - Needs clear guidance and tutorials
   - Requires safe environment to explore features
   - Benefits from standardized workflows

## User Experience Goals (Exact from PRD)
- **Simplicity**: Intuitive interface requiring minimal training
- **Speed**: Sub-2 second response times for all interactions
- **Clarity**: Clear visualization of data and recommendations
- **Trust**: Transparent AI decision-making with explanations
- **Efficiency**: Reduce manual work by 90%

## Technical Architecture (Enhanced Version 2.1)
```yaml
Frontend (Enhanced Premium UX Stack):
  - Framework: Vite + React + TypeScript
  - UI: Tailwind CSS + Radix UI (Brand Wisdom themed)
  - State: Zustand + TanStack Query
  - Routing: React Router DOM (simplified)
  - Forms: React Hook Form + Zod validation
  - Animations: Framer Motion (premium micro-interactions)
  - Notifications: Sonner (elegant toasts)
  - Charts: Recharts
  - Tables: TanStack Table
  - Date Handling: date-fns
  - Development: ESLint 9+ with TypeScript rules

Enhanced UX Features (New in 2.1):
  - Auto-save: Form drafts and UI state persistence
  - A/B Testing: Built-in experimentation framework
  - Micro-interactions: Smooth animations and transitions
  - Real-time feedback: Instant form validation
  - Personalization: Remember user preferences

Backend:
  - API: FastAPI (Python)
  - Task Queue: Celery + Redis
  - Scheduler: Celery Beat
  - AI/ML: LangChain + OpenRouter API
  - Google Ads: google-ads-python

Database & Services (Enhanced):
  - Database: Supabase (PostgreSQL) - 57 tables (4 new UX tables)
  - Vector Store: Supabase vector extension (latest HNSW indexes)
  - Cache: Redis
  - File Storage: Supabase Storage
  - Real-time: Supabase Realtime
  - UX Analytics: Interaction tracking and A/B testing

Infrastructure:
  - Frontend: Vercel
  - Backend: Railway
  - Containers: Docker
  - Monitoring: Sentry
  - CI/CD: GitHub Actions
```

## Integration Requirements
- **Google Ads API**: Real-time campaign data and modification capabilities
- **Supabase**: User authentication, data storage, real-time subscriptions, email notifications
- **AI Services**: OpenAI GPT models and Anthropic Claude for recommendations
- **Monitoring**: Application performance and error tracking
- **Notifications**: Email notifications via Supabase, in-app notification system

## Data Flow
1. **Data Ingestion**: Automated sync from Google Ads API every 15 minutes
2. **AI Analysis**: Background processing of campaign data for optimization opportunities
3. **Recommendation Generation**: AI creates actionable suggestions with confidence scores
4. **User Review**: Dashboard presents recommendations for user approval
5. **Implementation**: One-click execution through Google Ads API
6. **Monitoring**: Track results and measure improvement impact

## Success Criteria
- **User Adoption**: 90% of users implement at least one AI recommendation weekly
- **Performance Impact**: Average 15% improvement in campaign ROI
- **Time Savings**: 80% reduction in manual optimization time
- **System Reliability**: 99.9% uptime with sub-2 second response times
