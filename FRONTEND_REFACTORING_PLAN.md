# Frontend Refactoring Plan: Google Ads AI Campaign Management Platform

## Executive Summary

This document outlines a comprehensive refactoring plan for the AdsAI frontend codebase based on a senior developer architecture review. The plan addresses critical architectural issues and provides a phased approach to transform the current MVP into a scalable, maintainable enterprise application.

### Critical Issues Identified
1. **Broken routing system** using custom hash-based navigation
2. **Chaotic styling architecture** mixing Tailwind CDN and custom CSS
3. **No scalable state management** solution
4. **Zero type safety** leading to runtime errors
5. **Flat directory structure** that won't scale

### Timeline Overview
- **Phase 1 (Week 1)**: Critical fixes - Routing, Styling, PropTypes
- **Phase 2 (Weeks 2-3)**: Architecture improvements - Feature folders, State management, Services
- **Phase 3 (Weeks 4-6)**: Scalability enhancements - Code splitting, Testing, Documentation
- **Phase 4 (Weeks 7-8)**: Production readiness - Performance, Security, Deployment

---

## Phase 1: Critical Infrastructure Fixes (Week 1)

### Day 1-2: Replace Routing System

#### Current Problem
```javascript
// BROKEN: Custom hash-based routing in App.jsx
const handleHashChange = () => {
  const hash = window.location.hash.slice(1)
  const validRoutes = ['dashboard', 'google-ads-setup', ...]
  if (hash && validRoutes.includes(hash)) {
    setCurrentRoute(hash)
  }
}
```

#### Solution Implementation

1. **Install React Router**
```bash
npm install react-router-dom
```

2. **Create Route Configuration**
```javascript
// src/routes/index.jsx
import { createBrowserRouter } from 'react-router-dom'
import { ProtectedRoute } from './ProtectedRoute'
import { lazy } from 'react'

// Lazy load for code splitting
const Dashboard = lazy(() => import('../pages/DashboardPage'))
const Campaigns = lazy(() => import('../pages/CampaignsPage'))
const SearchMining = lazy(() => import('../features/ai/SearchMining'))
const IntentClassifier = lazy(() => import('../features/ai/IntentClassifier'))

export const router = createBrowserRouter([
  {
    path: '/',
    element: <AppLayout />,
    errorElement: <ErrorPage />,
    children: [
      {
        index: true,
        element: <Navigate to="/dashboard" replace />
      },
      {
        path: 'dashboard',
        element: (
          <ProtectedRoute>
            <Dashboard />
          </ProtectedRoute>
        )
      },
      {
        path: 'campaigns',
        children: [
          {
            index: true,
            element: <ProtectedRoute><Campaigns /></ProtectedRoute>
          },
          {
            path: ':campaignId',
            element: <ProtectedRoute><CampaignDetail /></ProtectedRoute>
          }
        ]
      },
      {
        path: 'ai',
        element: <ProtectedRoute><AILayout /></ProtectedRoute>,
        children: [
          { path: 'search-mining', element: <SearchMining /> },
          { path: 'intent-classifier', element: <IntentClassifier /> },
          { path: 'ad-copy-lab', element: <AdCopyLab /> },
          // ... other AI features
        ]
      }
    ]
  },
  {
    path: '/auth',
    element: <AuthLayout />,
    children: [
      { path: 'login', element: <Login /> },
      { path: 'signup', element: <Signup /> },
      { path: 'forgot-password', element: <ForgotPassword /> }
    ]
  }
])
```

3. **Create Protected Route Component**
```javascript
// src/routes/ProtectedRoute.jsx
import { Navigate, useLocation } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'

export function ProtectedRoute({ children }) {
  const { user, loading } = useAuth()
  const location = useLocation()

  if (loading) {
    return <LoadingScreen />
  }

  if (!user) {
    return <Navigate to="/auth/login" state={{ from: location }} replace />
  }

  return children
}
```

4. **Update Navigation Components**
```javascript
// src/components/layout/DashboardLayout.jsx
import { NavLink, useNavigate } from 'react-router-dom'

// Replace href with to prop
<NavLink
  to="/dashboard"
  className={({ isActive }) => `
    sidebar-nav-item
    ${isActive ? 'active' : ''}
  `}
>
  <Icon className="sidebar-nav-icon" />
  <span>Dashboard</span>
</NavLink>
```

### Day 3: Fix Styling Architecture

#### Decision: CSS Modules + Tailwind Utilities

1. **Remove Tailwind CDN**
```html
<!-- Remove from index.html -->
<script src="https://cdn.tailwindcss.com"></script>
```

2. **Install Tailwind Properly**
```bash
npm install -D tailwindcss postcss autoprefixer
npx tailwindcss init -p
```

3. **Configure Tailwind**
```javascript
// tailwind.config.js
export default {
  content: ['./index.html', './src/**/*.{js,jsx}'],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#F4F7FF',
          500: '#4172F5',
          600: '#3E5CE7',
          700: '#324ECF',
          900: '#07153F',
        },
        gold: {
          500: '#FECD79'
        }
      },
      fontFamily: {
        'jost': ['Jost', 'sans-serif'],
        'playfair': ['Playfair Display', 'serif']
      }
    }
  },
  plugins: []
}
```

4. **Create Component Styles with CSS Modules**
```css
/* src/components/ui/Button.module.css */
.button {
  @apply inline-flex items-center justify-center rounded-md font-medium transition-colors;
  @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2;
  @apply disabled:pointer-events-none disabled:opacity-50;
}

.primary {
  @apply bg-primary-600 text-white hover:bg-primary-700;
  @apply focus-visible:ring-primary-600;
}

.secondary {
  @apply border border-gray-300 bg-white text-gray-700;
  @apply hover:bg-gray-50 focus-visible:ring-gray-500;
}

.ghost {
  @apply hover:bg-gray-100 hover:text-gray-900;
  @apply focus-visible:ring-gray-500;
}

/* Size variants */
.sm { @apply h-9 px-3 text-sm; }
.md { @apply h-10 px-4 py-2; }
.lg { @apply h-11 px-8; }
```

5. **Update Components**
```javascript
// src/components/ui/Button.jsx
import styles from './Button.module.css'
import { cn } from '../../lib/utils'

export const Button = React.forwardRef(({ 
  className,
  variant = 'primary',
  size = 'md',
  ...props 
}, ref) => {
  return (
    <button
      ref={ref}
      className={cn(
        styles.button,
        styles[variant],
        styles[size],
        className
      )}
      {...props}
    />
  )
})
```

### Day 4: Add PropTypes & Basic Type Safety

1. **Install PropTypes**
```bash
npm install prop-types
```

2. **Create PropTypes for All Components**
```javascript
// src/components/dashboard/CampaignOverview.jsx
import PropTypes from 'prop-types'

export const CampaignOverview = ({ campaigns = [] }) => {
  // ... component code
}

CampaignOverview.propTypes = {
  campaigns: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      name: PropTypes.string.isRequired,
      status: PropTypes.oneOf(['active', 'paused', 'ended']).isRequired,
      budget: PropTypes.number.isRequired,
      spend: PropTypes.number.isRequired,
      impressions: PropTypes.number.isRequired,
      clicks: PropTypes.number.isRequired,
      ctr: PropTypes.number.isRequired,
      cpc: PropTypes.number.isRequired,
      quality_score: PropTypes.number
    })
  )
}
```

3. **Create Shared PropTypes**
```javascript
// src/lib/propTypes.js
import PropTypes from 'prop-types'

export const CampaignPropType = PropTypes.shape({
  id: PropTypes.string.isRequired,
  name: PropTypes.string.isRequired,
  status: PropTypes.oneOf(['active', 'paused', 'ended']).isRequired,
  budget: PropTypes.number.isRequired,
  metrics: PropTypes.shape({
    spend: PropTypes.number,
    impressions: PropTypes.number,
    clicks: PropTypes.number,
    ctr: PropTypes.number,
    cpc: PropTypes.number
  })
})

export const UserPropType = PropTypes.shape({
  id: PropTypes.string.isRequired,
  email: PropTypes.string.isRequired,
  full_name: PropTypes.string,
  avatar_url: PropTypes.string
})
```

### Day 5: Environment Configuration

1. **Create Environment Files**
```bash
# .env.development
VITE_API_URL=http://localhost:8000
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_anon_key
VITE_GOOGLE_OAUTH_CLIENT_ID=your_client_id

# .env.production
VITE_API_URL=https://api.adsai.com
VITE_SUPABASE_URL=your_prod_supabase_url
VITE_SUPABASE_ANON_KEY=your_prod_anon_key
VITE_GOOGLE_OAUTH_CLIENT_ID=your_prod_client_id
```

2. **Create Config Module**
```javascript
// src/config/index.js
export const config = {
  api: {
    baseUrl: import.meta.env.VITE_API_URL,
    timeout: 30000
  },
  supabase: {
    url: import.meta.env.VITE_SUPABASE_URL,
    anonKey: import.meta.env.VITE_SUPABASE_ANON_KEY
  },
  google: {
    clientId: import.meta.env.VITE_GOOGLE_OAUTH_CLIENT_ID
  },
  features: {
    enableAI: import.meta.env.VITE_ENABLE_AI_FEATURES === 'true'
  }
}
```

---

## Phase 2: Architecture Improvements (Weeks 2-3)

### Week 2: Feature-Based Architecture

#### New Directory Structure
```
src/
├── features/                 # Feature modules
│   ├── auth/
│   │   ├── components/      # Feature-specific components
│   │   │   ├── LoginForm.jsx
│   │   │   ├── SignupForm.jsx
│   │   │   └── OAuthButton.jsx
│   │   ├── hooks/          # Feature-specific hooks
│   │   │   ├── useLogin.js
│   │   │   └── useSignup.js
│   │   ├── services/       # API services
│   │   │   └── authService.js
│   │   ├── constants.js    # Feature constants
│   │   └── index.js        # Public exports
│   │
│   ├── campaigns/
│   │   ├── components/
│   │   │   ├── CampaignList/
│   │   │   ├── CampaignDetail/
│   │   │   └── CreateCampaign/
│   │   ├── hooks/
│   │   ├── services/
│   │   └── constants.js
│   │
│   ├── dashboard/
│   │   ├── components/
│   │   ├── hooks/
│   │   └── services/
│   │
│   └── ai/                 # AI features
│       ├── search-mining/
│       ├── intent-classifier/
│       ├── ad-copy-lab/
│       └── shared/
│
├── shared/                 # Shared across features
│   ├── components/         # Shared UI components
│   │   ├── ui/            # Base components
│   │   ├── charts/        # Data viz
│   │   └── feedback/      # Toasts, modals
│   ├── hooks/             # Shared hooks
│   ├── services/          # Shared services
│   └── utils/             # Utilities
│
├── core/                   # Core app functionality
│   ├── api/               # API client setup
│   ├── auth/              # Auth context/provider
│   ├── config/            # App configuration
│   ├── constants/         # Global constants
│   └── types/             # Shared PropTypes
│
├── layouts/               # Layout components
│   ├── AppLayout.jsx
│   ├── AuthLayout.jsx
│   └── AILayout.jsx
│
├── routes/                # Routing configuration
│   ├── index.js
│   └── ProtectedRoute.jsx
│
└── styles/                # Global styles
    ├── globals.css
    └── variables.css
```

#### Migration Strategy

1. **Create Feature Module Template**
```javascript
// src/features/campaigns/index.js
// Public API for campaigns feature
export { CampaignList } from './components/CampaignList'
export { CampaignDetail } from './components/CampaignDetail'
export { CreateCampaign } from './components/CreateCampaign'
export { useCampaigns } from './hooks/useCampaigns'
export { useCampaign } from './hooks/useCampaign'
export { campaignService } from './services/campaignService'
export * from './constants'
```

2. **Move Components Incrementally**
```bash
# Example migration script
mkdir -p src/features/campaigns/components
mv src/components/campaigns/* src/features/campaigns/components/
mv src/hooks/useCampaigns.js src/features/campaigns/hooks/
```

### Week 3: State Management & Service Layer

#### Implement Zustand for State Management

1. **Install Zustand**
```bash
npm install zustand
```

2. **Create Store Structure**
```javascript
// src/store/index.js
export { useCampaignStore } from './campaignStore'
export { useUIStore } from './uiStore'
export { useAccountStore } from './accountStore'
```

3. **Campaign Store Example**
```javascript
// src/store/campaignStore.js
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { campaignService } from '@/features/campaigns/services'

export const useCampaignStore = create(devtools((set, get) => ({
  // State
  campaigns: [],
  selectedCampaign: null,
  filters: {
    status: 'all',
    clientId: null,
    dateRange: 'last7days'
  },
  loading: false,
  error: null,

  // Actions
  fetchCampaigns: async () => {
    set({ loading: true, error: null })
    try {
      const { filters } = get()
      const campaigns = await campaignService.getCampaigns(filters)
      set({ campaigns, loading: false })
    } catch (error) {
      set({ error: error.message, loading: false })
    }
  },

  selectCampaign: (campaignId) => {
    const campaign = get().campaigns.find(c => c.id === campaignId)
    set({ selectedCampaign: campaign })
  },

  updateFilter: (filterKey, value) => {
    set(state => ({
      filters: { ...state.filters, [filterKey]: value }
    }))
    get().fetchCampaigns() // Auto-refetch on filter change
  },

  createCampaign: async (campaignData) => {
    set({ loading: true, error: null })
    try {
      const newCampaign = await campaignService.createCampaign(campaignData)
      set(state => ({
        campaigns: [...state.campaigns, newCampaign],
        loading: false
      }))
      return newCampaign
    } catch (error) {
      set({ error: error.message, loading: false })
      throw error
    }
  }
})))
```

#### Create Service Layer

1. **Base API Service**
```javascript
// src/core/api/baseService.js
import { config } from '@/core/config'

class BaseService {
  constructor(endpoint) {
    this.endpoint = endpoint
    this.baseURL = config.api.baseUrl
  }

  async request(path, options = {}) {
    const url = `${this.baseURL}${this.endpoint}${path}`
    
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
        ...(await this.getAuthHeaders())
      }
    })

    if (!response.ok) {
      throw await this.handleError(response)
    }

    return response.json()
  }

  async getAuthHeaders() {
    const { data: { session } } = await supabase.auth.getSession()
    return session ? { Authorization: `Bearer ${session.access_token}` } : {}
  }

  async handleError(response) {
    const error = await response.json()
    return new Error(error.message || 'Request failed')
  }

  // CRUD methods
  async getAll(params = {}) {
    const query = new URLSearchParams(params).toString()
    return this.request(`?${query}`)
  }

  async getById(id) {
    return this.request(`/${id}`)
  }

  async create(data) {
    return this.request('', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  async update(id, data) {
    return this.request(`/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data)
    })
  }

  async delete(id) {
    return this.request(`/${id}`, {
      method: 'DELETE'
    })
  }
}
```

2. **Campaign Service**
```javascript
// src/features/campaigns/services/campaignService.js
import { BaseService } from '@/core/api/baseService'

class CampaignService extends BaseService {
  constructor() {
    super('/api/campaigns')
  }

  async getCampaigns(filters = {}) {
    return this.getAll(filters)
  }

  async getCampaignById(id) {
    return this.getById(id)
  }

  async createCampaign(campaignData) {
    // Validate campaign data
    this.validateCampaignData(campaignData)
    return this.create(campaignData)
  }

  async updateCampaign(id, updates) {
    return this.update(id, updates)
  }

  async pauseCampaign(id) {
    return this.update(id, { status: 'paused' })
  }

  async activateCampaign(id) {
    return this.update(id, { status: 'active' })
  }

  async getCampaignMetrics(id, dateRange) {
    return this.request(`/${id}/metrics`, {
      params: { dateRange }
    })
  }

  validateCampaignData(data) {
    const required = ['name', 'budget', 'type', 'targetLocations']
    const missing = required.filter(field => !data[field])
    
    if (missing.length > 0) {
      throw new Error(`Missing required fields: ${missing.join(', ')}`)
    }
  }
}

export const campaignService = new CampaignService()
```

---

## Phase 3: Scalability Enhancements (Weeks 4-6)

### Week 4: Code Splitting & Performance

#### Implement Route-Based Code Splitting

1. **Update Route Configuration**
```javascript
// src/routes/index.jsx
import { lazy, Suspense } from 'react'
import { RouteLoadingSpinner } from '@/shared/components/feedback'

// Lazy load all routes
const Dashboard = lazy(() => import('@/features/dashboard'))
const Campaigns = lazy(() => import('@/features/campaigns'))
const SearchMining = lazy(() => import('@/features/ai/search-mining'))

// Wrap routes in Suspense
const withSuspense = (Component) => (
  <Suspense fallback={<RouteLoadingSpinner />}>
    <Component />
  </Suspense>
)

export const routes = [
  {
    path: 'dashboard',
    element: withSuspense(Dashboard)
  },
  {
    path: 'campaigns',
    element: withSuspense(Campaigns)
  }
]
```

2. **Implement Component-Level Code Splitting**
```javascript
// src/features/campaigns/components/CampaignDetail/index.jsx
import { lazy, Suspense } from 'react'

// Split heavy components
const CampaignMetricsChart = lazy(() => import('./CampaignMetricsChart'))
const SearchTermsAnalysis = lazy(() => import('./SearchTermsAnalysis'))

export function CampaignDetail() {
  return (
    <div>
      <CampaignHeader />
      
      <Suspense fallback={<ChartSkeleton />}>
        <CampaignMetricsChart />
      </Suspense>
      
      <Suspense fallback={<TableSkeleton />}>
        <SearchTermsAnalysis />
      </Suspense>
    </div>
  )
}
```

#### Add Performance Optimizations

1. **Implement React.memo for Heavy Components**
```javascript
// src/features/dashboard/components/MetricsChart.jsx
import { memo } from 'react'

export const MetricsChart = memo(({ data, type }) => {
  // ... component code
}, (prevProps, nextProps) => {
  // Custom comparison
  return prevProps.data === nextProps.data && 
         prevProps.type === nextProps.type
})
```

2. **Add useMemo/useCallback Hooks**
```javascript
// src/features/campaigns/hooks/useCampaigns.js
import { useMemo, useCallback } from 'react'

export function useCampaigns() {
  const { campaigns, filters } = useCampaignStore()
  
  // Memoize expensive filtering
  const filteredCampaigns = useMemo(() => {
    return campaigns.filter(campaign => {
      if (filters.status !== 'all' && campaign.status !== filters.status) {
        return false
      }
      if (filters.clientId && campaign.clientId !== filters.clientId) {
        return false
      }
      return true
    })
  }, [campaigns, filters])
  
  // Memoize callbacks
  const updateCampaign = useCallback((id, updates) => {
    return campaignService.updateCampaign(id, updates)
  }, [])
  
  return { filteredCampaigns, updateCampaign }
}
```

### Day 9: Basic Testing Setup (Optional for MVP)

#### Minimal Testing Framework

1. **Install Essential Testing Dependencies**
```bash
npm install -D vitest @testing-library/react @testing-library/jest-dom
# Skip MSW and complex mocking for MVP
```

2. **Configure Vitest**
```javascript
// vite.config.js
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: './src/test/setup.js',
    coverage: {
      reporter: ['text', 'json', 'html'],
      exclude: ['node_modules/', 'src/test/']
    }
  }
})
```

3. **Create Test Setup**
```javascript
// src/test/setup.js
import '@testing-library/jest-dom'
import { server } from './mocks/server'

beforeAll(() => server.listen())
afterEach(() => server.resetHandlers())
afterAll(() => server.close())
```

4. **Create MSW Mocks**
```javascript
// src/test/mocks/handlers.js
import { rest } from 'msw'

export const handlers = [
  rest.get('/api/campaigns', (req, res, ctx) => {
    return res(
      ctx.json({
        campaigns: [
          {
            id: '1',
            name: 'Test Campaign',
            status: 'active',
            budget: 1000
          }
        ]
      })
    )
  })
]
```

#### Write Component Tests

```javascript
// src/features/campaigns/components/CampaignList/CampaignList.test.jsx
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { CampaignList } from './CampaignList'

describe('CampaignList', () => {
  it('renders campaign list', async () => {
    render(<CampaignList />)
    
    await waitFor(() => {
      expect(screen.getByText('Test Campaign')).toBeInTheDocument()
    })
  })
  
  it('filters campaigns by status', async () => {
    const user = userEvent.setup()
    render(<CampaignList />)
    
    await user.click(screen.getByRole('button', { name: /filter/i }))
    await user.click(screen.getByRole('option', { name: /active/i }))
    
    await waitFor(() => {
      expect(screen.getByText('Test Campaign')).toBeInTheDocument()
    })
  })
})
```

### Day 10: Production Build Optimization

#### Focus on Build Performance

1. **Skip Storybook for MVP**
```javascript
// Focus on production build optimization instead
// Documentation can come after MVP
```

2. **Create Component Stories**
```javascript
// src/shared/components/ui/Button/Button.stories.js
export default {
  title: 'UI/Button',
  component: Button,
  parameters: {
    docs: {
      description: {
        component: 'Base button component with multiple variants'
      }
    }
  },
  argTypes: {
    variant: {
      control: 'select',
      options: ['primary', 'secondary', 'ghost', 'destructive']
    },
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg']
    }
  }
}

export const Default = {
  args: {
    children: 'Click me',
    variant: 'primary'
  }
}

export const AllVariants = () => (
  <div className="space-y-4">
    <Button variant="primary">Primary</Button>
    <Button variant="secondary">Secondary</Button>
    <Button variant="ghost">Ghost</Button>
    <Button variant="destructive">Destructive</Button>
  </div>
)
```

---

## Phase 4: Production Readiness (Weeks 7-8)

### Week 7: Performance & Security

#### Performance Monitoring

1. **Add Web Vitals**
```javascript
// src/core/performance/vitals.js
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals'

function sendToAnalytics({ name, delta, id }) {
  // Send to analytics service
  console.log({ name, delta, id })
}

export function reportWebVitals() {
  getCLS(sendToAnalytics)
  getFID(sendToAnalytics)
  getFCP(sendToAnalytics)
  getLCP(sendToAnalytics)
  getTTFB(sendToAnalytics)
}
```

2. **Implement Error Boundaries**
```javascript
// src/core/errors/ErrorBoundary.jsx
import { Component } from 'react'
import * as Sentry from '@sentry/react'

export class ErrorBoundary extends Component {
  state = { hasError: false, error: null }
  
  static getDerivedStateFromError(error) {
    return { hasError: true, error }
  }
  
  componentDidCatch(error, errorInfo) {
    console.error('Error caught by boundary:', error, errorInfo)
    Sentry.captureException(error, { contexts: { errorInfo } })
  }
  
  render() {
    if (this.state.hasError) {
      return (
        <ErrorFallback 
          error={this.state.error}
          resetError={() => this.setState({ hasError: false })}
        />
      )
    }
    
    return this.props.children
  }
}
```

#### Security Enhancements

1. **Add Content Security Policy**
```javascript
// public/index.html
<meta http-equiv="Content-Security-Policy" 
      content="default-src 'self'; 
               script-src 'self' 'unsafe-inline' https://apis.google.com; 
               style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
               font-src 'self' https://fonts.gstatic.com;
               img-src 'self' data: https:;
               connect-src 'self' https://api.adsai.com https://*.supabase.co">
```

2. **Implement Input Sanitization**
```javascript
// src/shared/utils/sanitize.js
import DOMPurify from 'dompurify'

export function sanitizeInput(input) {
  return DOMPurify.sanitize(input, { 
    ALLOWED_TAGS: [],
    ALLOWED_ATTR: []
  })
}

export function sanitizeHTML(html) {
  return DOMPurify.sanitize(html, {
    ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'a', 'p', 'br'],
    ALLOWED_ATTR: ['href', 'target', 'rel']
  })
}
```

### Week 8: Deployment & CI/CD

#### Set Up GitHub Actions

```yaml
# .github/workflows/ci.yml
name: CI

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run linter
      run: npm run lint
    
    - name: Run tests
      run: npm run test:coverage
    
    - name: Build
      run: npm run build
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
```

#### Production Build Configuration

```javascript
// vite.config.prod.js
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { visualizer } from 'rollup-plugin-visualizer'

export default defineConfig({
  plugins: [
    react(),
    visualizer({ open: true })
  ],
  build: {
    target: 'esnext', // Target modern browsers
    minify: 'esbuild', // Faster than terser
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          'react-vendor': ['react', 'react-dom', 'react-router-dom'],
          'ui-vendor': ['recharts', 'lucide-react'],
          'supabase': ['@supabase/supabase-js'],
          'utils': ['axios']
        }
      }
    },
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    }
  }
})
```

---

## Migration Checklist

### Phase 1 Checklist (Week 1)
- [ ] Install and configure React Router
- [ ] Create route configuration with lazy loading
- [ ] Implement ProtectedRoute component
- [ ] Update all navigation links
- [ ] Remove hash-based routing
- [ ] Install Tailwind properly
- [ ] Configure PostCSS
- [ ] Create CSS Module structure
- [ ] Update all components to use CSS Modules
- [ ] Remove Tailwind CDN
- [ ] Install PropTypes
- [ ] Add PropTypes to all components
- [ ] Create shared PropType definitions
- [ ] Set up environment configuration
- [ ] Create config module

### Phase 2 Checklist (Weeks 2-3)
- [ ] Create feature-based directory structure
- [ ] Move auth feature
- [ ] Move campaigns feature
- [ ] Move dashboard feature
- [ ] Move AI features
- [ ] Create shared components directory
- [ ] Install Zustand
- [ ] Create campaign store
- [ ] Create UI store
- [ ] Create account store
- [ ] Implement BaseService class
- [ ] Create campaign service
- [ ] Create auth service
- [ ] Create Google Ads service
- [ ] Update components to use stores

### Phase 3 Checklist (Weeks 4-6)
- [ ] Implement route-based code splitting
- [ ] Add Suspense boundaries
- [ ] Optimize heavy components with React.memo
- [ ] Add useMemo/useCallback where needed
- [ ] Install testing framework
- [ ] Configure Vitest
- [ ] Set up MSW for mocking
- [ ] Write component tests
- [ ] Write hook tests
- [ ] Write service tests
- [ ] Install Storybook
- [ ] Create component stories
- [ ] Document component APIs
- [ ] Create usage examples

### Phase 4 Checklist (Weeks 7-8)
- [ ] Add web vitals monitoring
- [ ] Implement error boundaries
- [ ] Add Sentry integration
- [ ] Configure CSP headers
- [ ] Implement input sanitization
- [ ] Add security headers
- [ ] Set up GitHub Actions
- [ ] Configure production builds
- [ ] Optimize bundle size
- [ ] Set up deployment pipeline

---

## Key Performance Indicators (KPIs)

### Technical Metrics
- **Bundle Size**: < 200KB (gzipped)
- **First Contentful Paint**: < 1.5s
- **Time to Interactive**: < 3s
- **Code Coverage**: > 80%
- **Lighthouse Score**: > 90

### Development Metrics
- **Component Reusability**: > 70%
- **PropTypes Coverage**: 100% (TypeScript migration in future)
- **Documentation Coverage**: 100%
- **Build Time**: < 30s
- **Test Execution Time**: < 2 minutes

### Business Impact
- **Feature Development Speed**: 2x faster
- **Bug Rate**: 50% reduction
- **Developer Onboarding**: < 1 week
- **Deployment Frequency**: Daily
- **Mean Time to Recovery**: < 30 minutes

---

## Risk Mitigation

### Technical Risks
1. **Breaking Changes During Migration**
   - Mitigation: Feature flags for gradual rollout
   - Fallback: Keep old code paths temporarily

2. **Performance Regression**
   - Mitigation: Performance budget monitoring
   - Fallback: Rollback deployment

3. **State Management Complexity**
   - Mitigation: Start simple, add complexity as needed
   - Fallback: Local component state

### Business Risks
1. **Development Slowdown**
   - Mitigation: Parallel development tracks
   - Fallback: Pause refactoring if needed

2. **Feature Delivery Delay**
   - Mitigation: Phase approach allows feature work
   - Fallback: Critical features first

---

## Conclusion

This refactoring plan transforms the current MVP into a production-ready, scalable application. The phased approach ensures continuous delivery while improving code quality. Each phase builds upon the previous, creating a solid foundation for the ambitious AI-powered features planned for the Google Ads management platform.

The investment in proper architecture will pay dividends through:
- Faster feature development
- Fewer bugs and regressions  
- Easier team collaboration
- Better performance
- Enhanced security
- Improved developer experience

Start with Phase 1 immediately as the routing system is critically broken and blocking proper feature development.

### Updated Approach Based on 2025 Best Practices:
1. **React 19 eliminates need for manual optimization** - No more useMemo/useCallback/React.memo
2. **Pure Tailwind approach** - Avoid CSS Modules hybrid for better performance
3. **Context API is sufficient** - No need for external state management in MVP
4. **Focus on core functionality** - Skip Storybook, complex testing for initial refactor
5. **Use modern build targets** - ESNext with esbuild for faster builds