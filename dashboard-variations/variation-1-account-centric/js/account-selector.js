// Account Selector Functionality
document.addEventListener('DOMContentLoaded', function() {
    const accountSelector = document.getElementById('accountSelector');
    const accountDropdown = document.getElementById('accountDropdown');
    const accountItems = document.querySelectorAll('.account-item');
    const allAccountsView = document.querySelector('.all-accounts-view');
    const singleAccountView = document.querySelector('.single-account-view');
    const pageTitle = document.querySelector('.page-title');
    const pageDescription = document.querySelector('.page-description');

    // Toggle dropdown
    accountSelector.addEventListener('click', function(e) {
        e.stopPropagation();
        accountDropdown.classList.toggle('show');
        accountSelector.classList.toggle('active');
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', function() {
        accountDropdown.classList.remove('show');
        accountSelector.classList.remove('active');
    });

    // Prevent dropdown from closing when clicking inside
    accountDropdown.addEventListener('click', function(e) {
        e.stopPropagation();
    });

    // Handle account selection
    accountItems.forEach(item => {
        item.addEventListener('click', function() {
            // Remove selected class from all items
            accountItems.forEach(i => i.classList.remove('selected'));
            
            // Add selected class to clicked item
            this.classList.add('selected');

            // Get account data
            const accountId = this.dataset.account;
            const accountName = this.querySelector('.account-info strong').textContent;
            const accountDetails = this.querySelector('.account-info span').textContent;

            // Update selector display
            const selectedAccount = accountSelector.querySelector('.selected-account');
            
            if (accountId === 'all') {
                selectedAccount.innerHTML = `
                    <i data-feather="briefcase"></i>
                    <span class="account-name">All Accounts</span>
                    <span class="account-count">(23 Active)</span>
                `;
                
                // Show all accounts view
                allAccountsView.style.display = 'grid';
                singleAccountView.style.display = 'none';
                
                // Update page header
                pageTitle.textContent = 'All Accounts Overview';
                pageDescription.textContent = 'Monitor performance across all 23 client accounts';
            } else {
                selectedAccount.innerHTML = `
                    <i data-feather="briefcase"></i>
                    <span class="account-name">${accountName}</span>
                    <span class="account-count">${accountDetails.split('•')[0].trim()}</span>
                `;
                
                // Show single account view
                allAccountsView.style.display = 'none';
                singleAccountView.style.display = 'grid';
                
                // Update page header
                pageTitle.textContent = accountName;
                pageDescription.textContent = `Account Details • ${accountDetails}`;
            }

            // Replace feather icons
            feather.replace();

            // Close dropdown
            accountDropdown.classList.remove('show');
            accountSelector.classList.remove('active');
        });
    });

    // Search functionality
    const searchInput = document.querySelector('.account-search');
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        
        accountItems.forEach(item => {
            const accountName = item.querySelector('.account-info strong').textContent.toLowerCase();
            if (accountName.includes(searchTerm) || item.dataset.account === 'all') {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        });
    });
});