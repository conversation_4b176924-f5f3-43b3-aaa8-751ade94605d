<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AdsAI - Variation 1: Account-Centric Dashboard</title>
    
    <!-- Brand Wisdom Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Jost:ital,wght@0,100..900;1,100..900&family=Playfair+Display:ital,wght@0,400..900;1,400..900&display=swap" rel="stylesheet">
    
    <!-- Feather Icons -->
    <script src="https://unpkg.com/feather-icons"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/theme.css">
    <link rel="stylesheet" href="css/layout.css">
</head>
<body>
    <!-- Account Selector Bar (Always Visible) -->
    <header class="account-selector-bar">
        <div class="brand-section">
            <img src="assets/brand-wisdom-icon.png" alt="Brand Wisdom" class="brand-icon">
            <div class="brand-text">
                <h1>AdsAI Platform</h1>
                <span class="brand-tagline">Google Ads Management</span>
            </div>
        </div>
        
        <div class="account-selector-section">
            <button class="account-selector-trigger" id="accountSelector">
                <div class="selected-account">
                    <i data-feather="briefcase"></i>
                    <span class="account-name">All Accounts</span>
                    <span class="account-count">(23 Active)</span>
                </div>
                <i data-feather="chevron-down" class="dropdown-icon"></i>
            </button>
            
            <!-- Account Dropdown -->
            <div class="account-dropdown" id="accountDropdown">
                <div class="dropdown-header">
                    <input type="search" placeholder="Search accounts..." class="account-search">
                </div>
                <div class="account-list">
                    <div class="account-item selected" data-account="all">
                        <i data-feather="grid"></i>
                        <div class="account-info">
                            <strong>All Accounts Overview</strong>
                            <span>23 accounts • ₹40.4L monthly spend</span>
                        </div>
                    </div>
                    <div class="dropdown-divider"></div>
                    <div class="account-item" data-account="techstart">
                        <div class="account-status status-active"></div>
                        <div class="account-info">
                            <strong>TechStart Solutions</strong>
                            <span>₹5.2L/mo • 12 campaigns</span>
                        </div>
                        <span class="account-performance">+24%</span>
                    </div>
                    <div class="account-item" data-account="fashion">
                        <div class="account-status status-warning"></div>
                        <div class="account-info">
                            <strong>Fashion Forward</strong>
                            <span>₹3.8L/mo • 8 campaigns</span>
                        </div>
                        <span class="account-performance performance-down">-12%</span>
                    </div>
                    <div class="account-item" data-account="homeDecor">
                        <div class="account-status status-active"></div>
                        <div class="account-info">
                            <strong>Home Decor Pro</strong>
                            <span>₹2.1L/mo • 6 campaigns</span>
                        </div>
                        <span class="account-performance">+8%</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="header-actions">
            <button class="icon-button">
                <i data-feather="bell"></i>
                <span class="notification-badge">5</span>
            </button>
            <button class="icon-button">
                <i data-feather="command"></i>
            </button>
            <button class="user-menu">
                <img src="assets/avatar.jpg" alt="User" class="user-avatar">
            </button>
        </div>
    </header>

    <!-- Main Layout -->
    <div class="main-container">
        <!-- Sidebar Navigation -->
        <nav class="sidebar">
            <div class="nav-section">
                <h3 class="nav-section-title">Core Management</h3>
                <a href="#" class="nav-item active">
                    <i data-feather="home"></i>
                    <span>Dashboard</span>
                </a>
                <a href="#" class="nav-item">
                    <i data-feather="plus-circle"></i>
                    <span>Create Campaign</span>
                </a>
                <a href="#" class="nav-item">
                    <i data-feather="trending-up"></i>
                    <span>Performance</span>
                </a>
                <a href="#" class="nav-item">
                    <i data-feather="dollar-sign"></i>
                    <span>Budget Manager</span>
                </a>
            </div>
            
            <div class="nav-section">
                <h3 class="nav-section-title">AI Tools</h3>
                <a href="#" class="nav-item">
                    <i data-feather="search"></i>
                    <span>Search Mining</span>
                    <span class="ai-badge">AI</span>
                </a>
                <a href="#" class="nav-item">
                    <i data-feather="edit-3"></i>
                    <span>Ad Copy Lab</span>
                    <span class="ai-badge">AI</span>
                </a>
                <a href="#" class="nav-item">
                    <i data-feather="shield"></i>
                    <span>Negative Keywords</span>
                    <span class="ai-badge">AI</span>
                </a>
                <a href="#" class="nav-item">
                    <i data-feather="cpu"></i>
                    <span>Bid Intelligence</span>
                    <span class="ai-badge">AI</span>
                </a>
            </div>
        </nav>

        <!-- Main Content Area -->
        <main class="content-area">
            <!-- Context-Aware Header -->
            <div class="context-header">
                <div class="context-info">
                    <h2 class="page-title">All Accounts Overview</h2>
                    <p class="page-description">Monitor performance across all 23 client accounts</p>
                </div>
                <div class="context-actions">
                    <button class="btn btn-secondary">
                        <i data-feather="download"></i>
                        Export Report
                    </button>
                    <button class="btn btn-primary">
                        <i data-feather="plus"></i>
                        Quick Action
                    </button>
                </div>
            </div>

            <!-- Multi-Account Overview (Shows when "All Accounts" selected) -->
            <div class="dashboard-grid all-accounts-view">
                <!-- Account Health Overview -->
                <div class="card span-full">
                    <div class="card-header">
                        <h3>Account Health Summary</h3>
                        <span class="time-range">Last 30 days</span>
                    </div>
                    <div class="health-grid">
                        <div class="health-item healthy">
                            <div class="health-icon">
                                <i data-feather="check-circle"></i>
                            </div>
                            <div class="health-info">
                                <span class="health-count">15</span>
                                <span class="health-label">Healthy Accounts</span>
                            </div>
                        </div>
                        <div class="health-item warning">
                            <div class="health-icon">
                                <i data-feather="alert-triangle"></i>
                            </div>
                            <div class="health-info">
                                <span class="health-count">6</span>
                                <span class="health-label">Need Attention</span>
                            </div>
                        </div>
                        <div class="health-item critical">
                            <div class="health-icon">
                                <i data-feather="alert-circle"></i>
                            </div>
                            <div class="health-info">
                                <span class="health-count">2</span>
                                <span class="health-label">Critical Issues</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Aggregate Metrics -->
                <div class="card">
                    <div class="metric-card">
                        <div class="metric-icon blue">
                            <i data-feather="mouse-pointer"></i>
                        </div>
                        <div class="metric-content">
                            <span class="metric-label">Total Clicks</span>
                            <span class="metric-value">487.2K</span>
                            <span class="metric-change positive">+12.4%</span>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="metric-card">
                        <div class="metric-icon green">
                            <i data-feather="trending-up"></i>
                        </div>
                        <div class="metric-content">
                            <span class="metric-label">Avg. CTR</span>
                            <span class="metric-value">4.82%</span>
                            <span class="metric-change positive">+0.34%</span>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="metric-card">
                        <div class="metric-icon purple">
                            <i data-feather="credit-card"></i>
                        </div>
                        <div class="metric-content">
                            <span class="metric-label">Total Spend</span>
                            <span class="metric-value">₹40.4L</span>
                            <span class="metric-change">On Budget</span>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="metric-card">
                        <div class="metric-icon orange">
                            <i data-feather="target"></i>
                        </div>
                        <div class="metric-content">
                            <span class="metric-label">Conversions</span>
                            <span class="metric-value">12,847</span>
                            <span class="metric-change positive">+18.2%</span>
                        </div>
                    </div>
                </div>

                <!-- Top Performers & Underperformers -->
                <div class="card">
                    <div class="card-header">
                        <h3>Top Performing Accounts</h3>
                    </div>
                    <div class="account-performance-list">
                        <div class="performance-item">
                            <div class="account-name">TechStart Solutions</div>
                            <div class="performance-metric">
                                <span class="metric">ROAS: 4.2x</span>
                                <span class="change positive">+24%</span>
                            </div>
                        </div>
                        <div class="performance-item">
                            <div class="account-name">Beauty Boutique</div>
                            <div class="performance-metric">
                                <span class="metric">ROAS: 3.8x</span>
                                <span class="change positive">+18%</span>
                            </div>
                        </div>
                        <div class="performance-item">
                            <div class="account-name">Fitness Pro</div>
                            <div class="performance-metric">
                                <span class="metric">ROAS: 3.5x</span>
                                <span class="change positive">+15%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3>Accounts Needing Attention</h3>
                    </div>
                    <div class="account-performance-list">
                        <div class="performance-item alert">
                            <div class="account-name">Fashion Forward</div>
                            <div class="performance-metric">
                                <span class="metric">CPC: ₹42</span>
                                <span class="change negative">+35%</span>
                            </div>
                        </div>
                        <div class="performance-item alert">
                            <div class="account-name">Local Services Hub</div>
                            <div class="performance-metric">
                                <span class="metric">CTR: 1.2%</span>
                                <span class="change negative">-28%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- AI Recommendations Across Accounts -->
                <div class="card span-2">
                    <div class="card-header">
                        <h3>Cross-Account AI Insights</h3>
                        <span class="ai-badge">AI Powered</span>
                    </div>
                    <div class="insights-list">
                        <div class="insight-item">
                            <div class="insight-icon">
                                <i data-feather="trending-up"></i>
                            </div>
                            <div class="insight-content">
                                <h4>Common High-Performing Keywords Found</h4>
                                <p>5 keywords performing well across multiple accounts could be applied to 8 other accounts</p>
                                <button class="btn btn-small">View Keywords</button>
                            </div>
                        </div>
                        <div class="insight-item">
                            <div class="insight-icon">
                                <i data-feather="alert-circle"></i>
                            </div>
                            <div class="insight-content">
                                <h4>Budget Exhaustion Pattern Detected</h4>
                                <p>6 accounts consistently exhaust budget by 3 PM. Consider dayparting adjustments.</p>
                                <button class="btn btn-small">Optimize Schedules</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Single Account View (Hidden by default, shown when account selected) -->
            <div class="dashboard-grid single-account-view" style="display: none;">
                <!-- Account-Specific Metrics -->
                <div class="card span-full">
                    <div class="account-header">
                        <div class="account-overview">
                            <h3>TechStart Solutions</h3>
                            <span class="account-id">MCC: 123-456-7890</span>
                        </div>
                        <div class="account-quick-stats">
                            <div class="quick-stat">
                                <span class="stat-label">Monthly Budget</span>
                                <span class="stat-value">₹5.2L</span>
                            </div>
                            <div class="quick-stat">
                                <span class="stat-label">Active Campaigns</span>
                                <span class="stat-value">12</span>
                            </div>
                            <div class="quick-stat">
                                <span class="stat-label">Account Health</span>
                                <span class="stat-value health-good">Excellent</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Account-specific content would go here -->
            </div>
        </main>
    </div>

    <script src="js/account-selector.js"></script>
    <script>
        feather.replace();
    </script>
</body>
</html>