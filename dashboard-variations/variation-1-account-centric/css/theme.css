/* Brand Wisdom Theme Variables */
:root {
    /* Brand Colors */
    --primary-purple: #6c5ce7;
    --primary-blue: #4CAF50;
    --secondary-orange: #EC7063;
    --secondary-green: #04c999;
    --accent-yellow: #f7dc6f;
    
    /* Neutral Colors */
    --gray-900: #1f2937;
    --gray-800: #374151;
    --gray-700: #4b5563;
    --gray-600: #6b7280;
    --gray-500: #9ca3af;
    --gray-400: #d1d5db;
    --gray-300: #e5e7eb;
    --gray-200: #f3f4f6;
    --gray-100: #f9fafb;
    --white: #ffffff;
    
    /* Semantic Colors */
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #3b82f6;
    
    /* Typography */
    --font-primary: 'Jost', -apple-system, BlinkMacSystemFont, sans-serif;
    --font-heading: 'Playfair Display', Georgia, serif;
    
    /* Spacing */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-full: 9999px;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
    
    /* Transitions */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-base: 200ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Base Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-primary);
    font-size: 16px;
    line-height: 1.5;
    color: var(--gray-900);
    background-color: var(--gray-100);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    font-weight: 600;
    line-height: 1.2;
}

h1 { font-size: 1.5rem; }
h2 { font-size: 1.875rem; }
h3 { font-size: 1.25rem; }
h4 { font-size: 1.125rem; }

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-sm) var(--space-lg);
    font-family: var(--font-primary);
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: var(--radius-md);
    border: none;
    cursor: pointer;
    transition: all var(--transition-base);
    text-decoration: none;
}

.btn-primary {
    background-color: var(--primary-purple);
    color: var(--white);
}

.btn-primary:hover {
    background-color: #5f4ddb;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background-color: var(--white);
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.btn-secondary:hover {
    background-color: var(--gray-50);
    border-color: var(--gray-400);
}

.btn-small {
    padding: var(--space-xs) var(--space-md);
    font-size: 0.75rem;
}

/* Cards */
.card {
    background-color: var(--white);
    border-radius: var(--radius-lg);
    padding: var(--space-lg);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-base);
}

.card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-lg);
}

.card-header h3 {
    font-size: 1.125rem;
    color: var(--gray-900);
}

/* Icon Buttons */
.icon-button {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: var(--radius-md);
    background-color: transparent;
    border: none;
    cursor: pointer;
    transition: all var(--transition-base);
}

.icon-button:hover {
    background-color: var(--gray-100);
}

.notification-badge {
    position: absolute;
    top: -4px;
    right: -4px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    background-color: var(--error);
    color: var(--white);
    font-size: 0.625rem;
    font-weight: 600;
    border-radius: var(--radius-full);
}

/* AI Badge */
.ai-badge {
    display: inline-flex;
    align-items: center;
    padding: 2px 8px;
    background-color: var(--primary-purple);
    color: var(--white);
    font-size: 0.625rem;
    font-weight: 600;
    border-radius: var(--radius-full);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Status Indicators */
.status-active {
    width: 8px;
    height: 8px;
    background-color: var(--success);
    border-radius: var(--radius-full);
}

.status-warning {
    width: 8px;
    height: 8px;
    background-color: var(--warning);
    border-radius: var(--radius-full);
}

.status-error {
    width: 8px;
    height: 8px;
    background-color: var(--error);
    border-radius: var(--radius-full);
}

/* Performance Indicators */
.positive {
    color: var(--success);
}

.negative {
    color: var(--error);
}

.performance-down {
    color: var(--error);
}