/* Account-Centric Dashboard Layout */

/* Account Selector Bar (Top Bar) */
.account-selector-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 64px;
    background-color: var(--white);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--space-lg);
    z-index: 1000;
    box-shadow: var(--shadow-sm);
}

.brand-section {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.brand-icon {
    width: 40px;
    height: 40px;
}

.brand-text h1 {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
}

.brand-tagline {
    font-size: 0.75rem;
    color: var(--gray-600);
}

/* Account Selector */
.account-selector-section {
    flex: 1;
    max-width: 400px;
    margin: 0 var(--space-xl);
    position: relative;
}

.account-selector-trigger {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-sm) var(--space-md);
    background-color: var(--gray-50);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-base);
}

.account-selector-trigger:hover {
    background-color: var(--gray-100);
    border-color: var(--primary-purple);
}

.selected-account {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.account-name {
    font-weight: 600;
    color: var(--gray-900);
}

.account-count {
    font-size: 0.875rem;
    color: var(--gray-600);
}

.dropdown-icon {
    width: 20px;
    height: 20px;
    color: var(--gray-600);
    transition: transform var(--transition-base);
}

.account-selector-trigger.active .dropdown-icon {
    transform: rotate(180deg);
}

/* Account Dropdown */
.account-dropdown {
    position: absolute;
    top: calc(100% + 8px);
    left: 0;
    right: 0;
    background-color: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    max-height: 400px;
    overflow: hidden;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-base);
}

.account-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-header {
    padding: var(--space-md);
    border-bottom: 1px solid var(--gray-200);
}

.account-search {
    width: 100%;
    padding: var(--space-sm) var(--space-md);
    background-color: var(--gray-50);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
}

.account-list {
    max-height: 320px;
    overflow-y: auto;
}

.account-item {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    padding: var(--space-md) var(--space-lg);
    cursor: pointer;
    transition: background-color var(--transition-base);
}

.account-item:hover {
    background-color: var(--gray-50);
}

.account-item.selected {
    background-color: var(--primary-purple);
    background-opacity: 0.1;
}

.account-item.selected .account-info strong {
    color: var(--primary-purple);
}

.account-info {
    flex: 1;
}

.account-info strong {
    display: block;
    font-size: 0.875rem;
    color: var(--gray-900);
}

.account-info span {
    font-size: 0.75rem;
    color: var(--gray-600);
}

.account-performance {
    font-size: 0.875rem;
    font-weight: 600;
}

.dropdown-divider {
    height: 1px;
    background-color: var(--gray-200);
    margin: var(--space-sm) 0;
}

/* Header Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.user-menu {
    display: flex;
    align-items: center;
    padding: var(--space-xs);
    background: none;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: background-color var(--transition-base);
}

.user-menu:hover {
    background-color: var(--gray-100);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: var(--radius-full);
}

/* Main Container */
.main-container {
    display: flex;
    padding-top: 64px;
    height: 100vh;
}

/* Sidebar */
.sidebar {
    width: 240px;
    background-color: var(--white);
    border-right: 1px solid var(--gray-200);
    padding: var(--space-lg);
    overflow-y: auto;
}

.nav-section {
    margin-bottom: var(--space-xl);
}

.nav-section-title {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: var(--space-sm);
}

.nav-item {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-sm) var(--space-md);
    color: var(--gray-700);
    text-decoration: none;
    border-radius: var(--radius-md);
    transition: all var(--transition-base);
    margin-bottom: var(--space-xs);
    font-size: 0.875rem;
}

.nav-item:hover {
    background-color: var(--gray-100);
    color: var(--gray-900);
}

.nav-item.active {
    background-color: var(--primary-purple);
    color: var(--white);
}

.nav-item svg {
    width: 18px;
    height: 18px;
}

/* Content Area */
.content-area {
    flex: 1;
    overflow-y: auto;
    padding: var(--space-xl);
}

/* Context Header */
.context-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-xl);
}

.page-title {
    font-size: 1.875rem;
    color: var(--gray-900);
    margin-bottom: var(--space-xs);
}

.page-description {
    color: var(--gray-600);
    font-size: 0.875rem;
}

.context-actions {
    display: flex;
    gap: var(--space-md);
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-lg);
}

.span-full {
    grid-column: 1 / -1;
}

.span-2 {
    grid-column: span 2;
}

/* Health Grid */
.health-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-lg);
}

.health-item {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    padding: var(--space-lg);
    background-color: var(--gray-50);
    border-radius: var(--radius-md);
}

.health-item.healthy {
    background-color: rgba(16, 185, 129, 0.1);
}

.health-item.warning {
    background-color: rgba(245, 158, 11, 0.1);
}

.health-item.critical {
    background-color: rgba(239, 68, 68, 0.1);
}

.health-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    border-radius: var(--radius-md);
}

.health-item.healthy .health-icon {
    background-color: var(--success);
    color: var(--white);
}

.health-item.warning .health-icon {
    background-color: var(--warning);
    color: var(--white);
}

.health-item.critical .health-icon {
    background-color: var(--error);
    color: var(--white);
}

.health-count {
    display: block;
    font-size: 1.875rem;
    font-weight: 700;
    line-height: 1;
}

.health-label {
    display: block;
    font-size: 0.875rem;
    color: var(--gray-600);
}

/* Metric Cards */
.metric-card {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.metric-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    border-radius: var(--radius-md);
}

.metric-icon.blue {
    background-color: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

.metric-icon.green {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success);
}

.metric-icon.purple {
    background-color: rgba(108, 92, 231, 0.1);
    color: var(--primary-purple);
}

.metric-icon.orange {
    background-color: rgba(236, 112, 99, 0.1);
    color: var(--secondary-orange);
}

.metric-content {
    flex: 1;
}

.metric-label {
    display: block;
    font-size: 0.75rem;
    color: var(--gray-600);
    margin-bottom: var(--space-xs);
}

.metric-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-xs);
}

.metric-change {
    font-size: 0.75rem;
    font-weight: 600;
}

/* Performance Lists */
.account-performance-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
}

.performance-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-md);
    background-color: var(--gray-50);
    border-radius: var(--radius-md);
}

.performance-item.alert {
    background-color: rgba(239, 68, 68, 0.05);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.performance-metric {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.performance-metric .metric {
    font-size: 0.875rem;
    color: var(--gray-700);
}

.performance-metric .change {
    font-size: 0.75rem;
    font-weight: 600;
}

/* Insights */
.insights-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-md);
}

.insight-item {
    display: flex;
    gap: var(--space-md);
    padding: var(--space-md);
    background-color: var(--gray-50);
    border-radius: var(--radius-md);
    border-left: 4px solid var(--primary-purple);
}

.insight-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: var(--white);
    border-radius: var(--radius-md);
    color: var(--primary-purple);
}

.insight-content {
    flex: 1;
}

.insight-content h4 {
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: var(--space-xs);
}

.insight-content p {
    font-size: 0.75rem;
    color: var(--gray-600);
    margin-bottom: var(--space-sm);
}

/* Account Header */
.account-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-lg);
    background-color: var(--gray-50);
    border-radius: var(--radius-md);
}

.account-overview h3 {
    font-size: 1.5rem;
    margin-bottom: var(--space-xs);
}

.account-id {
    font-size: 0.875rem;
    color: var(--gray-600);
}

.account-quick-stats {
    display: flex;
    gap: var(--space-xl);
}

.quick-stat {
    text-align: center;
}

.stat-label {
    display: block;
    font-size: 0.75rem;
    color: var(--gray-600);
    margin-bottom: var(--space-xs);
}

.stat-value {
    display: block;
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--gray-900);
}

.health-good {
    color: var(--success);
}