<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AdsAI Dashboard Variations</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            padding: 2rem;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        h1 {
            text-align: center;
            margin-bottom: 1rem;
            color: #1f2937;
        }
        
        .subtitle {
            text-align: center;
            color: #6b7280;
            margin-bottom: 3rem;
        }
        
        .variations-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .variation-card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .variation-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }
        
        .variation-number {
            display: inline-block;
            width: 32px;
            height: 32px;
            background-color: #6c5ce7;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 32px;
            font-weight: bold;
            margin-bottom: 1rem;
        }
        
        h2 {
            color: #1f2937;
            margin-bottom: 0.5rem;
        }
        
        .variation-tagline {
            color: #6b7280;
            font-size: 0.875rem;
            margin-bottom: 1rem;
        }
        
        .variation-description {
            color: #4b5563;
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }
        
        .features-list {
            list-style: none;
            margin-bottom: 1.5rem;
        }
        
        .features-list li {
            position: relative;
            padding-left: 1.5rem;
            margin-bottom: 0.5rem;
            color: #4b5563;
            font-size: 0.875rem;
        }
        
        .features-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #10b981;
            font-weight: bold;
        }
        
        .view-btn {
            display: inline-block;
            width: 100%;
            padding: 0.75rem 1.5rem;
            background-color: #6c5ce7;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            text-align: center;
            font-weight: 500;
            transition: background-color 0.2s;
        }
        
        .view-btn:hover {
            background-color: #5f4ddb;
        }
        
        .comparison-link {
            text-align: center;
            margin-top: 3rem;
        }
        
        .comparison-link a {
            color: #6c5ce7;
            text-decoration: none;
            font-weight: 500;
        }
        
        .comparison-link a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>AdsAI Dashboard UX Variations</h1>
        <p class="subtitle">Three distinct approaches to Google Ads campaign management</p>
        
        <div class="variations-grid">
            <!-- Variation 1 -->
            <div class="variation-card">
                <span class="variation-number">1</span>
                <h2>Account-Centric Dashboard</h2>
                <p class="variation-tagline">Multi-account management with contextual switching</p>
                <p class="variation-description">
                    Traditional yet powerful approach with a persistent account selector at the top. 
                    Perfect for agency directors who need to switch between client accounts frequently.
                </p>
                <ul class="features-list">
                    <li>Top account selector bar</li>
                    <li>Contextual dashboard content</li>
                    <li>Account health overview</li>
                    <li>Traditional sidebar navigation</li>
                </ul>
                <a href="variation-1-account-centric/index.html" class="view-btn">View Variation 1</a>
            </div>
            
            <!-- Variation 2 -->
            <div class="variation-card">
                <span class="variation-number">2</span>
                <h2>Activity-Stream Dashboard</h2>
                <p class="variation-tagline">Real-time monitoring with timeline-based updates</p>
                <p class="variation-description">
                    Modern social media-inspired design with a live activity feed. 
                    Ideal for team leads who need to monitor multiple campaigns in real-time.
                </p>
                <ul class="features-list">
                    <li>Live activity timeline</li>
                    <li>Dark sidebar navigation</li>
                    <li>Real-time status indicators</li>
                    <li>Summary panels</li>
                </ul>
                <a href="variation-2-activity-stream/index.html" class="view-btn">View Variation 2</a>
            </div>
            
            <!-- Variation 3 -->
            <div class="variation-card">
                <span class="variation-number">3</span>
                <h2>Command-Center Dashboard</h2>
                <p class="variation-tagline">Task-focused with keyboard-driven workflows</p>
                <p class="variation-description">
                    Developer-inspired dark theme with command palette and shortcuts. 
                    Built for power users who value speed and efficiency above all.
                </p>
                <ul class="features-list">
                    <li>Command palette (Ctrl+K)</li>
                    <li>Priority task queue</li>
                    <li>AI terminal interface</li>
                    <li>Keyboard shortcuts</li>
                </ul>
                <a href="variation-3-command-center/index.html" class="view-btn">View Variation 3</a>
            </div>
        </div>
        
        <div class="comparison-link">
            <a href="COMPARISON.md">View Detailed Comparison →</a>
        </div>
    </div>
</body>
</html>