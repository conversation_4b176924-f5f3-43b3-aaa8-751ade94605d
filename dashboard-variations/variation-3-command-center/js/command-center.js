// Command Center Functionality
document.addEventListener('DOMContentLoaded', function() {
    const commandTrigger = document.getElementById('commandTrigger');
    const commandPalette = document.getElementById('commandPalette');
    const commandInput = document.querySelector('.command-input');
    
    // Toggle command palette
    commandTrigger.addEventListener('click', function() {
        toggleCommandPalette();
    });
    
    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl+K or Cmd+K to open command palette
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            toggleCommandPalette();
        }
        
        // ESC to close command palette
        if (e.key === 'Escape' && commandPalette.classList.contains('show')) {
            closeCommandPalette();
        }
        
        // Other shortcuts
        if (e.altKey) {
            switch(e.key) {
                case 'n':
                    e.preventDefault();
                    console.log('Create new campaign');
                    break;
                case 'a':
                    e.preventDefault();
                    console.log('Open Ad Copy Lab');
                    break;
                case 'i':
                    e.preventDefault();
                    console.log('Run AI Analysis');
                    break;
                case 'p':
                    e.preventDefault();
                    console.log('View Performance');
                    break;
                case 'k':
                    e.preventDefault();
                    console.log('Manage Negative Keywords');
                    break;
                case 'e':
                    e.preventDefault();
                    console.log('Export Report');
                    break;
            }
        }
    });
    
    // Command palette functions
    function toggleCommandPalette() {
        if (commandPalette.classList.contains('show')) {
            closeCommandPalette();
        } else {
            openCommandPalette();
        }
    }
    
    function openCommandPalette() {
        commandPalette.classList.add('show');
        commandInput.focus();
        commandInput.value = '';
    }
    
    function closeCommandPalette() {
        commandPalette.classList.remove('show');
        commandInput.blur();
    }
    
    // Click outside to close
    commandPalette.addEventListener('click', function(e) {
        if (e.target === commandPalette) {
            closeCommandPalette();
        }
    });
    
    // Command search functionality
    commandInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const commandItems = document.querySelectorAll('.command-item');
        
        commandItems.forEach(item => {
            const text = item.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        });
    });
    
    // Command item clicks
    document.querySelectorAll('.command-item').forEach(item => {
        item.addEventListener('click', function() {
            const command = this.querySelector('span').textContent;
            console.log('Executing command:', command);
            closeCommandPalette();
        });
    });
    
    // Action card clicks
    document.querySelectorAll('.action-card').forEach(card => {
        card.addEventListener('click', function() {
            const action = this.querySelector('.action-label').textContent;
            console.log('Quick action:', action);
        });
    });
    
    // Priority card actions
    document.querySelectorAll('.action-primary, .action-secondary').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.stopPropagation();
            console.log('Action clicked:', this.textContent);
        });
    });
    
    // AI Terminal functionality
    const terminalInput = document.querySelector('.terminal-command');
    const terminalSend = document.querySelector('.terminal-send');
    const terminalMessages = document.querySelector('.terminal-messages');
    const promptChips = document.querySelectorAll('.prompt-chip');
    
    // Send message
    function sendTerminalMessage() {
        const message = terminalInput.value.trim();
        if (!message) return;
        
        // Add user message
        addTerminalMessage('You', message, 'user');
        
        // Clear input
        terminalInput.value = '';
        
        // Simulate AI response
        setTimeout(() => {
            const aiResponse = getAIResponse(message);
            addTerminalMessage('AI Assistant', aiResponse, 'ai');
        }, 1000);
    }
    
    // Add message to terminal
    function addTerminalMessage(sender, message, type) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `terminal-message ${type}`;
        messageDiv.innerHTML = `
            <strong>${sender}:</strong>
            <p>${message}</p>
        `;
        terminalMessages.appendChild(messageDiv);
        terminalMessages.scrollTop = terminalMessages.scrollHeight;
    }
    
    // Simulate AI responses
    function getAIResponse(message) {
        const responses = {
            'why did cpc increase': 'CPC increased by 12% due to: 1) Increased competition on your top keywords from 2 new competitors, 2) Lower Quality Scores on 5 high-volume keywords, 3) Seasonal demand increase in your industry.',
            'optimize budgets': 'I\'ve analyzed your budget allocation. Recommendations: 1) Shift ₹15K from underperforming Display campaigns to high-ROAS Search campaigns, 2) Implement dayparting for Fashion Forward account to prevent early budget exhaustion.',
            'find opportunities': 'Found 3 immediate opportunities: 1) 47 profitable search terms with 3.8x ROAS potential, 2) 12 competitor keywords with low competition, 3) Shopping campaign expansion opportunity for 23 products.'
        };
        
        const lowerMessage = message.toLowerCase();
        for (const [key, response] of Object.entries(responses)) {
            if (lowerMessage.includes(key.toLowerCase())) {
                return response;
            }
        }
        
        return 'I\'m analyzing your request. Based on current data, I recommend reviewing the priority tasks in your dashboard for immediate optimization opportunities.';
    }
    
    // Terminal send button
    terminalSend.addEventListener('click', sendTerminalMessage);
    
    // Terminal input enter key
    terminalInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            sendTerminalMessage();
        }
    });
    
    // Quick prompt chips
    promptChips.forEach(chip => {
        chip.addEventListener('click', function() {
            terminalInput.value = this.textContent;
            terminalInput.focus();
        });
    });
    
    // Animate progress bars
    const progressBars = document.querySelectorAll('.progress-fill');
    progressBars.forEach(bar => {
        const width = bar.style.width;
        bar.style.width = '0';
        setTimeout(() => {
            bar.style.width = width;
        }, 500);
    });
});