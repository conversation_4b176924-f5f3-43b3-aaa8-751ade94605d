<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AdsAI - Variation 3: Command Center Dashboard</title>
    
    <!-- Brand Wisdom Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Jost:ital,wght@0,100..900;1,100..900&family=Playfair+Display:ital,wght@0,400..900;1,400..900&display=swap" rel="stylesheet">
    
    <!-- Feather Icons -->
    <script src="https://unpkg.com/feather-icons"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../variation-1-account-centric/css/theme.css">
    <link rel="stylesheet" href="css/command-layout.css">
</head>
<body>
    <!-- Command Center Header -->
    <header class="command-header">
        <div class="header-brand">
            <img src="assets/brand-wisdom-icon.png" alt="Brand Wisdom" class="brand-icon">
            <h1>AdsAI Command Center</h1>
        </div>
        
        <!-- Command Bar -->
        <div class="command-bar">
            <button class="command-trigger" id="commandTrigger">
                <i data-feather="command"></i>
                <span>Quick Command</span>
                <kbd>Ctrl+K</kbd>
            </button>
        </div>
        
        <div class="header-tools">
            <div class="account-switcher">
                <select class="account-select">
                    <option value="all">All Accounts (23)</option>
                    <option value="techstart">TechStart Solutions</option>
                    <option value="fashion">Fashion Forward</option>
                    <option value="beauty">Beauty Boutique</option>
                </select>
            </div>
            <button class="icon-button">
                <i data-feather="bell"></i>
                <span class="notification-dot"></span>
            </button>
            <button class="icon-button">
                <i data-feather="settings"></i>
            </button>
        </div>
    </header>

    <!-- Command Palette (Hidden by default) -->
    <div class="command-palette" id="commandPalette">
        <div class="command-search">
            <i data-feather="search"></i>
            <input type="text" placeholder="Type a command or search..." class="command-input">
            <kbd>ESC</kbd>
        </div>
        <div class="command-results">
            <div class="command-section">
                <h4>Quick Actions</h4>
                <div class="command-item">
                    <i data-feather="plus-circle"></i>
                    <span>Create New Campaign</span>
                    <kbd>C</kbd>
                </div>
                <div class="command-item">
                    <i data-feather="pause-circle"></i>
                    <span>Pause Underperforming Campaigns</span>
                </div>
                <div class="command-item">
                    <i data-feather="download"></i>
                    <span>Export Performance Report</span>
                </div>
            </div>
            <div class="command-section">
                <h4>AI Tools</h4>
                <div class="command-item">
                    <i data-feather="cpu"></i>
                    <span>Run Search Query Mining</span>
                    <span class="ai-indicator">AI</span>
                </div>
                <div class="command-item">
                    <i data-feather="edit-3"></i>
                    <span>Generate Ad Copy Variations</span>
                    <span class="ai-indicator">AI</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Dashboard -->
    <main class="command-dashboard">
        <!-- Task Priority Queue -->
        <section class="priority-section">
            <div class="section-header">
                <h2>Priority Tasks</h2>
                <span class="task-count">8 tasks require attention</span>
            </div>
            
            <div class="priority-grid">
                <div class="priority-card urgent">
                    <div class="priority-indicator"></div>
                    <div class="priority-content">
                        <h3>Budget Exhaustion Alert</h3>
                        <p>3 accounts will exhaust budget by 3 PM today</p>
                        <div class="task-meta">
                            <span class="impact">High Impact</span>
                            <span class="time-est">~5 min</span>
                        </div>
                        <div class="task-actions">
                            <button class="action-primary">Adjust Budgets</button>
                            <button class="action-secondary">View Details</button>
                        </div>
                    </div>
                </div>

                <div class="priority-card high">
                    <div class="priority-indicator"></div>
                    <div class="priority-content">
                        <h3>Quality Score Optimization</h3>
                        <p>12 keywords with QS < 5 affecting campaign performance</p>
                        <div class="task-meta">
                            <span class="impact">Medium Impact</span>
                            <span class="time-est">~15 min</span>
                        </div>
                        <div class="task-actions">
                            <button class="action-primary">Optimize Keywords</button>
                            <button class="action-secondary">AI Suggestions</button>
                        </div>
                    </div>
                </div>

                <div class="priority-card medium">
                    <div class="priority-indicator"></div>
                    <div class="priority-content">
                        <h3>New Search Terms Review</h3>
                        <p>47 profitable search terms identified across accounts</p>
                        <div class="task-meta">
                            <span class="impact">High Opportunity</span>
                            <span class="time-est">~10 min</span>
                        </div>
                        <div class="task-actions">
                            <button class="action-primary">Review & Apply</button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Quick Actions Dashboard -->
        <section class="actions-section">
            <h2>Quick Actions</h2>
            <div class="actions-grid">
                <button class="action-card">
                    <div class="action-icon green">
                        <i data-feather="plus-circle"></i>
                    </div>
                    <span class="action-label">Create Campaign</span>
                    <span class="action-shortcut">Alt+N</span>
                </button>
                
                <button class="action-card">
                    <div class="action-icon blue">
                        <i data-feather="edit-3"></i>
                    </div>
                    <span class="action-label">Ad Copy Lab</span>
                    <span class="action-shortcut">Alt+A</span>
                </button>
                
                <button class="action-card">
                    <div class="action-icon purple">
                        <i data-feather="cpu"></i>
                    </div>
                    <span class="action-label">AI Analysis</span>
                    <span class="action-shortcut">Alt+I</span>
                </button>
                
                <button class="action-card">
                    <div class="action-icon orange">
                        <i data-feather="trending-up"></i>
                    </div>
                    <span class="action-label">Performance</span>
                    <span class="action-shortcut">Alt+P</span>
                </button>
                
                <button class="action-card">
                    <div class="action-icon red">
                        <i data-feather="shield"></i>
                    </div>
                    <span class="action-label">Negative Keywords</span>
                    <span class="action-shortcut">Alt+K</span>
                </button>
                
                <button class="action-card">
                    <div class="action-icon yellow">
                        <i data-feather="download"></i>
                    </div>
                    <span class="action-label">Export Report</span>
                    <span class="action-shortcut">Alt+E</span>
                </button>
            </div>
        </section>

        <div class="dashboard-columns">
            <!-- Status Overview -->
            <section class="status-section">
                <h2>System Status</h2>
                
                <div class="status-grid">
                    <div class="status-card">
                        <div class="status-header">
                            <span class="status-label">Active Campaigns</span>
                            <span class="status-indicator good"></span>
                        </div>
                        <div class="status-value">156</div>
                        <div class="status-subtext">All running smoothly</div>
                    </div>
                    
                    <div class="status-card">
                        <div class="status-header">
                            <span class="status-label">Today's Spend</span>
                            <span class="status-indicator warning"></span>
                        </div>
                        <div class="status-value">₹1.34L</div>
                        <div class="status-subtext">85% of daily budget</div>
                    </div>
                    
                    <div class="status-card">
                        <div class="status-header">
                            <span class="status-label">Conversion Rate</span>
                            <span class="status-indicator good"></span>
                        </div>
                        <div class="status-value">4.2%</div>
                        <div class="status-subtext">+0.8% from yesterday</div>
                    </div>
                    
                    <div class="status-card">
                        <div class="status-header">
                            <span class="status-label">AI Tasks</span>
                            <span class="status-indicator processing"></span>
                        </div>
                        <div class="status-value">12</div>
                        <div class="status-subtext">Processing optimizations</div>
                    </div>
                </div>
                
                <!-- Recent Actions Log -->
                <div class="actions-log">
                    <h3>Recent Actions</h3>
                    <div class="log-items">
                        <div class="log-item">
                            <span class="log-time">2m</span>
                            <span class="log-action">Paused low-performing keywords</span>
                            <span class="log-result success">Saved ₹2.3K</span>
                        </div>
                        <div class="log-item">
                            <span class="log-time">15m</span>
                            <span class="log-action">Applied AI bid adjustments</span>
                            <span class="log-result success">+12% CTR</span>
                        </div>
                        <div class="log-item">
                            <span class="log-time">1h</span>
                            <span class="log-action">Created Performance Max campaign</span>
                            <span class="log-result">TechStart</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- AI Command Terminal -->
            <section class="ai-terminal">
                <div class="terminal-header">
                    <h2>AI Assistant</h2>
                    <span class="terminal-status">Ready</span>
                </div>
                
                <div class="terminal-content">
                    <div class="terminal-messages">
                        <div class="terminal-message ai">
                            <strong>AI Assistant:</strong>
                            <p>Good morning! I've identified 3 urgent tasks and 12 optimization opportunities across your accounts.</p>
                        </div>
                        <div class="terminal-message user">
                            <strong>You:</strong>
                            <p>Show me the urgent tasks</p>
                        </div>
                        <div class="terminal-message ai">
                            <strong>AI Assistant:</strong>
                            <p>Here are your urgent tasks:</p>
                            <ul>
                                <li>Budget exhaustion risk for 3 accounts (Fashion Forward, Beauty Pro, Local Services)</li>
                                <li>Quality Score dropped below 5 for 12 high-volume keywords</li>
                                <li>Competitor increased bids on your top keywords - immediate response recommended</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="terminal-input">
                        <input type="text" placeholder="Ask AI or type a command..." class="terminal-command">
                        <button class="terminal-send">
                            <i data-feather="send"></i>
                        </button>
                    </div>
                    
                    <div class="quick-prompts">
                        <button class="prompt-chip">Why did CPC increase?</button>
                        <button class="prompt-chip">Optimize budgets</button>
                        <button class="prompt-chip">Find opportunities</button>
                    </div>
                </div>
            </section>
        </div>

        <!-- Automation Queue -->
        <section class="automation-section">
            <div class="section-header">
                <h2>Automation Queue</h2>
                <button class="view-all-btn">View All</button>
            </div>
            
            <div class="automation-items">
                <div class="automation-item pending">
                    <div class="automation-status">
                        <i data-feather="clock"></i>
                    </div>
                    <div class="automation-details">
                        <h4>Negative Keyword Analysis</h4>
                        <p>Analyzing 2,847 search terms for wasted spend</p>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 65%"></div>
                        </div>
                    </div>
                    <div class="automation-eta">~3 min</div>
                </div>
                
                <div class="automation-item complete">
                    <div class="automation-status">
                        <i data-feather="check-circle"></i>
                    </div>
                    <div class="automation-details">
                        <h4>Bid Optimization Complete</h4>
                        <p>Adjusted bids for 156 keywords across 12 campaigns</p>
                        <span class="automation-result">Estimated savings: ₹4.2K/day</span>
                    </div>
                    <button class="review-btn">Review</button>
                </div>
            </div>
        </section>
    </main>

    <script src="js/command-center.js"></script>
    <script>
        feather.replace();
    </script>
</body>
</html>