# Complete Database Schema
# Google Ads AI Search Optimization Platform
# Version 2.1 - Enhanced for Premium UX

## What's New in Version 2.1

**Enhanced for Premium User Experience:**
- **UI State Persistence**: Save user's UI preferences, collapsed panels, active tabs
- **Form Draft Management**: Auto-save complex forms with React Hook Form
- **Interaction Analytics**: Track micro-interactions for UX optimization
- **A/B Testing Framework**: Test UI variations for continuous improvement
- **Enhanced User Preferences**: Support for animation preferences, themes, default views

These enhancements support the upgraded tech stack (React Hook Form, Framer Motion, Sonner) to deliver a premium user experience.

---

## Database Overview

**Database Type**: PostgreSQL (via Supabase)  
**Extensions Required**: 
- `uuid-ossp` (for UUID generation)
- `pgvector` (for semantic search and AI embeddings)
- `pg_cron` (for scheduled jobs)

---

## 1. Core User & Authentication Tables

```sql
-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgvector";
CREATE EXTENSION IF NOT EXISTS "pg_cron";

-- Users table (integrates with Supabase Auth)
CREATE TABLE users (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT,
    company_name TEXT,
    role TEXT CHECK (role IN ('admin', 'manager', 'specialist', 'viewer')) DEFAULT 'specialist',
    avatar_url TEXT,
    timezone TEXT DEFAULT 'UTC',
    notification_preferences JSONB DEFAULT '{"email": true, "push": false, "sms": false}'::jsonb,
    ui_preferences JSONB DEFAULT '{
        "theme": "light",
        "reducedMotion": false,
        "compactView": false,
        "defaultDateRange": "last_30_days",
        "chartAnimations": true,
        "autoPlayTutorials": true
    }'::jsonb,
    onboarding_completed BOOLEAN DEFAULT FALSE,
    onboarding_steps JSONB DEFAULT '[]'::jsonb, -- Track which onboarding animations were seen
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    last_login_at TIMESTAMPTZ,
    is_active BOOLEAN DEFAULT TRUE,
    metadata JSONB DEFAULT '{}'::jsonb
);

-- User sessions tracking
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    started_at TIMESTAMPTZ DEFAULT NOW(),
    ended_at TIMESTAMPTZ,
    ip_address INET,
    user_agent TEXT,
    actions_count INTEGER DEFAULT 0
);

-- Team management
CREATE TABLE teams (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    company_name TEXT,
    subscription_tier TEXT DEFAULT 'free',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    settings JSONB DEFAULT '{}'::jsonb
);

CREATE TABLE team_members (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    team_id UUID NOT NULL REFERENCES teams(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role TEXT CHECK (role IN ('owner', 'admin', 'member', 'viewer')) DEFAULT 'member',
    joined_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(team_id, user_id)
);
```

---

## 2. Google Ads Account Management

```sql
-- Google Ads accounts
CREATE TABLE google_ads_accounts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    currency_code TEXT DEFAULT 'USD',
    timezone TEXT,
    status TEXT CHECK (status IN ('active', 'paused', 'suspended', 'disconnected')) DEFAULT 'active',
    team_id UUID REFERENCES teams(id) ON DELETE CASCADE,
    connected_by UUID REFERENCES users(id),
    refresh_token TEXT ENCRYPTED, -- Supabase Vault for encryption
    access_token TEXT ENCRYPTED,
    token_expires_at TIMESTAMPTZ,
    last_sync_at TIMESTAMPTZ,
    sync_status TEXT DEFAULT 'pending',
    sync_error TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    settings JSONB DEFAULT '{
        "auto_optimize": true,
        "approval_required": false,
        "notification_threshold": {"spend": 1000, "cpa": 50}
    }'::jsonb,
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Account access permissions
CREATE TABLE account_permissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID NOT NULL REFERENCES google_ads_accounts(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    permission_level TEXT CHECK (permission_level IN ('read', 'write', 'admin')) DEFAULT 'read',
    granted_at TIMESTAMPTZ DEFAULT NOW(),
    granted_by UUID REFERENCES users(id),
    UNIQUE(account_id, user_id)
);
```

---

## 3. Search Query Mining Tables

```sql
-- Search terms analysis
CREATE TABLE search_terms (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID NOT NULL REFERENCES google_ads_accounts(id) ON DELETE CASCADE,
    campaign_id TEXT NOT NULL,
    ad_group_id TEXT NOT NULL,
    search_term TEXT NOT NULL,
    match_type TEXT,
    clicks INTEGER DEFAULT 0,
    impressions INTEGER DEFAULT 0,
    cost DECIMAL(10,2) DEFAULT 0,
    conversions DECIMAL(10,2) DEFAULT 0,
    conversion_value DECIMAL(10,2) DEFAULT 0,
    ctr DECIMAL(5,4),
    cpc DECIMAL(10,2),
    cpa DECIMAL(10,2),
    roas DECIMAL(10,2),
    quality_score INTEGER,
    date DATE NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    INDEX idx_search_terms_account_date (account_id, date),
    INDEX idx_search_terms_performance (account_id, conversions DESC, cost DESC)
);

-- Search term recommendations
CREATE TABLE search_term_recommendations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    search_term_id UUID NOT NULL REFERENCES search_terms(id) ON DELETE CASCADE,
    account_id UUID NOT NULL REFERENCES google_ads_accounts(id) ON DELETE CASCADE,
    recommendation_type TEXT CHECK (recommendation_type IN (
        'add_as_keyword', 
        'add_as_negative', 
        'add_to_negative_list',
        'increase_bid',
        'decrease_bid',
        'change_match_type'
    )) NOT NULL,
    confidence_score DECIMAL(3,2) CHECK (confidence_score >= 0 AND confidence_score <= 1),
    impact_estimate JSONB DEFAULT '{
        "clicks_saved": 0,
        "cost_saved": 0,
        "conversions_gained": 0
    }'::jsonb,
    reason TEXT,
    status TEXT CHECK (status IN ('pending', 'approved', 'rejected', 'applied', 'expired')) DEFAULT 'pending',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    reviewed_at TIMESTAMPTZ,
    reviewed_by UUID REFERENCES users(id),
    applied_at TIMESTAMPTZ,
    applied_by UUID REFERENCES users(id),
    expires_at TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '7 days'),
    INDEX idx_recommendations_status (account_id, status, created_at DESC)
);

-- Search query patterns (for ML)
CREATE TABLE search_query_patterns (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID NOT NULL REFERENCES google_ads_accounts(id) ON DELETE CASCADE,
    pattern TEXT NOT NULL,
    pattern_type TEXT CHECK (pattern_type IN ('ngram', 'intent', 'semantic', 'regex')),
    frequency INTEGER DEFAULT 1,
    total_cost DECIMAL(10,2) DEFAULT 0,
    total_conversions DECIMAL(10,2) DEFAULT 0,
    avg_cpa DECIMAL(10,2),
    classification TEXT,
    is_negative_candidate BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(account_id, pattern, pattern_type)
);
```

---

## 4. Search Intent Classification

```sql
-- Intent classifications
CREATE TABLE search_intents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    search_term_id UUID NOT NULL REFERENCES search_terms(id) ON DELETE CASCADE,
    account_id UUID NOT NULL REFERENCES google_ads_accounts(id) ON DELETE CASCADE,
    primary_intent TEXT CHECK (primary_intent IN (
        'transactional',
        'informational', 
        'navigational',
        'commercial_investigation'
    )) NOT NULL,
    secondary_intent TEXT,
    confidence_score DECIMAL(3,2),
    intent_signals JSONB DEFAULT '{}'::jsonb, -- Keywords that triggered classification
    bid_modifier_suggestion DECIMAL(3,2),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    INDEX idx_intents_account (account_id, primary_intent)
);

-- Intent-based optimizations
CREATE TABLE intent_optimizations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID NOT NULL REFERENCES google_ads_accounts(id) ON DELETE CASCADE,
    intent_type TEXT NOT NULL,
    optimization_type TEXT CHECK (optimization_type IN (
        'bid_adjustment',
        'ad_copy_variation',
        'landing_page_selection',
        'extension_selection'
    )),
    settings JSONB NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES users(id)
);
```

---

## 5. Ad Copy Laboratory

```sql
-- Ad copy templates and variations
CREATE TABLE ad_copies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID NOT NULL REFERENCES google_ads_accounts(id) ON DELETE CASCADE,
    campaign_id TEXT,
    ad_group_id TEXT,
    ad_type TEXT CHECK (ad_type IN ('responsive_search', 'expanded_text', 'call_only')) DEFAULT 'responsive_search',
    headlines JSONB NOT NULL, -- Array of headline objects with text and performance
    descriptions JSONB NOT NULL, -- Array of description objects
    final_urls JSONB,
    path1 TEXT,
    path2 TEXT,
    psychological_triggers JSONB DEFAULT '[]'::jsonb, -- Array of triggers used
    ai_generated BOOLEAN DEFAULT FALSE,
    ai_model TEXT,
    performance_score DECIMAL(3,2),
    status TEXT CHECK (status IN ('draft', 'testing', 'active', 'paused', 'ended')) DEFAULT 'draft',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES users(id),
    google_ads_id TEXT,
    INDEX idx_ad_copies_performance (account_id, performance_score DESC)
);

-- Ad copy tests
CREATE TABLE ad_copy_tests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID NOT NULL REFERENCES google_ads_accounts(id) ON DELETE CASCADE,
    ad_group_id TEXT NOT NULL,
    test_name TEXT NOT NULL,
    control_ad_id UUID REFERENCES ad_copies(id),
    variant_ad_ids UUID[] NOT NULL,
    test_type TEXT CHECK (test_type IN ('a/b', 'multivariate', 'psychological_triggers')),
    metrics_tracked JSONB DEFAULT '["ctr", "conversion_rate", "cpa"]'::jsonb,
    start_date DATE NOT NULL,
    end_date DATE,
    status TEXT CHECK (status IN ('planned', 'running', 'completed', 'cancelled')) DEFAULT 'planned',
    winner_ad_id UUID REFERENCES ad_copies(id),
    statistical_significance DECIMAL(3,2),
    results JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES users(id)
);

-- Psychological triggers library
CREATE TABLE psychological_triggers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    trigger_type TEXT NOT NULL CHECK (trigger_type IN (
        'urgency', 'scarcity', 'social_proof', 'authority',
        'reciprocity', 'commitment', 'liking', 'fear', 'greed'
    )),
    trigger_text TEXT NOT NULL,
    category TEXT,
    effectiveness_score DECIMAL(3,2),
    use_count INTEGER DEFAULT 0,
    industry_tags TEXT[],
    created_at TIMESTAMPTZ DEFAULT NOW()
);
```

---

## 6. Negative Keywords Management

```sql
-- Negative keyword lists
CREATE TABLE negative_keyword_lists (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID NOT NULL REFERENCES google_ads_accounts(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    list_type TEXT CHECK (list_type IN ('universal', 'campaign_specific', 'seasonal', 'competitor')) DEFAULT 'universal',
    is_shared BOOLEAN DEFAULT FALSE,
    google_ads_shared_set_id TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES users(id),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Negative keywords
CREATE TABLE negative_keywords (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    list_id UUID NOT NULL REFERENCES negative_keyword_lists(id) ON DELETE CASCADE,
    keyword TEXT NOT NULL,
    match_type TEXT CHECK (match_type IN ('broad', 'phrase', 'exact')) DEFAULT 'broad',
    added_from TEXT, -- Source: 'manual', 'ai_suggestion', 'search_term_mining'
    reason TEXT,
    impact_data JSONB DEFAULT '{
        "blocked_clicks": 0,
        "saved_cost": 0
    }'::jsonb,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES users(id),
    is_active BOOLEAN DEFAULT TRUE,
    UNIQUE(list_id, keyword, match_type)
);

-- Negative keyword suggestions from AI
CREATE TABLE negative_keyword_suggestions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID NOT NULL REFERENCES google_ads_accounts(id) ON DELETE CASCADE,
    keyword TEXT NOT NULL,
    match_type TEXT NOT NULL,
    source TEXT CHECK (source IN ('ai_analysis', 'pattern_detection', 'competitor_analysis', 'search_terms')),
    confidence_score DECIMAL(3,2),
    reason TEXT,
    potential_impact JSONB,
    status TEXT CHECK (status IN ('pending', 'approved', 'rejected', 'applied')) DEFAULT 'pending',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    reviewed_at TIMESTAMPTZ,
    reviewed_by UUID REFERENCES users(id)
);
```

---

## 7. Quality Score Optimization

```sql
-- Quality score tracking
CREATE TABLE quality_scores (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID NOT NULL REFERENCES google_ads_accounts(id) ON DELETE CASCADE,
    keyword_id TEXT NOT NULL,
    keyword_text TEXT NOT NULL,
    campaign_id TEXT NOT NULL,
    ad_group_id TEXT NOT NULL,
    quality_score INTEGER CHECK (quality_score >= 1 AND quality_score <= 10),
    landing_page_experience TEXT CHECK (landing_page_experience IN ('above_average', 'average', 'below_average')),
    ad_relevance TEXT CHECK (ad_relevance IN ('above_average', 'average', 'below_average')),
    expected_ctr TEXT CHECK (expected_ctr IN ('above_average', 'average', 'below_average')),
    historical_quality_score INTEGER,
    historical_landing_page_experience TEXT,
    historical_ad_relevance TEXT,
    historical_expected_ctr TEXT,
    first_page_cpc DECIMAL(10,2),
    top_of_page_cpc DECIMAL(10,2),
    recorded_date DATE NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    INDEX idx_quality_scores_date (account_id, recorded_date DESC),
    INDEX idx_quality_scores_score (account_id, quality_score)
);

-- Quality score improvement recommendations
CREATE TABLE qs_improvement_plans (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    quality_score_id UUID NOT NULL REFERENCES quality_scores(id) ON DELETE CASCADE,
    improvement_area TEXT CHECK (improvement_area IN (
        'landing_page_speed',
        'landing_page_relevance',
        'ad_keyword_relevance',
        'ctr_improvement',
        'ad_extensions'
    )),
    current_state TEXT,
    target_state TEXT,
    action_items JSONB NOT NULL, -- Array of specific actions
    priority INTEGER CHECK (priority >= 1 AND priority <= 5),
    estimated_impact INTEGER, -- Estimated QS improvement
    status TEXT CHECK (status IN ('pending', 'in_progress', 'completed', 'cancelled')) DEFAULT 'pending',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ
);
```

---

## 8. Bidding Intelligence

```sql
-- Bid strategies
CREATE TABLE bid_strategies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID NOT NULL REFERENCES google_ads_accounts(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    strategy_type TEXT CHECK (strategy_type IN (
        'manual_cpc',
        'enhanced_cpc',
        'target_cpa',
        'target_roas',
        'maximize_conversions',
        'maximize_conversion_value',
        'custom_rules'
    )) NOT NULL,
    settings JSONB NOT NULL,
    campaigns_applied TEXT[],
    is_active BOOLEAN DEFAULT TRUE,
    performance_metrics JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES users(id)
);

-- Bid adjustments
CREATE TABLE bid_adjustments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID NOT NULL REFERENCES google_ads_accounts(id) ON DELETE CASCADE,
    campaign_id TEXT NOT NULL,
    adjustment_type TEXT CHECK (adjustment_type IN (
        'device',
        'location',
        'ad_schedule',
        'audience',
        'demographic'
    )) NOT NULL,
    adjustment_value DECIMAL(3,2) NOT NULL, -- Percentage adjustment
    criteria JSONB NOT NULL, -- Specific criteria (e.g., {"device": "mobile"})
    reason TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ,
    performance_impact JSONB DEFAULT '{}'::jsonb
);

-- Dayparting schedules
CREATE TABLE dayparting_schedules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID NOT NULL REFERENCES google_ads_accounts(id) ON DELETE CASCADE,
    campaign_id TEXT NOT NULL,
    day_of_week INTEGER CHECK (day_of_week >= 0 AND day_of_week <= 6),
    hour_start INTEGER CHECK (hour_start >= 0 AND hour_start <= 23),
    hour_end INTEGER CHECK (hour_end >= 0 AND hour_end <= 23),
    bid_modifier DECIMAL(3,2),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW()
);
```

---

## 9. Ad Extensions Management

```sql
-- Ad extensions
CREATE TABLE ad_extensions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID NOT NULL REFERENCES google_ads_accounts(id) ON DELETE CASCADE,
    extension_type TEXT CHECK (extension_type IN (
        'sitelink',
        'callout',
        'structured_snippet',
        'call',
        'location',
        'price',
        'promotion',
        'app'
    )) NOT NULL,
    extension_data JSONB NOT NULL, -- Type-specific data
    campaigns_applied TEXT[],
    ad_groups_applied TEXT[],
    performance_metrics JSONB DEFAULT '{
        "clicks": 0,
        "impressions": 0,
        "ctr": 0
    }'::jsonb,
    is_active BOOLEAN DEFAULT TRUE,
    google_ads_id TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Extension performance tracking
CREATE TABLE extension_performance (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    extension_id UUID NOT NULL REFERENCES ad_extensions(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    clicks INTEGER DEFAULT 0,
    impressions INTEGER DEFAULT 0,
    conversions DECIMAL(10,2) DEFAULT 0,
    cost DECIMAL(10,2) DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(extension_id, date)
);

-- Extension recommendations
CREATE TABLE extension_recommendations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID NOT NULL REFERENCES google_ads_accounts(id) ON DELETE CASCADE,
    campaign_id TEXT,
    extension_type TEXT NOT NULL,
    recommendation_data JSONB NOT NULL,
    reason TEXT,
    expected_ctr_lift DECIMAL(5,2),
    priority INTEGER,
    status TEXT CHECK (status IN ('pending', 'applied', 'dismissed')) DEFAULT 'pending',
    created_at TIMESTAMPTZ DEFAULT NOW()
);
```

---

## 10. Landing Page Analysis

```sql
-- Landing pages
CREATE TABLE landing_pages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID NOT NULL REFERENCES google_ads_accounts(id) ON DELETE CASCADE,
    url TEXT NOT NULL,
    domain TEXT NOT NULL,
    page_title TEXT,
    meta_description TEXT,
    h1_text TEXT,
    content_summary TEXT,
    keywords_found TEXT[],
    cta_elements JSONB DEFAULT '[]'::jsonb,
    trust_signals JSONB DEFAULT '[]'::jsonb,
    last_crawled_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(account_id, url)
);

-- Landing page performance
CREATE TABLE landing_page_performance (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    landing_page_id UUID NOT NULL REFERENCES landing_pages(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    load_time_ms INTEGER,
    mobile_score INTEGER,
    desktop_score INTEGER,
    bounce_rate DECIMAL(5,2),
    avg_time_on_page INTEGER, -- seconds
    conversion_rate DECIMAL(5,2),
    core_web_vitals JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(landing_page_id, date)
);

-- Message match analysis
CREATE TABLE message_match_analysis (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    ad_copy_id UUID NOT NULL REFERENCES ad_copies(id) ON DELETE CASCADE,
    landing_page_id UUID NOT NULL REFERENCES landing_pages(id) ON DELETE CASCADE,
    headline_match_score DECIMAL(3,2),
    description_match_score DECIMAL(3,2),
    cta_match_score DECIMAL(3,2),
    overall_match_score DECIMAL(3,2),
    issues_found JSONB DEFAULT '[]'::jsonb,
    recommendations JSONB DEFAULT '[]'::jsonb,
    analyzed_at TIMESTAMPTZ DEFAULT NOW()
);
```

---

## 11. Automation Workflows

```sql
-- Automation workflows
CREATE TABLE automation_workflows (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID NOT NULL REFERENCES google_ads_accounts(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    trigger_type TEXT CHECK (trigger_type IN (
        'schedule',
        'performance_threshold',
        'budget_threshold',
        'manual',
        'event'
    )) NOT NULL,
    trigger_config JSONB NOT NULL,
    conditions JSONB NOT NULL, -- Array of conditions
    actions JSONB NOT NULL, -- Array of actions to take
    is_active BOOLEAN DEFAULT TRUE,
    last_run_at TIMESTAMPTZ,
    next_run_at TIMESTAMPTZ,
    run_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES users(id)
);

-- Workflow execution history
CREATE TABLE workflow_executions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    workflow_id UUID NOT NULL REFERENCES automation_workflows(id) ON DELETE CASCADE,
    started_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    status TEXT CHECK (status IN ('running', 'completed', 'failed', 'cancelled')) DEFAULT 'running',
    items_processed INTEGER DEFAULT 0,
    actions_taken JSONB DEFAULT '[]'::jsonb,
    error_message TEXT,
    execution_log JSONB DEFAULT '[]'::jsonb
);

-- Automation rules
CREATE TABLE automation_rules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID NOT NULL REFERENCES google_ads_accounts(id) ON DELETE CASCADE,
    rule_name TEXT NOT NULL,
    entity_type TEXT CHECK (entity_type IN ('keyword', 'ad', 'campaign', 'ad_group')) NOT NULL,
    condition_logic TEXT CHECK (condition_logic IN ('all', 'any')) DEFAULT 'all',
    conditions JSONB NOT NULL,
    action_type TEXT NOT NULL,
    action_config JSONB NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW()
);
```

---

## 12. Scripts Library

```sql
-- Google Ads scripts
CREATE TABLE scripts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    category TEXT CHECK (category IN (
        'reporting',
        'optimization',
        'monitoring',
        'analysis',
        'custom'
    )) NOT NULL,
    script_content TEXT NOT NULL,
    parameters JSONB DEFAULT '[]'::jsonb, -- Parameters that can be customized
    is_template BOOLEAN DEFAULT TRUE,
    is_public BOOLEAN DEFAULT FALSE,
    usage_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES users(id),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Script deployments
CREATE TABLE script_deployments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    script_id UUID NOT NULL REFERENCES scripts(id) ON DELETE CASCADE,
    account_id UUID NOT NULL REFERENCES google_ads_accounts(id) ON DELETE CASCADE,
    customized_content TEXT NOT NULL,
    parameters_used JSONB,
    schedule TEXT, -- Cron expression
    is_active BOOLEAN DEFAULT TRUE,
    last_run_at TIMESTAMPTZ,
    next_run_at TIMESTAMPTZ,
    deployed_at TIMESTAMPTZ DEFAULT NOW(),
    deployed_by UUID REFERENCES users(id)
);

-- Script execution logs
CREATE TABLE script_executions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    deployment_id UUID NOT NULL REFERENCES script_deployments(id) ON DELETE CASCADE,
    started_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    status TEXT CHECK (status IN ('running', 'completed', 'failed')) DEFAULT 'running',
    output JSONB,
    error_message TEXT,
    changes_made JSONB DEFAULT '[]'::jsonb
);
```

---

## 13. AI Insights & Natural Language

```sql
-- AI conversations
CREATE TABLE ai_conversations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    account_id UUID REFERENCES google_ads_accounts(id) ON DELETE CASCADE,
    started_at TIMESTAMPTZ DEFAULT NOW(),
    ended_at TIMESTAMPTZ,
    message_count INTEGER DEFAULT 0,
    context JSONB DEFAULT '{}'::jsonb -- Conversation context
);

-- AI messages
CREATE TABLE ai_messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    conversation_id UUID NOT NULL REFERENCES ai_conversations(id) ON DELETE CASCADE,
    role TEXT CHECK (role IN ('user', 'assistant', 'system')) NOT NULL,
    content TEXT NOT NULL,
    intent_classification TEXT,
    entities_extracted JSONB DEFAULT '[]'::jsonb,
    data_queries_executed JSONB DEFAULT '[]'::jsonb,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- AI insights
CREATE TABLE ai_insights (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID NOT NULL REFERENCES google_ads_accounts(id) ON DELETE CASCADE,
    insight_type TEXT CHECK (insight_type IN (
        'anomaly',
        'opportunity',
        'trend',
        'prediction',
        'recommendation'
    )) NOT NULL,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    data_points JSONB NOT NULL,
    confidence_score DECIMAL(3,2),
    impact_estimate JSONB,
    suggested_actions JSONB DEFAULT '[]'::jsonb,
    priority INTEGER CHECK (priority >= 1 AND priority <= 5),
    is_read BOOLEAN DEFAULT FALSE,
    is_acted_upon BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ,
    INDEX idx_insights_unread (account_id, is_read, created_at DESC)
);

-- Saved prompts/queries
CREATE TABLE saved_queries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    query_text TEXT NOT NULL,
    query_type TEXT,
    parameters JSONB DEFAULT '{}'::jsonb,
    is_favorite BOOLEAN DEFAULT FALSE,
    use_count INTEGER DEFAULT 0,
    last_used_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);
```

---

## 14. Performance & Analytics

```sql
-- Platform usage analytics (Enhanced for UX tracking)
CREATE TABLE platform_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    action_type TEXT NOT NULL,
    action_details JSONB DEFAULT '{}'::jsonb,
    feature_used TEXT,
    duration_ms INTEGER,
    interaction_type TEXT, -- 'click', 'hover', 'scroll', 'form_submit', 'animation_complete'
    ui_element TEXT, -- Which UI element was interacted with
    created_at TIMESTAMPTZ DEFAULT NOW(),
    INDEX idx_analytics_user_date (user_id, created_at DESC),
    INDEX idx_analytics_interaction (interaction_type, created_at DESC)
);

-- Feature adoption tracking
CREATE TABLE feature_adoption (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID NOT NULL REFERENCES google_ads_accounts(id) ON DELETE CASCADE,
    feature_name TEXT NOT NULL,
    first_used_at TIMESTAMPTZ DEFAULT NOW(),
    last_used_at TIMESTAMPTZ DEFAULT NOW(),
    use_count INTEGER DEFAULT 1,
    adoption_score DECIMAL(3,2),
    UNIQUE(account_id, feature_name)
);

-- Performance benchmarks
CREATE TABLE performance_benchmarks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID NOT NULL REFERENCES google_ads_accounts(id) ON DELETE CASCADE,
    metric_name TEXT NOT NULL,
    metric_value DECIMAL(10,2) NOT NULL,
    benchmark_period TEXT NOT NULL, -- 'daily', 'weekly', 'monthly'
    recorded_date DATE NOT NULL,
    industry_average DECIMAL(10,2),
    percentile_rank INTEGER,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    INDEX idx_benchmarks_date (account_id, recorded_date DESC)
);
```

---

## 15. Audit & Compliance

```sql
-- Audit logs
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    action TEXT NOT NULL,
    entity_type TEXT,
    entity_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    INDEX idx_audit_user_date (user_id, created_at DESC),
    INDEX idx_audit_entity (entity_type, entity_id, created_at DESC)
);

-- Change approvals
CREATE TABLE change_approvals (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID NOT NULL REFERENCES google_ads_accounts(id) ON DELETE CASCADE,
    change_type TEXT NOT NULL,
    change_details JSONB NOT NULL,
    requested_by UUID NOT NULL REFERENCES users(id),
    requested_at TIMESTAMPTZ DEFAULT NOW(),
    approval_required_from UUID REFERENCES users(id),
    approved_by UUID REFERENCES users(id),
    approved_at TIMESTAMPTZ,
    status TEXT CHECK (status IN ('pending', 'approved', 'rejected', 'expired')) DEFAULT 'pending',
    comments TEXT,
    expires_at TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '48 hours')
);
```

---

## 16. Notifications & Alerts

```sql
-- Notification templates
CREATE TABLE notification_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL UNIQUE,
    type TEXT CHECK (type IN ('email', 'in_app', 'push', 'sms')) NOT NULL,
    subject_template TEXT,
    body_template TEXT NOT NULL,
    variables JSONB DEFAULT '[]'::jsonb,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- User notifications
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    template_id UUID REFERENCES notification_templates(id),
    type TEXT NOT NULL,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    data JSONB DEFAULT '{}'::jsonb,
    is_read BOOLEAN DEFAULT FALSE,
    is_archived BOOLEAN DEFAULT FALSE,
    action_url TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    read_at TIMESTAMPTZ,
    INDEX idx_notifications_unread (user_id, is_read, created_at DESC)
);

-- Alert rules
CREATE TABLE alert_rules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID NOT NULL REFERENCES google_ads_accounts(id) ON DELETE CASCADE,
    alert_name TEXT NOT NULL,
    metric TEXT NOT NULL,
    condition TEXT NOT NULL, -- '>', '<', '=', 'change_percent'
    threshold DECIMAL(10,2) NOT NULL,
    time_window TEXT DEFAULT '1 hour',
    notification_channels JSONB DEFAULT '["in_app"]'::jsonb,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES users(id)
);
```

---

## 17. Reporting & Exports

```sql
-- Saved reports
CREATE TABLE saved_reports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID NOT NULL REFERENCES google_ads_accounts(id) ON DELETE CASCADE,
    report_name TEXT NOT NULL,
    report_type TEXT CHECK (report_type IN (
        'performance',
        'search_terms',
        'quality_score',
        'competitive',
        'custom'
    )) NOT NULL,
    configuration JSONB NOT NULL, -- Metrics, dimensions, filters, etc.
    schedule TEXT, -- Cron expression for automated reports
    recipients TEXT[],
    format TEXT CHECK (format IN ('pdf', 'excel', 'csv', 'google_sheets')) DEFAULT 'pdf',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES users(id),
    last_generated_at TIMESTAMPTZ
);

-- Report executions
CREATE TABLE report_executions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    report_id UUID NOT NULL REFERENCES saved_reports(id) ON DELETE CASCADE,
    generated_at TIMESTAMPTZ DEFAULT NOW(),
    file_url TEXT,
    file_size INTEGER,
    row_count INTEGER,
    status TEXT CHECK (status IN ('generating', 'completed', 'failed')) DEFAULT 'generating',
    error_message TEXT
);
```

---

## 18. UI State & Form Management (New for Enhanced UX)

```sql
-- UI state persistence for premium UX
CREATE TABLE ui_states (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    page_identifier TEXT NOT NULL, -- e.g., 'search_query_mining', 'ad_copy_lab'
    state_data JSONB NOT NULL, -- Collapsed panels, active tabs, sort preferences, etc.
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, page_identifier)
);

-- Form drafts for complex forms (React Hook Form integration)
CREATE TABLE form_drafts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    form_type TEXT NOT NULL, -- 'ad_copy', 'campaign_settings', 'automation_workflow'
    form_data JSONB NOT NULL, -- Partial form data
    related_entity_id UUID, -- Optional link to campaign, ad_group, etc.
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '7 days'),
    INDEX idx_form_drafts_user (user_id, form_type, updated_at DESC)
);

-- User interaction events for UX analytics
CREATE TABLE interaction_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_id UUID REFERENCES user_sessions(id),
    event_type TEXT NOT NULL, -- 'animation_played', 'tooltip_shown', 'feature_discovered'
    event_target TEXT NOT NULL, -- Which component/feature
    event_data JSONB DEFAULT '{}'::jsonb, -- Additional context
    created_at TIMESTAMPTZ DEFAULT NOW(),
    INDEX idx_interactions_user_session (user_id, session_id, created_at)
);

-- A/B test variations for UI features
CREATE TABLE ui_experiments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    experiment_name TEXT NOT NULL,
    feature TEXT NOT NULL,
    variant_a JSONB NOT NULL, -- Control variant configuration
    variant_b JSONB NOT NULL, -- Test variant configuration
    allocation_percentage INTEGER DEFAULT 50, -- % of users in variant B
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    ends_at TIMESTAMPTZ
);

-- User experiment assignments
CREATE TABLE user_experiments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    experiment_id UUID NOT NULL REFERENCES ui_experiments(id) ON DELETE CASCADE,
    variant TEXT NOT NULL CHECK (variant IN ('A', 'B')),
    assigned_at TIMESTAMPTZ DEFAULT NOW(),
    converted BOOLEAN DEFAULT FALSE,
    conversion_data JSONB,
    UNIQUE(user_id, experiment_id)
);
```

---

## 19. Vector Storage for AI

```sql
-- Embeddings for semantic search
CREATE TABLE embeddings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    content_type TEXT NOT NULL, -- 'search_term', 'ad_copy', 'landing_page'
    content_id UUID NOT NULL,
    content_text TEXT NOT NULL,
    embedding vector(1536), -- OpenAI embedding dimension
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    INDEX idx_embeddings_vector USING ivfflat (embedding vector_cosine_ops)
);

-- Knowledge base for AI
CREATE TABLE knowledge_base (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    category TEXT NOT NULL,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    tags TEXT[],
    embedding vector(1536),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

---

## Database Indexes & Performance

```sql
-- Additional performance indexes
CREATE INDEX idx_search_terms_text ON search_terms USING gin(to_tsvector('english', search_term));
CREATE INDEX idx_ad_copies_headlines ON ad_copies USING gin(headlines);
CREATE INDEX idx_landing_pages_domain ON landing_pages(domain);
CREATE INDEX idx_quality_scores_low ON quality_scores(account_id, quality_score) WHERE quality_score < 7;
CREATE INDEX idx_recommendations_pending ON search_term_recommendations(account_id, status) WHERE status = 'pending';
CREATE INDEX idx_insights_recent ON ai_insights(account_id, created_at DESC) WHERE is_read = false;

-- Materialized views for performance
CREATE MATERIALIZED VIEW account_performance_summary AS
SELECT 
    ga.id as account_id,
    ga.name as account_name,
    COUNT(DISTINCT st.campaign_id) as active_campaigns,
    SUM(st.cost) as total_cost,
    SUM(st.conversions) as total_conversions,
    AVG(qs.quality_score) as avg_quality_score,
    COUNT(CASE WHEN str.status = 'pending' THEN 1 END) as pending_recommendations
FROM google_ads_accounts ga
LEFT JOIN search_terms st ON ga.id = st.account_id
LEFT JOIN quality_scores qs ON ga.id = qs.account_id
LEFT JOIN search_term_recommendations str ON ga.id = str.account_id
GROUP BY ga.id, ga.name;

-- Refresh materialized view daily
CREATE OR REPLACE FUNCTION refresh_account_summary()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW account_performance_summary;
END;
$$ LANGUAGE plpgsql;

-- Schedule refresh using pg_cron
SELECT cron.schedule('refresh-account-summary', '0 2 * * *', 'SELECT refresh_account_summary();');
```

---

## Row Level Security (RLS) Policies

```sql
-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE google_ads_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE search_terms ENABLE ROW LEVEL SECURITY;
ALTER TABLE ui_states ENABLE ROW LEVEL SECURITY;
ALTER TABLE form_drafts ENABLE ROW LEVEL SECURITY;
ALTER TABLE interaction_events ENABLE ROW LEVEL SECURITY;
-- ... (enable for all tables)

-- Example RLS policies
CREATE POLICY "Users can view their own data" ON users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can view accounts they have access to" ON google_ads_accounts
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM account_permissions
            WHERE account_permissions.account_id = google_ads_accounts.id
            AND account_permissions.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can modify accounts they admin" ON google_ads_accounts
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM account_permissions
            WHERE account_permissions.account_id = google_ads_accounts.id
            AND account_permissions.user_id = auth.uid()
            AND account_permissions.permission_level = 'admin'
        )
    );

-- New policies for UI state tables
CREATE POLICY "Users can manage their own UI states" ON ui_states
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own form drafts" ON form_drafts
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own interactions" ON interaction_events
    FOR SELECT USING (auth.uid() = user_id);
```

---

## Database Functions & Triggers

```sql
-- Auto-update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply to relevant tables
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Calculate wasted spend
CREATE OR REPLACE FUNCTION calculate_wasted_spend(account_uuid UUID, days INTEGER DEFAULT 30)
RETURNS TABLE (
    total_wasted DECIMAL,
    wasted_clicks INTEGER,
    top_wasteful_terms JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        SUM(cost) as total_wasted,
        SUM(clicks)::INTEGER as wasted_clicks,
        jsonb_agg(
            jsonb_build_object(
                'term', search_term,
                'cost', cost,
                'clicks', clicks
            ) ORDER BY cost DESC
        ) FILTER (WHERE row_num <= 10) as top_wasteful_terms
    FROM (
        SELECT 
            search_term,
            SUM(cost) as cost,
            SUM(clicks) as clicks,
            ROW_NUMBER() OVER (ORDER BY SUM(cost) DESC) as row_num
        FROM search_terms
        WHERE account_id = account_uuid
        AND date >= CURRENT_DATE - days
        AND conversions = 0
        AND clicks > 5
        GROUP BY search_term
    ) wasteful;
END;
$$ LANGUAGE plpgsql;

-- Smart recommendation scorer
CREATE OR REPLACE FUNCTION score_recommendation_impact(
    clicks INTEGER,
    cost DECIMAL,
    conversions DECIMAL,
    recommendation_type TEXT
) RETURNS DECIMAL AS $
DECLARE
    impact_score DECIMAL;
BEGIN
    impact_score := 0;
    
    IF recommendation_type = 'add_as_negative' THEN
        impact_score := cost * 0.8; -- 80% of cost is potential savings
    ELSIF recommendation_type = 'add_as_keyword' THEN
        impact_score := conversions * 50; -- Assume $50 value per conversion
    END IF;
    
    RETURN impact_score;
END;
$ LANGUAGE plpgsql;

-- Form completion analytics
CREATE OR REPLACE FUNCTION get_form_completion_stats(
    form_type_param TEXT,
    days_back INTEGER DEFAULT 30
) RETURNS TABLE (
    total_started INTEGER,
    total_completed INTEGER,
    completion_rate DECIMAL,
    avg_time_to_complete INTERVAL,
    abandonment_points JSONB
) AS $
BEGIN
    RETURN QUERY
    WITH form_stats AS (
        SELECT 
            COUNT(*) FILTER (WHERE created_at >= CURRENT_TIMESTAMP - (days_back || ' days')::INTERVAL) as started,
            COUNT(*) FILTER (WHERE form_data->>'submitted' = 'true') as completed,
            AVG(EXTRACT(EPOCH FROM (updated_at - created_at))) FILTER (WHERE form_data->>'submitted' = 'true') as avg_seconds
        FROM form_drafts
        WHERE form_type = form_type_param
    ),
    abandonment_analysis AS (
        SELECT 
            jsonb_object_agg(
                form_data->>'lastField',
                count(*)
            ) as abandonment_by_field
        FROM form_drafts
        WHERE form_type = form_type_param
        AND form_data->>'submitted' IS NULL
        AND updated_at < CURRENT_TIMESTAMP - INTERVAL '1 hour'
        GROUP BY form_data->>'lastField'
    )
    SELECT 
        started::INTEGER,
        completed::INTEGER,
        CASE WHEN started > 0 THEN (completed::DECIMAL / started) ELSE 0 END,
        make_interval(secs => avg_seconds),
        abandonment_by_field
    FROM form_stats, abandonment_analysis;
END;
$ LANGUAGE plpgsql;
```

---

## Data Retention & Archival

```sql
-- Archive old search terms data
CREATE TABLE search_terms_archive (
    LIKE search_terms INCLUDING ALL
);

-- Move old data to archive
CREATE OR REPLACE FUNCTION archive_old_search_terms()
RETURNS void AS $$
BEGIN
    INSERT INTO search_terms_archive
    SELECT * FROM search_terms
    WHERE date < CURRENT_DATE - INTERVAL '90 days';
    
    DELETE FROM search_terms
    WHERE date < CURRENT_DATE - INTERVAL '90 days';
END;
$$ LANGUAGE plpgsql;

-- Schedule archival
SELECT cron.schedule('archive-search-terms', '0 3 * * 0', 'SELECT archive_old_search_terms();');
```

---

## Sample Data & Testing

```sql
-- Sample data for testing (development only)
INSERT INTO psychological_triggers (trigger_type, trigger_text, effectiveness_score) VALUES
('urgency', 'Limited Time Offer', 0.82),
('urgency', 'Sale Ends Today', 0.78),
('scarcity', 'Only 3 Left in Stock', 0.85),
('social_proof', 'Join 10,000+ Happy Customers', 0.75),
('authority', 'Industry Leader Since 2010', 0.68);

-- Sample UI experiments
INSERT INTO ui_experiments (experiment_name, feature, variant_a, variant_b, allocation_percentage) VALUES
('recommendation_cards_animation', 'search_query_recommendations', 
 '{"animation": "fade", "duration": 200}'::jsonb,
 '{"animation": "slide", "duration": 300, "stagger": true}'::jsonb,
 50),
('form_validation_timing', 'negative_keyword_form',
 '{"validation": "onSubmit"}'::jsonb,
 '{"validation": "onChange", "debounce": 500}'::jsonb,
 50),
('dashboard_layout', 'main_dashboard',
 '{"layout": "grid", "density": "normal"}'::jsonb,
 '{"layout": "masonry", "density": "compact"}'::jsonb,
 30);

-- Test data generator function
CREATE OR REPLACE FUNCTION generate_test_search_terms(account_uuid UUID, num_terms INTEGER)
RETURNS void AS $$
DECLARE
    i INTEGER;
    sample_terms TEXT[] := ARRAY[
        'buy shoes online',
        'cheap running shoes',
        'nike shoes sale',
        'best walking shoes',
        'comfortable shoes for work'
    ];
BEGIN
    FOR i IN 1..num_terms LOOP
        INSERT INTO search_terms (
            account_id,
            campaign_id,
            ad_group_id,
            search_term,
            clicks,
            impressions,
            cost,
            conversions,
            date
        ) VALUES (
            account_uuid,
            'campaign_' || (i % 5),
            'ad_group_' || (i % 10),
            sample_terms[1 + (i % array_length(sample_terms, 1))],
            floor(random() * 100),
            floor(random() * 1000),
            random() * 100,
            CASE WHEN random() > 0.7 THEN floor(random() * 5) ELSE 0 END,
            CURRENT_DATE - (i % 30)
        );
    END LOOP;
END;
$$ LANGUAGE plpgsql;
```

---

## Database Maintenance & Monitoring

```sql
-- Table sizes monitoring
CREATE VIEW table_sizes AS
SELECT
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) AS size,
    pg_total_relation_size(schemaname||'.'||tablename) AS size_bytes
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY size_bytes DESC;

-- Index usage statistics
CREATE VIEW index_usage AS
SELECT
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch,
    pg_size_pretty(pg_relation_size(indexrelid)) AS index_size
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;

-- Slow query monitor
CREATE TABLE slow_queries (
    id SERIAL PRIMARY KEY,
    query TEXT,
    duration_ms INTEGER,
    called_by TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);
```

---

## Migration & Version Control

```sql
-- Schema versions table
CREATE TABLE schema_versions (
    version INTEGER PRIMARY KEY,
    description TEXT,
    applied_at TIMESTAMPTZ DEFAULT NOW(),
    applied_by TEXT
);

-- Initial version
INSERT INTO schema_versions (version, description, applied_by) 
VALUES (1, 'Initial schema creation', 'system');
```

This comprehensive database schema provides:

1. **Complete feature coverage** for all PRD features
2. **Performance optimization** with strategic indexes
3. **Security** with RLS policies
4. **Scalability** with partitioning strategies
5. **Audit trails** for compliance
6. **AI/ML support** with vector storage
7. **Automation** with triggers and functions
8. **Analytics** with materialized views
9. **Enhanced UX support** with UI state persistence and form drafts
10. **A/B testing capabilities** for continuous UX improvement

The schema is designed to handle millions of search terms, thousands of accounts, and provide sub-second query performance for the dashboard while supporting the premium user experience features enabled by the enhanced tech stack.

## Schema Version 2.1 Enhancements

**New tables added for enhanced tech stack support:**
- `ui_states`: Persist UI preferences across sessions
- `form_drafts`: Save complex form progress (React Hook Form)
- `interaction_events`: Track micro-interactions for UX analytics
- `ui_experiments` & `user_experiments`: A/B test UI variations

**Enhanced tables:**
- `users`: Added `ui_preferences` and `onboarding_steps` for better UX personalization
- `platform_analytics`: Added interaction tracking fields

These enhancements ensure the database fully supports the premium user experience delivered by React Hook Form, Framer Motion, and other frontend improvements.