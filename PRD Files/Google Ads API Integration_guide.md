# Integrating Applications with the Google Ads API: A Comprehensive Technical Guide

This report provides a detailed examination of the Google Ads API, OAuth 2.0 authentication, and other essential components required for developing and operating applications that interact with the Google Ads platform. All information is sourced from official Google Ads API documentation to ensure accuracy and relevance.

## 1\. Understanding the Google Ads API for Application Development

The Google Ads API is a sophisticated toolset enabling developers to build applications that programmatically interface with Google Ads accounts. A clear understanding of its purpose, capabilities, and ideal use cases is fundamental before embarking on any integration project.

### Overview of the Google Ads API: Purpose, Key Capabilities, and Benefits

The Google Ads API is officially described as an advanced feature primarily designed for developers. Its core purpose is to facilitate the creation of applications that can directly interact with and manage Google Ads accounts and campaigns, particularly beneficial for advertisers handling large or complex account structures.1 The API empowers developers to extend the functionality of Google Ads, automate processes, and integrate ad management into broader business workflows.

The **key capabilities** of the Google Ads API can be summarized into three main pillars:

1.  **Scale:** The API allows for the automation of account management tasks, enabling improvements to be made across numerous Google Ads accounts simultaneously. This includes capabilities like building custom reporting solutions and dynamically altering ad copy in real-time based on evolving business data or inventory levels.1 For applications designed to manage many accounts or large-scale campaigns, this scalability is a significant advantage.
2.  **Integrate:** Developers can leverage the API to merge Google Ads data with other critical business systems, such as inventory management platforms or CRM software. This allows for more holistic data analysis and can trigger automated campaign adjustments based on external data feeds. Furthermore, the API supports the development of customized interfaces on proprietary platforms, which can then be used by an application's customers to create and manage their own Google Ads campaigns.1
3.  **Develop:** The API provides the foundation for creating a wide array of additional tools and applications tailored to specific advertising needs. Engineering teams can build applications that interact directly with the Google Ads server, enabling highly customized solutions.1

Functionally, the API permits management from the highest customer account level down to the granular details of individual keywords.2 However, it is crucial to recognize that utilizing the Google Ads API presupposes a certain level of technical expertise. The documentation explicitly states that coding experience is necessary, as the API is intended for developers proficient in programming languages such as Java, PHP, Python, Ruby, and Perl.1 This is a foundational consideration for any app concept; if the development team lacks these skills, the API might not be the appropriate choice.

The API is consistently positioned as an "advanced feature" 1 intended for "large or complex Google Ads accounts".2 This implies that for simpler advertising needs or smaller accounts, the full power (and potential complexity) of the API might be unnecessary. Alternative tools, as discussed later, could offer a more streamlined solution in such cases. Moreover, the decision to use the Google Ads API carries implications for infrastructure. The official documentation recommends the API for developers who are "comfortable writing code, managing software infrastructure including servers and database".2 This points to an operational overhead that must be factored into the application's design, cost, and long-term maintenance strategy.

### Common Use Cases for API Integration

The versatility of the Google Ads API lends itself to a variety of applications. Common use cases include:

*   **Automated account management:** Programmatically adjusting bids, budgets, and campaign statuses based on performance data or predefined rules.2
*   **Custom reporting:** Generating tailored reports that go beyond the standard Google Ads interface, often integrating data from multiple sources.2
*   **Ad management based on inventory:** Dynamically creating, pausing, or modifying ads based on product availability or stock levels, particularly relevant for e-commerce businesses.2
*   **Managing smart bidding strategies:** Implementing and fine-tuning sophisticated bidding strategies programmatically.2
*   **Content generation:** Automatically generating keywords, ad text, and even suggesting landing pages based on specific inputs or templates.1

These examples illustrate the potential for an application to significantly enhance advertising efficiency and effectiveness through API integration.

### Choosing Between Google Ads API, Google Ads Scripts, and Other Tools

Google offers several tools for interacting with Google Ads, and selecting the most appropriate one depends on the specific needs, technical capabilities, and infrastructure preferences of the developer or organization.2

*   **Google Ads API:** This is the recommended choice for developers aiming to build their own distinct software products or integrate Google Ads functionality deeply into existing applications. It is suited for those who are proficient in coding and are prepared to manage the necessary software infrastructure, including servers and databases.2
*   **Google Ads Scripts:** Scripts offer a lighter-weight automation solution. They are suitable for developers who are comfortable with JavaScript but prefer not to manage their own server infrastructure. Scripts can also be an entry point for non-developers who are willing to learn some programming to automate specific Google Ads tasks.2
*   **BigQuery Data Transfer Service:** For data analysts or roles primarily focused on downloading and analyzing Google Ads report data, the BigQuery Data Transfer Service is often the most efficient solution.2
*   **Automated Rules, Bulk Uploads, Google Ads Editor:** For users who need to manage Google Ads in bulk but do not wish to write any code, Google provides several UI-based tools like automated rules, bulk uploads via spreadsheets, and the Google Ads Editor desktop application.2

The decision framework provided by Google 2 is clear: the API is for robust, custom application development where infrastructure management is feasible. If these conditions are not met, other tools might be more suitable.

## 2\. Essential Prerequisites for Google Ads API Integration

Before any code can be written or API calls made, a series of prerequisites must be fulfilled. These steps are foundational and, in many cases, sequential, forming a critical path to successful API integration.

A clear understanding of these prerequisites is essential, as they are not merely a checklist but represent a structured setup process. For instance, a Google Ads Manager Account is the gateway to applying for a Developer Token. Subsequently, a Google API Console project is necessary to obtain OAuth 2.0 credentials, which are then used in conjunction with the Developer Token to authenticate API requests. This ordered dependency means that these steps cannot be approached randomly; a methodical, step-by-step setup is crucial.

**Table 2.1: Prerequisites Checklist for Google Ads API Integration**

| Item | Description | Key Requirements/Considerations | Official Documentation Link(s) |
| --- | --- | --- | --- |
| Google Ads Manager Account (MCC) | A central account for managing multiple Google Ads client accounts. Mandatory for applying for the API. | Must be created with an email not previously tied to a Google Ads account. Recommended to be at the root of the account hierarchy. 2 | Create a manager account 5 |
| Developer Token | A unique 22-character alphanumeric string that allows an application to connect to the Google Ads API. | Applied for via the API Center in an MCC. Requires a functional company website and monitored API contact email. Subject to Google's review and access level assignment (Test, Basic, Standard). One token per company is usual. Can be disabled if unused for 90 days. 1 | Obtain a developer token 5 |
| Google API Console Project | A project within the Google Cloud Platform used to manage APIs, generate credentials (OAuth 2.0), and control billing. | The Google Ads API must be enabled within this project. Used to create Client ID and Client Secret for OAuth 2.0. No charge for API usage, but project quotas exist. 2 | (https://developers.google.com/google-ads/api/docs/get-started/oauth-cloud-project) 6 |
| Target Google Ads Client Account(s) | The specific Google Ads accounts the application will manage or report on. | The 10-digit customer ID (without hyphens) is required for API calls. Necessary permissions to operate on these accounts must be in place. 2 | N/A (Account-specific) |

### Google Ads Manager Account (MCC)

A Google Ads Manager Account (often referred to as an MCC account) is the cornerstone for API access. It is a mandatory prerequisite for applying for the Google Ads API.2 When setting up an MCC for API purposes, it is advisable to place it at the root of the Google Ads account hierarchy. This structure can simplify the developer token review process and streamline account management later on.5 A critical detail is that the email address used to create this manager account must not have been previously associated with any other Google Ads account.5

Beyond being a prerequisite, the Manager Account itself offers powerful functionalities that could be integral to an application's design, especially if the app targets agencies or advertisers managing multiple client accounts. MCCs provide a single login to access and manage multiple Google Ads accounts, offer consolidated billing options, and facilitate performance tracking and reporting across all linked accounts.8 Thus, the MCC plays a dual role: an entry point for API access and a potential tool for enhanced account management features within the application.

### Obtaining Your Developer Token

The Developer Token is a unique 22-character alphanumeric string that acts as the primary identifier for an application, allowing it to connect to the Google Ads API.1 This token is not merely a technical key; it represents a level of trust and vetting by Google. It is obtained by submitting an application through the API Center, accessible from within a Google Ads Manager Account.1

The application process itself has specific requirements. Applicants must provide details about their intended API usage, a functional company website, and a regularly monitored API contact email address. Google's API compliance team may use this email to request clarifications during the review process, and a non-functional website can be grounds for application rejection.5 This underscores that the Developer Token is not automatically granted and involves a due diligence step by Google.

Upon approval, Developer Tokens are assigned an access level (e.g., Test Access, Basic Access, Standard Access). This access level is crucial as it dictates the number of API calls the application can make per day and the environments (test or production accounts) it can target.2 Initially, tokens are typically granted "Test Access," allowing development and testing against test accounts. To interact with production accounts, Basic or Standard Access must be requested and approved.

Google generally issues one Developer Token per corporate entity, encouraging reuse of existing tokens where possible.5 Furthermore, a Developer Token that remains unused for 90 consecutive days may be disabled by Google, highlighting the need for active usage or proper management if development is paused.10

### Google API Console Project Setup

A Google API Console project is another critical prerequisite. This project serves two primary functions for Google Ads API integration:

1.  It is used to generate the OAuth 2.0 credentials (specifically, a client ID and client secret) that the application will use for authentication and authorization.2
2.  It is the environment where the Google Ads API service is explicitly enabled, allowing the project to accept API calls from the application.2

The setup process involves either creating a new project or selecting an existing one within the Google API Console. Once the project is established, the "Google Ads API" must be located in the API Library and enabled for that project.6 While the use of the Google Ads API itself does not incur charges, Google Cloud Platform does have quotas on the total number of projects a single account can create.6

### Target Google Ads Client Account(s)

Finally, the application will need to interact with one or more specific Google Ads client accounts. These are the actual accounts whose campaigns will be managed or whose data will be reported on.2 For each API call directed at a client account, the application must provide the 10-digit customer ID of that account. This ID is typically displayed in the Google Ads web interface (e.g., 123-456-7890) and must be passed to the API without hyphens (e.g., **********).2 Crucially, the authenticated user or service account making the API call must have the necessary permissions to perform the requested operations on these target client accounts.2

## 3\. Authenticating Your Application: A Deep Dive into OAuth 2.0

Securely authenticating an application to access Google Ads data is paramount. The Google Ads API, like other Google APIs, utilizes the OAuth 2.0 protocol for this purpose.11 OAuth 2.0 is an industry-standard authorization framework that enables third-party applications to access user resources on a web service without exposing the user's credentials (username and password) to the application.

### Overview of OAuth 2.0 with Google APIs

OAuth 2.0 allows an application to request limited access to a user's Google Ads account. Instead of handling sensitive login credentials directly, the application obtains an access token from the Google Authorization Server. This token represents the grant of specific permissions, known as scopes, which define what operations the application can perform on the user's behalf.11 This mechanism enhances security and provides users with control over what data their applications can access.

### Obtaining OAuth 2.0 Credentials (Client ID, Client Secret)

To use OAuth 2.0, an application must first be registered with Google via the Google API Console project. This registration process yields a set of credentials unique to the application:

*   **Client ID:** A public identifier for the application.
*   **Client Secret:** A confidential string known only to the application and Google, used to authenticate the application's identity. (Note: For certain application types, like client-side JavaScript applications or some installed app flows, a client secret may not be required or treated as strictly confidential 11).

These credentials are obtained from the Credentials page within the Google API Console project associated with the application.6 The steps generally involve:

1.  Navigating to the "Credentials" page in the API Console.
2.  Initiating the creation of new credentials and selecting "OAuth client ID."
3.  **Configuring the OAuth consent screen:** This is a critical step. The consent screen is what users see when the application requests permission to access their Google Ads data. It displays information about the application, the developer, and the specific data (scopes) being requested.6 A well-configured consent screen is vital for user trust. Depending on the scopes requested and the nature of the application (internal vs. external users), Google may require an app verification process, which can take several business days to complete. This potential review period should be factored into project timelines.7
4.  Selecting the appropriate **application type** (e.g., "Web application," "Desktop app," "TVs and Limited Input devices," or setting up a "Service account"). The choice of application type dictates the OAuth flow to be used and the specific credential details.6
5.  Providing a name for the OAuth client ID and completing its creation.
6.  Securely copying the generated Client ID and Client Secret, as these will be needed to configure the application or client library.6

For "Web application" types, it is mandatory to specify one or more **authorized redirect URIs**. These are the endpoints to which the Google Authorization Server will redirect the user's browser after they have granted (or denied) access. For local development and testing, especially when using Google's client library examples, http://127.0.0.1 (often with a specific port) is commonly used as a redirect URI.7

### Understanding OAuth 2.0 Scopes for Google Ads API

The concept of scopes is central to OAuth 2.0. A scope is a string that defines the permission being requested by the application. When an application requests an access token, it specifies one or more scopes.11 For the Google Ads API, the primary and essential scope that must be requested is:

https://www.googleapis.com/auth/adwords.7

This scope grants permission to manage Google Ads campaigns. It must be explicitly added when configuring the OAuth consent screen in the Google API Console. Requesting insufficient scopes is a common cause of authorization failures, often resulting in an ACCESS\_TOKEN\_SCOPE\_INSUFFICIENT error.12 Therefore, ensuring the correct adwords scope is requested and granted is fundamental.

As a best practice, applications should request scopes incrementally, meaning they should only ask for the permissions they need at the time access is required, rather than requesting all possible permissions upfront.11 After receiving an access token, the application should examine the scopes actually granted by the user (as they might not grant all requested scopes) to ensure it has the necessary permissions for its intended operations.11

### Choosing the Right OAuth 2.0 Flow for Your Application

OAuth 2.0 supports several "flows" or grant types, tailored to different application architectures. Selecting the appropriate flow is a critical architectural decision.11

*   **Web Server Applications:** This flow is designed for applications that run on a secure server (e.g., written in PHP, Java, Python). The application redirects the user's browser to Google for authentication and consent. Upon successful authorization, Google redirects the browser back to the application's specified redirect URI with an authorization code. The application's backend then exchanges this code for an access token and, importantly, a refresh token. Refresh tokens allow the application to obtain new access tokens when the current one expires, without requiring the user to go through the consent process again. These refresh tokens must be stored securely.11 This flow is suitable if the application has a backend component that needs to perform actions on behalf of users, potentially even when the user is not actively using the application.
*   **Installed/Desktop Applications:** This flow is for applications installed directly on a user's device (computer, mobile phone, tablet). Similar to the web server flow, it involves redirecting the user (often via an embedded browser or system browser) to Google for authentication. The client ID is typically embedded in the application. For desktop applications, a loopback IP address (like http://127.0.0.1) is often used as the redirect URI. The Google Ads API documentation specifically notes that the "Desktop app" flow can be simpler for first-time users or for applications managing Google Ads accounts via a single top-level manager account, where authentication happens on one machine.7
*   Service Account (Server-to-Server): This flow is distinct because it authenticates the application itself rather than an end-user. It is used for server-to-server interactions where an application needs to access Google APIs to work with its own data or act on behalf of users in an enterprise context (e.g., Google Workspace domain with domain-wide delegation). User consent is generally not required for individual API calls after initial setup. The application uses a service account key file (typically a JSON file containing a private key) to create and cryptographically sign JSON Web Tokens (JWTs). These JWTs are then exchanged for access tokens.11  
    For many backend applications or automation tools interacting with the Google Ads API, the service account flow is often the most suitable and is strongly recommended by Google's documentation for the Ads API.6 This preference stems from its non-interactive nature once configured, making it ideal for automated tasks, scheduled jobs, or backend systems that manage Google Ads data programmatically without requiring user intervention for each session.

While other flows like Client-side (JavaScript/Implicit) and Limited-input Device flows exist 11, they are generally less common for the typical backend or tooling applications built with the Google Ads API.

**Table 3.1: OAuth 2.0 Flow Selection Guide for Google Ads API Applications**

| Application Type/Scenario | Recommended OAuth Flow | Key Characteristics | Official Google Guide Link(s) |
| --- | --- | --- | --- |
| Backend application performing scheduled tasks, automated reporting, or campaign management without direct user interaction per session. | Service Account | Authenticates the application itself. No user interaction for token refresh. Requires a service account key file. Ideal for server-to-server. 6 | (https://developers.google.com/identity/protocols/oauth2/service-account) 11 |
| Web application where users log in to manage their own or their clients' Google Ads accounts through the application's interface. | Web Server Applications | User authenticates via browser redirect. Application receives authorization code, exchanges for access & refresh tokens. Refresh tokens must be securely stored per user. | (https://developers.google.com/identity/protocols/oauth2/web-server) 11 |
| Desktop tool installed on a user's machine for managing their Google Ads account(s). | Installed Applications (Desktop app type) | User authenticates via browser (often system browser). Can use loopback redirect. Simpler for single-machine, single top-level manager scenarios. 7 | Using OAuth 2.0 for Installed Applications 11 |

### Implementing the Authorization Flow and Token Management

Regardless of the chosen flow, the general process involves:

1.  **Obtaining an Access Token:** The application initiates the chosen OAuth flow to request an access token from the Google Authorization Server, ensuring it includes the necessary https://www.googleapis.com/auth/adwords scope.11 This step might involve redirecting the user for login and consent unless a service account flow with pre-authorized delegation is used.
2.  **Sending the Access Token to the API:** Once an access token is obtained, the application includes it in the Authorization HTTP header for every API request made to the Google Ads API, typically as a "Bearer" token (e.g., Authorization: Bearer <access\_token>).11
3.  **Refreshing the Access Token:** Access tokens have a limited lifespan (usually one hour). For applications requiring long-term access (common in web server and installed app flows), a refresh token is used to obtain a new access token when the current one expires. This process happens without requiring the user to re-authenticate.11 Refresh tokens should be stored securely and persistently by the application. Service accounts manage token renewal internally using their key files.
4.  **Handling Initial API Enablement Errors:** A common pitfall during initial setup is forgetting to enable the Google Ads API within the Google Cloud Project. If this step is missed, API calls will fail with an error message similar to: "permission denied Google Ads API has not been used in project \[project-id\] before or it is disabled. Enable it by visiting...".14 Navigating to the provided URL and enabling the API resolves this issue, allowing subsequent calls to succeed.14

## 4\. Working with the Google Ads API: Client Libraries and Core Interactions

Once authentication is configured, the next step is to interact with the API. Google strongly recommends using its official client libraries for this purpose, as they significantly simplify development and handle many underlying complexities.

### Introduction to Google Ads API Client Libraries

Google provides a suite of client libraries designed to streamline the development process when working with the Google Ads API.15 These libraries offer several advantages:

*   **Simplified Development:** They provide high-level abstractions and pre-built functionalities for common API operations, reducing the amount of boilerplate code developers need to write.15
*   **Handling Low-Level Details:** Client libraries manage many of the complexities of direct API communication, such as authentication flows, request/response serialization, timeout settings, and result set pagination.16 For example, the libraries correctly handle the cost\_micros field, which is returned by the API as 1,000,000 times the UI value (e.g., 1 USD is 1,000,000 micros).13
*   **Performance:** Many of the official client libraries utilize gRPC "under the hood," a high-performance RPC framework, which can offer efficiency benefits over traditional REST/JSON communication.16
*   **Code Examples and Utilities:** They often come with extensive code examples and utility functions for common tasks, such as constructing resource names and handling field masks.17

Given these benefits, using an official client library is the recommended path, especially for developers new to the Google Ads API.15 Attempting to interact with the raw REST or gRPC interface directly is generally more complex and error-prone.

### Supported Languages and Installation

Google officially supports client libraries for several popular programming languages:

*   .NET (C#)
*   Java
*   PHP
*   Python
*   Ruby
*   Perl 15

Community-maintained libraries may also be available for other languages, such as Node.js and Go, though these are not officially supported by Google and should be used with an understanding of the associated risks.15

Installation of the official libraries is typically straightforward, utilizing standard package management tools for each respective language, such as NuGet for.NET, Maven or Gradle for Java, Composer for PHP, pip for Python, and RubyGems for Ruby.15

It's important to note that client libraries are versioned and are compatible with specific versions of the Google Ads API. For example, to use Google Ads API v19 with the Python client library, a minimum library version of 25.2.0 is required.15 This version dependency necessitates careful management of library versions within a project and an awareness of the Google Ads API release cycle. Developers must plan for ongoing maintenance to keep client libraries and API versions current, ensuring compatibility and access to the latest features while avoiding deprecated versions.

**Table 4.1: Official Google Ads API Client Libraries Overview**

| Language | Official Library Name/Package | Example Installation Command | Link to Official GitHub/Docs |
| --- | --- | --- | --- |
| .NET (C#) | Google.Ads.GoogleAds | Install-Package Google.Ads.GoogleAds (NuGet) | google-ads-dotnet 15 |
| Java | com.google.ads:google-ads | (Maven/Gradle dependency) | google-ads-java 15 |
| PHP | google/google-ads | composer require google/google-ads | google-ads-php 15 |
| Python | google-ads | pip install google-ads | google-ads-python 15 |
| Ruby | google-ads-googleads | gem install google-ads-googleads | google-ads-ruby 15 |
| Perl | Google::Ads::GoogleAds::Client | (Via CPAN) | google-ads-perl 15 |

### Configuration of Client Libraries

Proper configuration is essential for client libraries to authenticate and communicate with the Google Ads API correctly. Configuration can typically be done through a dedicated configuration file (e.g., google\_ads.yaml for Python, App.config or Web.config for.NET, adsapi\_php.ini for PHP) or by setting environment variables.1

Common configuration parameters include:

*   **Developer Token:** GOOGLE\_ADS\_DEVELOPER\_TOKEN (environment variable) or equivalent in the config file.15
*   **OAuth 2.0 Credentials:**
    *   For **Application Mode** (Web Server/Installed App flows): GOOGLE\_ADS\_CLIENT\_ID, GOOGLE\_ADS\_CLIENT\_SECRET, GOOGLE\_ADS\_REFRESH\_TOKEN.15
    *   For **Service Account Mode:** GOOGLE\_ADS\_JSON\_KEY\_FILE\_PATH (path to the service account key file), GOOGLE\_ADS\_IMPERSONATED\_EMAIL (if impersonating a user with domain-wide delegation).15
*   **Login Customer ID:** GOOGLE\_ADS\_LOGIN\_CUSTOMER\_ID.15 This is a particularly crucial setting, especially when operating within a Manager Account (MCC) hierarchy. It specifies the customer ID of the authorized MCC account making the request, effectively defining the "context" or "identity" under which the API call is made. This determines which client accounts are accessible. Misconfiguring this can lead to authorization errors or inability to access intended client accounts.
*   **Linked Customer ID:** GOOGLE\_ADS\_LINKED\_CUSTOMER\_ID. This header is required only for specific methods that update resources of an entity when permissioned through Linked Accounts in the Google Ads UI (e.g., updating third-party app analytics links).15
*   **Configuration File Path:** GOOGLE\_ADS\_CONFIGURATION\_FILE\_PATH can be used to specify a custom location for the configuration file.15

Each client library's official documentation provides detailed guides on configuration options and best practices.15

### Understanding API Interfaces: gRPC and REST

The Google Ads API supports two primary communication interfaces: gRPC and REST.16 Both interfaces adhere to a resource-oriented design, which is common across many Google Cloud APIs.17

*   **gRPC (Google Remote Procedure Call):**
    *   This is a modern, high-performance, open-source RPC framework that typically uses HTTP/2 for transport.16
    *   It utilizes Protocol Buffers (proto3) as its Interface Definition Language (IDL). Proto files define the structure of API services, methods, and message payloads.18
    *   The official Google Ads API client libraries predominantly use gRPC for their underlying communication with the API servers.16
    *   The authoritative .proto interface definitions for the Google Ads API (and other Google APIs) are publicly available in the googleapis/googleapis GitHub repository.18 For a specific API version, like v19, these files would be located under a path structure such as google/ads/googleads/v19/, with subdirectories for services, resources, enums, etc. For example, ad\_group.proto for API v18 is found at google/ads/googleads/v18/resources/ad\_group.proto 20, and google\_ads\_service.proto for v17 is at google/ads/googleads/v17/services/google\_ads\_service.proto.21
*   **REST (Representational State Transfer):**
    *   The API also exposes a traditional JSON/REST interface, allowing interaction via standard HTTP methods.16
    *   This interface can be used if developers choose not to use the Google-supported client libraries, perhaps preferring to write their own custom HTTP client code or utilize a third-party HTTP library.16
    *   While REST reference documentation is available, Google generally recommends using the gRPC documentation (which aligns with the client libraries) unless there's a specific need for direct REST interaction.17

While understanding these underlying interfaces can be beneficial for advanced use cases or deep troubleshooting, the strong and consistent recommendation from Google is to leverage the official client libraries, which abstract away most of these gRPC/REST details.

### Making Your First API Call (Conceptual)

After successfully completing all prerequisites, configuring OAuth 2.0, and setting up the chosen client library, an application can make its first API call. The conceptual flow typically involves:

1.  **Instantiating the Google Ads Client:** Initialize the main client object provided by the library, which will use the configured credentials (developer token, OAuth details).
2.  **Creating a Service Client:** Obtain a specific service client object from the main client (e.g., GoogleAdsServiceClient for general search/query operations, or CampaignServiceClient for campaign-specific mutations).
3.  **Constructing a Request:** Build the request object for the desired operation. For example, to fetch campaign data, this would involve creating a search request with a GAQL query. For creating a campaign, it would involve populating a Campaign object and wrapping it in a CampaignOperation.
4.  **Executing the Request:** Call the appropriate method on the service client, passing the constructed request and the target customer ID.
5.  **Processing the Response:** Handle the response returned by the API. This could be a list of entities, a stream of data, or a confirmation of a mutation operation.14

The official client library documentation and examples (e.g., fetching campaigns using GoogleAdsService.SearchStream 15) provide concrete code for these steps in each supported language.

## 5\. Key Operational Aspects: Leveraging the API for Your App

Beyond initial setup and authentication, an application will interact with the Google Ads API to perform various operational tasks. The core functionalities typically revolve around campaign management, performance reporting, and potentially billing management. A fundamental pattern in API interaction is the distinction between "mutate" operations (for creating, updating, or deleting entities) and "query" or "get" operations (for reading data). This dichotomy applies across different services and resource types.

### Campaign Management: Creating and Managing Campaigns, Ad Groups, and Ads (Conceptual Overview)

The Google Ads API provides comprehensive capabilities for programmatic campaign management. This includes the creation of new campaigns, ad groups, and ads, as well as updating their settings, statuses (e.g., pausing, enabling), and targeting criteria.2

When operating through a Manager Account (MCC), the API can be used to make updates across multiple linked Google Ads client accounts simultaneously. This is particularly useful for tasks like adjusting daily budgets or pausing campaigns in bulk across an entire portfolio.8

The general workflow for making changes (mutations) involves:

1.  Identifying the target entity (e.g., a specific campaign or ad group).
2.  Constructing an "operation" object that defines the change to be made (e.g., a CampaignOperation to update a campaign's budget, or an AdGroupAdOperation to create a new ad).
3.  Sending these operation objects in a mutate request to the relevant service client (e.g., CampaignServiceClient.MutateCampaigns, AdGroupServiceClient.MutateAdGroups).
4.  Processing the response to confirm the success or failure of the operations.

Google provides code samples for various campaign management tasks, such as "Add complete campaigns using batch job" for efficiently creating multiple campaigns, "Add campaign labels" for organization, and "Validate ad" to check ad compliance before submission.23

### Performance Reporting: Retrieving Data with GAQL (Resources, Segments, Metrics)

A primary use case for the Google Ads API is to retrieve detailed performance data for custom reporting and analysis.2 This is achieved by constructing queries using the Google Ads Query Language (GAQL) and executing them via the GoogleAdsService.

Understanding GAQL is non-negotiable for any reporting functionality. It is a powerful, SQL-like language specifically designed for querying Google Ads data. Developers must learn its syntax and how to select appropriate resources, segments, and metrics to extract the desired information. This represents a significant learning curve but unlocks highly customizable reporting.

The key components of a GAQL query for reporting are:

*   **Resources:** These are the primary entities from which data is fetched. Examples include campaign, ad\_group, ad\_group\_ad, keyword\_view, or customer.26 The FROM clause in a GAQL query specifies the main resource.
*   **Segments:** These are dimensions used to break down or categorize the performance data. Common segments include segments.date, segments.device (e.g., mobile, desktop, tablet), segments.network (e.g., Search, Display), or segments.geo\_target\_city.25 Segments are included in the SELECT clause and can also be used for filtering in the WHERE clause.
*   **Metrics:** These are the quantitative performance measures. Examples include metrics.clicks, metrics.impressions, metrics.ctr (click-through rate), metrics.conversions, metrics.cost\_micros (cost in micro-currency units), and metrics.average\_cpc (average cost per click).13 Metrics are included in the SELECT clause.

A typical GAQL query structure is:

SELECT <fields, segments, metrics> FROM <resource> WHERE <conditions> ORDER BY <fields> LIMIT <count>

For instance, to get daily clicks and impressions for a specific campaign:

SELECT segments.date, metrics.clicks, metrics.impressions FROM campaign WHERE campaign.id = <campaign\_id> AND segments.date DURING LAST\_30\_DAYS ORDER BY segments.date

The API offers two main methods for executing these queries:

*   GoogleAdsService.SearchStream: This method is generally recommended for fetching entities, especially large result sets. It returns the data as a stream of rows, which can be processed efficiently without loading the entire response into memory at once.15
*   GoogleAdsService.Search: This method returns results in paginated fixed-size pages (typically 10,000 rows per page). The client libraries automatically handle the pagination when iterating through results.15

A crucial detail for financial metrics is that cost-related values (e.g., metrics.cost\_micros, metrics.average\_cpc if it's derived from cost) are reported in **micros**. This means the value returned by the API is 1,000,000 times the value displayed in the Google Ads UI for the base currency unit (e.g., if the cost is $10.50, the API will return 10,500,000).13 Applications must convert these micro-amounts to the standard currency unit for display or further calculation.

### Billing Management: Account Budgets and Invoices (Conceptual Overview)

The Google Ads API also provides functionalities for managing billing aspects, although this capability is contingent on the specific Google Ads account being configured for **monthly invoicing**.27 If an account is not set up for monthly invoicing, the billing-related API features cannot be used for that account. This is a critical external dependency that applications offering billing management features must consider.

The key entities involved in API-driven billing management are:

*   **BillingSetup**: This resource represents the link between a Payments account (which determines who pays) and a specific Google Ads account. It's used to get and manage the account-wide billing configuration.27
*   **AccountBudget**: This defines the budget properties at the account level, such as start and end times, and spending limits.27
*   **AccountBudgetProposal**: Changes to account budgets are not made directly. Instead, an AccountBudgetProposal is submitted. This proposal, once reviewed and approved by Google, becomes an active AccountBudget. This mechanism is used for creating new budgets or updating existing ones.27
*   **Invoice**: For accounts on monthly invoicing, the API can be used to retrieve monthly invoices. These invoices contain details such as adjustments, regulatory costs, taxes, and the budgets applied. They can often be downloaded as PDF files and are useful for reconciliation purposes.27

Google provides code samples for billing operations, including "Add account budget proposal," "Add billing setup," and "Get invoices," which can serve as starting points for developers.23

## 6\. Adherence and Best Practices: Ensuring Smooth Operation

Successful and sustainable integration with the Google Ads API requires strict adherence to Google's policies and diligent application of best practices regarding API usage, quotas, and error handling. Non-compliance can lead to operational disruptions, financial penalties, or even suspension of API access.

### Understanding Google Ads API Policies

Google has established a comprehensive set of policies to ensure the API is used responsibly and effectively, maintaining a fair and transparent ecosystem for advertisers.10 Developers must familiarize themselves with these policies, as Google may review API usage at any time to ensure compliance.10 Key policy areas include:

*   **General API Policies:**
    *   The API must only be used for Google Ads campaign creation, management, or reporting, strictly adhering to the functionality described in the developer token application. Any significant changes to how the tool uses the API (e.g., adding management features to a reporting-only tool) require submitting a tool change form to Google.10
*   **Required Minimum Functionality (RMF):**
    *   Developers of certain types of tools, particularly full-service tools offered to third-party advertisers and holding Standard Access tokens, are required to implement a specific set of features related to campaign creation, management, and reporting. RMF aims to ensure users have access to core Google Ads functionalities regardless of the third-party tool they use. RMF generally does not apply to tools developed for internal use only.10 Non-compliance with RMF can result in non-compliance fees and downgrading of the API token's access level.10
*   **Required Disclosures and Other Responsibilities:**
    *   If an API tool facilitates bulk editing or data transfer between Google Ads and other advertising platforms, any incompatibilities or inconsistencies must be disclosed to clients. Clients must also be offered the ability to adjust or cancel such transfers.10
    *   Any delays in reporting Google Ads performance data exceeding 24 hours must be prominently disclosed to clients.10
    *   Agencies and third parties managing Google advertising for clients must comply with Google's third-party policy.10
    *   Written consent must be obtained from clients before selling, redistributing, or otherwise disclosing their Google Ads account-specific data.10
    *   End-advertiser clients must be provided with a quick and easy method to opt-out of the tool's management of their Google Ads campaigns and regain exclusive control of their accounts.10
    *   All advertiser data transferred via the API must be secured using at least 128-bit SSL encryption or equivalent.10
*   **Prohibited Practices:**
    *   Scraping data from TargetingIdeaService or TrafficEstimatorService for purposes other than creating or managing Google Ads campaigns is forbidden.10
    *   Scraping Google Search result pages or any other Google property, or purchasing scraped Google data from third parties, is strictly prohibited.10
    *   Developers cannot require their end-advertiser clients to apply for their own Google Ads API token to use the developer's tool (this is known as requiring "supplemental tokens").10
    *   Allowing agencies, end-advertisers, or other third parties to use the developer's API token (or a secondary API built on top of it) in a way that enables them to bypass applying for their own Google Ads API token or circumvent RMF is not permitted.10
    *   All API users and tools built upon the API must comply with the general Google Ads advertising policies and terms and conditions.10
    *   Unauthorized use of Google branding, trademarks, or replicating the look and feel of Google Ads interfaces in a way that might confuse users is prohibited.10
    *   Interfering with Google's monitoring or auditing of API activity, or attempting to conceal API activity, is a policy violation.10
*   **Policy Enforcement:**
    *   Developers must maintain up-to-date contact information in the API Center of their MCC account, as this is the primary channel for compliance communications. Failure to respond to Google's notices can be considered a violation.10
    *   Upon request, developers must provide a functional demo account of their API tool within 7 days for compliance review.10
    *   Violations of API policies can lead to warnings, non-compliance fees (as per the API rate sheet), downgrading of the API token's access level (e.g., from Standard to Basic), imposition of stricter quota limits, or, in severe cases, termination of the Google Ads API token.10

These policies are not static, and developers bear the responsibility of staying informed about any updates. The requirement for a monitored API contact email and the possibility of compliance reviews signify an ongoing relationship with Google Compliance, extending beyond the initial setup.

### API Quotas and Rate Limits

To ensure system stability and fair usage, the Google Ads API enforces various quotas and rate limits on API requests.13 Designing applications to operate within these limits is crucial for reliability and performance. It's important to recognize that there isn't a single, universal rate limit; limits vary significantly by the type of operation, the service being called, and the developer token's access level. This complexity requires a nuanced approach to application design rather than a simple, global throttling mechanism.

**Table 6.1: Key Google Ads API Rate Limits and Quotas**

| Limit Type | Value/Constraint | Scope | Consequence of Exceeding / Error Code | Supporting Snippets |
| --- | --- | --- | --- | --- |
| Daily API Operations (Basic Access) | 15,000 operations/day | Per Developer Token | RESOURCE_EXHAUSTED | 30 |
| Daily API Operations (Standard Access) | Varies (higher than Basic Access) | Per Developer Token | RESOURCE_EXHAUSTED | 31 (implied by access levels) |
| Mutate Operations per Request | 10,000 operations | Per Mutate Request | TOO_MANY_MUTATE_OPERATIONS | 31 |
| gRPC Message Size | 64 MB (max, set by client libraries) | Per Request/Response | gRPC 429 Resource Exhausted (not a GoogleAdsError) | 31 |
| KeywordPlanIdeaService QPS (e.g., GenerateKeywordIdeas) | 1 request per second (QPS) (calculated as 60 requests per 60 seconds) | Per Customer ID (CID) | RESOURCE_EXHAUSTED | 31 |
| Conversion Uploads per Request (e.g., UploadCallConversions) | 2,000 conversions | Per Request | TOO_MANY_CONVERSIONS_IN_REQUEST | 31 |
| GAQL IN Clause Items | 20,000 items | Per Query | FILTER_HAS_TOO_MANY_VALUES | 31 |
| Billing/Account Budget Mutate Operations | 1 operation | Per Mutate Request | TOO_MANY_MUTATE_OPERATIONS | 31 |

*   **Daily API Usage Limits:** These are based on the total number of API operations (a sum of get requests and mutate operations) made per developer token per day. The specific limit depends on the token's access level (e.g., Basic Access allows 15,000 operations per day).30 Exceeding this daily quota will result in requests being rejected with a RESOURCE\_EXHAUSTED error.
*   **gRPC Message Size Limit:** Google Ads API client libraries typically set the maximum gRPC message size to 64 MB. If a request (e.g., a search request with many fields) generates a response larger than this, or if a mutate request is too large, it will result in a gRPC 429 Resource Exhausted error, which is distinct from a standard GoogleAdsError. To mitigate this, developers can reduce the number of selected fields in search queries, use streaming for large search results, or send fewer operations per mutate request.31
*   **Mutate Request Limits:** In addition to daily operation quotas, a single mutate request cannot contain more than 10,000 operations. Violations lead to a TOO\_MANY\_MUTATE\_OPERATIONS error.31
*   **Search and SearchStream Requests:** A Search or SearchStream request generally counts as one operation towards the daily quota, irrespective of the number of batches processed by SearchStream. However, paginated requests that include a valid next\_page\_token (for retrieving subsequent pages of results) do _not_ count against the daily operation quota.31
*   **Specific Service Limits:** Many individual services have their own granular limits. For example:
    *   The **Keyword Planning Service** (e.g., KeywordPlanIdeaService.GenerateKeywordIdeas) is often limited to 1 request per second (QPS) per Customer ID (CID).31
    *   The **Conversion Upload Service** (e.g., ConversionUploadService.UploadCallConversions) typically limits uploads to 2,000 conversions per request. Exceeding this results in a TOO\_MANY\_CONVERSIONS\_IN\_REQUEST error.31
    *   **Billing and Account Budget Services** usually allow only one operation per mutate request and require that the target account be configured for monthly invoicing. Additionally, it's advised to wait at least 12 hours between successive budget order changes to the same account to prevent potential unrecoverable failures.31
*   **Other Limits:** There's a maximum of 20,000 items allowed within an IN clause in a GAQL query. Exceeding this triggers a FILTER\_HAS\_TOO\_MANY\_VALUES error.31

Applications must be designed with these varied limits in mind, potentially implementing queuing mechanisms, intelligent batching of operations, and careful query construction to ensure efficient and uninterrupted operation.

### Error Handling Strategies

Robust error handling is essential for building reliable applications that can gracefully manage issues arising from API interactions. The Google Ads API can return a variety of errors, and a sophisticated error handling strategy involves more than just generic try-catch blocks. It requires categorizing errors and implementing specific responses for each type.12

Common error categories and handling approaches include:

*   **Authentication Errors:** These occur when the application fails to authenticate correctly.
    *   OAUTH\_TOKEN\_REVOKED: The user has revoked the application's permission. The application should prompt the user to re-authorize (re-run the OAuth 2.0 flow).32
    *   GOOGLE\_ACCOUNT\_COOKIE\_INVALID or similar errors indicating an expired or invalid access token: The application should use the refresh token (if available) to obtain a new access token. If using a client library, it often handles this automatically.12
    *   DEVELOPER\_TOKEN\_NOT\_APPROVED: The developer token used is not approved for the type of access being attempted.12
    *   ACCESS\_TOKEN\_SCOPE\_INSUFFICIENT: The access token does not have the required OAuth scope (e.g., https://www.googleapis.com/auth/adwords) for the requested operation.12
*   **Retryable Errors:** Some errors indicate temporary issues with the API service.
    *   Examples include TRANSIENT\_ERROR or INTERNAL\_ERROR.
    *   The recommended strategy is to retry the request after a short pause, implementing an **exponential backoff** policy. For example, wait 5 seconds before the first retry, 10 seconds before the second, 20 before the third, and so on, up to a maximum number of retries or total wait time. This helps prevent overwhelming the API during transient outages.32
*   **Validation Errors:** These errors indicate that some input provided in the API request was invalid or unacceptable.
    *   Examples include PolicyViolationError, DateError, DateRangeError, StringLengthError, UrlFieldError, BID\_TOO\_SMALL, BID\_TOO\_BIG.12
    *   For user-initiated requests, the application should provide clear feedback to the user based on the specific error received, allowing them to correct the input. Input validation within the application _before_ making the API call can also prevent many such errors.
    *   For backend-initiated requests, the failed operation might be added to a queue for human operator review and correction.32
*   **Sync-Related Errors:** These often occur when an application maintains a local database or cache of Google Ads objects that becomes out of sync with the actual state on Google's servers.
    *   Examples include DUPLICATE\_CAMPAIGN\_NAME, DUPLICATE\_ADGROUP\_NAME, AD\_NOT\_UNDER\_ADGROUP, CANNOT\_OPERATE\_ON\_REMOVED\_ADGROUPAD.32
    *   If a user deletes an ad group directly in the Google Ads UI, but the application's local database is unaware, subsequent API calls attempting to modify that ad group will fail.
    *   Handling strategies include alerting the user to a potential sync issue, triggering a job to refresh the relevant local data from Google Ads, and then prompting the user to retry. For backend processes, some errors (like CANNOT\_OPERATE\_ON\_REMOVED\_ADGROUPAD) might allow the application to automatically update its local state (e.g., mark the ad as removed).32

It's important to note that requests that are rejected and return a GoogleAdsFailure object (which contains detailed error information) still count against the daily API operation quota.31 However, requests that fail at a network level before reaching the API service typically do not count. The official documentation includes a page on "Common Errors" 12 which lists many specific error codes, their causes, and tips for handling or preventing them. Application logic should be designed to inspect the details within GoogleAdsFailure to determine the appropriate response.

## 7\. Conclusion and Next Steps for Your App Development

Integrating an application with the Google Ads API is a significant undertaking that offers powerful capabilities for managing and optimizing advertising campaigns. This report has outlined the critical aspects involved, from understanding the API's core functionalities and prerequisites to navigating authentication, utilizing client libraries, performing key operations, and adhering to essential policies and best practices.

### Recap of Key Integration Points

The journey to a successful Google Ads API integration follows a structured path:

1.  **Foundational Understanding:** Grasping the API's purpose for advanced, large-scale management and its requirement for coding expertise.
2.  **Prerequisites Fulfillment:** Methodically setting up a Google Ads Manager Account, applying for and obtaining a Developer Token (which involves a review process and understanding access levels), and configuring a Google API Console Project.
3.  **Authentication Setup:** Choosing the appropriate OAuth 2.0 flow (with service accounts often being preferred for backend applications), obtaining client ID and secret, configuring the consent screen, and correctly requesting the https://www.googleapis.com/auth/adwords scope.
4.  **Client Library Utilization:** Leveraging official client libraries for the chosen programming language to simplify development, handle low-level communication details, and manage API versioning.
5.  **Core API Operations:** Understanding how to use the API for campaign management (mutations), performance reporting (GAQL queries via GoogleAdsService), and potentially billing management (if accounts use monthly invoicing).
6.  **Adherence and Best Practices:** Strictly following Google Ads API policies (RMF, data handling, prohibited practices), operating within defined rate limits and quotas, and implementing robust, categorized error handling with strategies like exponential backoff.

A recurring theme is the dynamic nature of the Google Ads API. It is not a static target; it evolves with new versions, feature enhancements, and policy updates. Consequently, a commitment to continuous learning and adaptation is crucial for the long-term success and maintenance of any application built upon it. Developers must stay informed through official channels and be prepared to update their applications to remain compatible and compliant.

Furthermore, while not a direct step in the initial integration plan, the consistent availability of "test accounts" 5 (especially with initial Developer Token Test Access) underscores a critical ongoing best practice: thorough testing in a non-production environment. All development, feature additions, and bug fixes should be rigorously tested against test accounts before deploying any changes that would affect live, production Google Ads accounts. This minimizes risks of unintended ad spend, incorrect campaign modifications, or policy violations.

### Pointers to Further Resources

To continue the development journey and address specific challenges, the following official resources are invaluable:

*   **Google Ads API Developer Site:** The central hub for all official documentation, guides, reference materials, and announcements.1
    *   _URL (general):_ [https://developers.google.com/google-ads/api/](https://developers.google.com/google-ads/api/)
*   **Google Ads API Support Forum:** A community forum where developers can ask questions, share solutions, and discuss best practices with fellow developers and sometimes Google Ads API team members.1
    *   _URL (example from snippets for AdWords API, but a similar one exists for Google Ads API):_ [https://groups.google.com/g/adwords-api](https://groups.google.com/g/adwords-api) (Note: Search for the current Google Ads API specific forum).
*   **Google Ads API Release Notes and Developer Blog:** Essential for staying updated on new API versions, feature deprecations, policy changes, and important announcements.33
*   **Client Library Specific Documentation:** Each official client library (Java, Python,.NET, PHP, Ruby, Perl) has its own detailed documentation, often hosted on GitHub or linked from the main developer site, covering installation, configuration, and usage examples.15
*   **Google API Console:** For managing API projects, credentials, and enabled APIs.11
    *   _URL:_ [https://console.cloud.google.com/](https://console.cloud.google.com/)
*   **Google Cloud Platform Documentation:** For broader topics related to Google Cloud projects, billing (if applicable for other services), and service accounts.7

By leveraging these resources and adhering to the principles outlined in this report, developers can effectively harness the power of the Google Ads API to build innovative and impactful applications.

#### Works cited

1.  Manage large accounts efficiently with the API – Google Ads, accessed June 4, 2025, [https://business.google.com/aunz/ad-tools/google-ads-api/](https://business.google.com/aunz/ad-tools/google-ads-api/)
2.  Introduction | Google Ads API, accessed June 4, 2025, [https://developers.google.com/google-ads/api/docs/get-started/introduction](https://developers.google.com/google-ads/api/docs/get-started/introduction)
3.  Seamless Account Management with Google Ads API, accessed June 4, 2025, [https://business.google.com/us/ad-tools/google-ads-api/](https://business.google.com/us/ad-tools/google-ads-api/)
4.  Manage Large Accounts Efficiently with API - Google Ads, accessed June 4, 2025, [https://ads.google.com/home/<USER>/api/](https://ads.google.com/home/<USER>/api/)
5.  Obtain a developer token | Google Ads API | Google for Developers, accessed June 4, 2025, [https://developers.google.com/google-ads/api/docs/get-started/dev-token](https://developers.google.com/google-ads/api/docs/get-started/dev-token)
6.  Set up a Google API Console project | Google Ads API | Google for ..., accessed June 4, 2025, [https://developers.google.com/google-ads/api/docs/get-started/oauth-cloud-project](https://developers.google.com/google-ads/api/docs/get-started/oauth-cloud-project)
7.  Configure a Google API Console Project for the Google Ads API, accessed June 4, 2025, [https://developers.google.com/google-ads/api/docs/oauth/cloud-project](https://developers.google.com/google-ads/api/docs/oauth/cloud-project)
8.  Manage Multiple Google Ads Accounts, accessed June 4, 2025, [https://business.google.com/us/ad-tools/manage-accounts/](https://business.google.com/us/ad-tools/manage-accounts/)
9.  Manage Clients and Campaigns with Manager Accounts - Google Ads, accessed June 4, 2025, [https://ads.google.com/home/<USER>/manager-accounts/](https://ads.google.com/home/<USER>/manager-accounts/)
10.  Google Ads API policies - Advertising Policies Help, accessed June 4, 2025, [https://support.google.com/adspolicy/answer/6169371?hl=en](https://support.google.com/adspolicy/answer/6169371?hl=en)
11.  Using OAuth 2.0 to Access Google APIs | Google Account ..., accessed June 4, 2025, [https://developers.google.com/identity/protocols/oauth2](https://developers.google.com/identity/protocols/oauth2)
12.  Common Errors - Ads API - Google for Developers, accessed June 4, 2025, [https://developers.google.com/google-ads/api/docs/common-errors](https://developers.google.com/google-ads/api/docs/common-errors)
13.  Google Ads - Dataddo!, accessed June 4, 2025, [https://docs.dataddo.com/docs/google-ads](https://docs.dataddo.com/docs/google-ads)
14.  Google Ads API Auth Series - Web Flow with the OAuth Playground - YouTube, accessed June 4, 2025, [https://www.youtube.com/watch?v=KFICa7Ngzng](https://www.youtube.com/watch?v=KFICa7Ngzng)
15.  Client Libraries | Google Ads API | Google for Developers, accessed June 4, 2025, [https://developers.google.com/google-ads/api/docs/client-libs](https://developers.google.com/google-ads/api/docs/client-libs)
16.  Client libraries explained | Cloud APIs - Google Cloud, accessed June 4, 2025, [https://cloud.google.com/apis/docs/client-libraries-explained](https://cloud.google.com/apis/docs/client-libraries-explained)
17.  REST Interface | Google Ads API | Google for Developers, accessed June 4, 2025, [https://developers.google.com/google-ads/api/rest/overview](https://developers.google.com/google-ads/api/rest/overview)
18.  googleapis/googleapis: Public interface definitions of ... - GitHub, accessed June 4, 2025, [https://github.com/googleapis/googleapis](https://github.com/googleapis/googleapis)
19.  gRPC overview | API Gateway Documentation - Google Cloud, accessed June 4, 2025, [https://cloud.google.com/api-gateway/docs/grpc-overview](https://cloud.google.com/api-gateway/docs/grpc-overview)
20.  googleapis/google/ads/googleads/v18/resources/ad\_group.proto at master - GitHub, accessed June 4, 2025, [https://github.com/googleapis/googleapis/blob/master/google/ads/googleads/v18/resources/ad\_group.proto](https://github.com/googleapis/googleapis/blob/master/google/ads/googleads/v18/resources/ad_group.proto)
21.  googleapis/google/ads/googleads/v17/services/google\_ads\_service.proto at master - GitHub, accessed June 4, 2025, [https://github.com/googleapis/googleapis/blob/master/google/ads/googleads/v17/services/google\_ads\_service.proto](https://github.com/googleapis/googleapis/blob/master/google/ads/googleads/v17/services/google_ads_service.proto)
22.  APIs and services – Google Cloud console, accessed June 4, 2025, [https://console.cloud.google.com/apis/library/googleads.googleapis.com?hl=en-GB](https://console.cloud.google.com/apis/library/googleads.googleapis.com?hl=en-GB)
23.  Code samples | Google Ads API | Google for Developers, accessed June 4, 2025, [https://developers.google.com/google-ads/api/samples](https://developers.google.com/google-ads/api/samples)
24.  Create Campaigns | Google Ads API | Google for Developers, accessed June 4, 2025, [https://developers.google.com/google-ads/api/docs/campaigns/create-campaigns](https://developers.google.com/google-ads/api/docs/campaigns/create-campaigns)
25.  Google Ads API Reference, accessed June 4, 2025, [https://docs.datavirtuality.com/connectors/google-ads-api-reference](https://docs.datavirtuality.com/connectors/google-ads-api-reference)
26.  Reports | Google Ads API | Google for Developers, accessed June 4, 2025, [https://developers.google.com/google-ads/api/fields/v19/overview](https://developers.google.com/google-ads/api/fields/v19/overview)
27.  Billing | Google Ads API | Google for Developers, accessed June 4, 2025, [https://developers.google.com/google-ads/api/docs/billing/overview](https://developers.google.com/google-ads/api/docs/billing/overview)
28.  Billing Setup - Ads API - Google for Developers, accessed June 4, 2025, [https://developers.google.com/google-ads/api/docs/billing/billing-setups](https://developers.google.com/google-ads/api/docs/billing/billing-setups)
29.  HubSpot API reference, accessed June 4, 2025, [https://developers.hubspot.com/docs/reference/api/overview](https://developers.hubspot.com/docs/reference/api/overview)
30.  Quotas? - Google Groups, accessed June 4, 2025, [https://groups.google.com/g/adwords-api/c/vDY3OfsVCac](https://groups.google.com/g/adwords-api/c/vDY3OfsVCac)
31.  API Limits and Quotas | Google Ads API | Google for Developers, accessed June 4, 2025, [https://developers.google.com/google-ads/api/docs/best-practices/quotas](https://developers.google.com/google-ads/api/docs/best-practices/quotas)
32.  Error Types - Ads API - Google for Developers, accessed June 4, 2025, [https://developers.google.com/google-ads/api/docs/best-practices/error-types](https://developers.google.com/google-ads/api/docs/best-practices/error-types)
33.  Release of v19 | Google Ads API, accessed June 4, 2025, [https://developers.google.com/google-ads/api/videos/catalog/release-19](https://developers.google.com/google-ads/api/videos/catalog/release-19)