# Brand Wisdom Solutions - Brand Style Guide

> **Version 1.0** — Comprehensive brand guidelines for consistent visual identity across all digital touchpoints.

---

## 1. Brand Identity

### 1.1 Brand Overview
**Brand Wisdom Solutions** is a professional AI-powered business solutions company that combines cutting-edge technology with strategic business insights. Our brand reflects innovation, reliability, and professional excellence.

**Brand Personality:**
- Professional yet approachable
- Innovation-driven
- Solution-oriented
- Trustworthy and reliable
- Forward-thinking

### 1.2 Logo & Visual Identity

**Primary Logo:**
- Logo file: `Brandwisdomlogo-1.webp`
- Placement: Top left of navigation
- Header size: 40px height (h-10)
- Clear space: Minimum 16px around all sides
- Usage: Primary logo for all digital applications

**Logo Guidelines:**
- Always maintain aspect ratio
- Ensure sufficient contrast with background
- Never stretch, skew, or modify the logo
- Use on white or light backgrounds for optimal visibility

---

## 2. Color Palette

### 2.1 Primary Colors

| Color Name | Hex Code | RGB | Usage |
|------------|----------|-----|-------|
| **Primary 50** | `#F4F7FF` | 244, 247, 255 | Light backgrounds, subtle fills |
| **Primary 100** | `#E7EEFF` | 231, 238, 255 | Muted backgrounds |
| **Primary 300** | `#B9CBFF` | 185, 203, 255 | Subtle accents, borders |
| **Primary 500** | `#4172F5` | 65, 114, 245 | Links, icons, primary actions |
| **Primary 600** | `#3E5CE7` | 62, 92, 231 | Primary buttons, hover states |
| **Primary 700** | `#324ECF` | 50, 78, 207 | Active states, pressed buttons |
| **Primary 900** | `#07153F` | 7, 21, 63 | Dark text, headers |

### 2.2 Secondary Colors

| Color Name | Hex Code | RGB | Usage |
|------------|----------|-----|-------|
| **Secondary** | `#444751` | 68, 71, 81 | Secondary text, subtle elements |
| **Text** | `#6F7176` | 111, 113, 118 | Body text, descriptions |
| **Gold 500** | `#FECD79` | 254, 205, 121 | Accent highlights, micro-interactions |

### 2.3 Neutral Colors

| Color Name | Hex Code | RGB | Usage |
|------------|----------|-----|-------|
| **White** | `#FFFFFF` | 255, 255, 255 | Backgrounds, cards |
| **Grey 100** | `#F3F6FA` | 243, 246, 250 | Light backgrounds |
| **Grey 400** | `#6F7176` | 111, 113, 118 | Body text |
| **Grey 900** | `#1A1E29` | 26, 30, 41 | Dark text, headings |

### 2.4 Status Colors

| Color Name | Hex Code | RGB | Usage |
|------------|----------|-----|-------|
| **Success** | `#27C084` | 39, 192, 132 | Success states, positive indicators |
| **Error** | `#EF5E5E` | 239, 94, 94 | Error states, warnings |

### 2.5 Gradients

**Brand Gradient:**
```css
background: linear-gradient(135deg, #4172F5 0%, #285CF7 100%);
```

**Ring Gradient (Subtle backgrounds):**
```css
background: radial-gradient(circle at center, rgba(65,114,245,.08) 0%, rgba(65,114,245,0) 70%);
```

---

## 3. Typography

### 3.1 Font Families

**Primary Font:** Jost
- Usage: Body text, UI elements, general content
- Weights: 100-900 (variable)
- Style: Modern, clean, highly readable

**Secondary Font:** Playfair Display
- Usage: Headings, titles, emphasis
- Weights: 400-900
- Style: Elegant, sophisticated serif

**System Font:** Inter
- Usage: System UI, fallback
- Weights: 300-700
- Style: Technical, clean sans-serif

### 3.2 Typography Scale

| Style | Font | Weight | Size | Line Height | Letter Spacing |
|-------|------|--------|------|-------------|----------------|
| **Display 1** | Playfair Display | 700 | 64px | 110% | -1% |
| **Display 1 Mobile** | Playfair Display | 700 | 48px | 110% | -1% |
| **H1** | Playfair Display | 700 | 48px | 120% | -0.5% |
| **H2** | Playfair Display | 700 | 36px | 120% | -0.25% |
| **H3** | Playfair Display | 600 | 28px | 130% | 0 |
| **H4** | Playfair Display | 600 | 22px | 130% | 0 |
| **Body Large** | Jost | 400 | 18px | 160% | 0 |
| **Body Medium** | Jost | 400 | 16px | 160% | 0 |
| **Body Small** | Jost | 400 | 15px | 150% | 0 |
| **Caption** | Jost | 500 | 12px | 140% | 0.2px |

### 3.3 Text Styling Guidelines

**Headings:**
- Color: `#07153F` (Primary 900) or `#1A1E29` (Grey 900)
- Font: Playfair Display
- Style: Sentence case
- Weight: Semibold to Bold

**Body Text:**
- Color: `#6F7176` (Text color)
- Font: Jost
- Weight: Regular (400)
- Line height: 1.6 for optimal readability

**Links:**
- Color: `#4172F5` (Primary 500)
- Hover: `#3E5CE7` (Primary 600)
- Underline: None by default, on hover for body links

**Buttons/Labels:**
- Text transform: UPPERCASE
- Font weight: Bold (700)
- Letter spacing: Wide tracking

---

## 4. Spacing & Layout

### 4.1 Spacing Scale (8pt Grid)

```
0 = 0px
1 = 4px
2 = 8px
3 = 12px
4 = 16px
5 = 24px
6 = 32px
7 = 40px
7.5 = 56px (Special for CTA blocks)
8 = 64px
9 = 80px
```

### 4.2 Border Radius

| Size | Value | Usage |
|------|-------|-------|
| **Small** | 5px | Buttons, small elements |
| **Medium** | 10px | Cards, form inputs |
| **Large** | 20px | Large cards, containers |
| **Extra Large** | 30px | Hero sections, major containers |

### 4.3 Container Widths

**Desktop:**
- Max width: 1280px
- Padding: 2rem (32px)

**Mobile:**
- Full width with 16px side padding
- Responsive breakpoints follow standard conventions

---

## 5. Components

### 5.1 Buttons

**Primary Button:**
```css
background: #3E5CE7;
color: white;
padding: 12px 24px;
border-radius: 5px;
font-weight: bold;
text-transform: uppercase;
transition: all 0.3s ease;
box-shadow: 0 2px 4px rgba(0,0,0,0.1);
```

**Hover State:**
```css
background: white;
color: #3E5CE7;
border: 1px solid #3E5CE7;
transform: translateY(-2px);
box-shadow: 0 4px 8px rgba(0,0,0,0.15);
```

**Secondary Button:**
```css
background: transparent;
color: #3E5CE7;
border: 1px solid #3E5CE7;
padding: 12px 24px;
border-radius: 5px;
```

### 5.2 Cards

**Standard Card:**
```css
background: white;
border-radius: 20px;
padding: 32px;
box-shadow: 0 4px 8px rgba(0,0,0,0.04);
border: 1px solid #F3F6FA;
```

### 5.3 Navigation

**Header:**
- Height: 64px (h-16)
- Background: White with shadow on scroll
- Logo: Left aligned, 40px height
- Navigation: Right aligned
- Sticky positioning

---

## 6. Usage Guidelines

### 6.1 Do's
- Maintain consistent spacing using the 8pt grid
- Use the defined color palette exclusively
- Ensure proper contrast ratios (minimum 4.5:1)
- Keep typography hierarchy clear and consistent
- Use appropriate font families for their designated purposes

### 6.2 Don'ts
- Don't modify the logo or use unofficial variations
- Don't use colors outside the defined palette
- Don't mix font families inappropriately
- Don't ignore spacing guidelines
- Don't use harsh gradients or overly bright colors

### 6.3 Accessibility
- Maintain WCAG 2.1 AA compliance
- Ensure sufficient color contrast
- Use semantic HTML structure
- Provide alt text for images
- Support keyboard navigation

---

## 7. Implementation

### 7.1 CSS Variables
```css
:root {
  /* Colors */
  --c-primary-600: #3E5CE7;
  --c-primary-900: #07153F;
  --c-text: #6F7176;
  --c-grey-100: #F3F6FA;
  
  /* Fonts */
  --font-primary: 'Jost', sans-serif;
  --font-secondary: 'Playfair Display', serif;
  
  /* Spacing */
  --space-4: 16px;
  --space-6: 32px;
  --space-8: 64px;
  
  /* Border Radius */
  --radius-sm: 5px;
  --radius-lg: 20px;
}
```

---

*This style guide ensures consistent brand application across all Brand Wisdom Solutions digital properties and maintains professional visual identity standards.*
