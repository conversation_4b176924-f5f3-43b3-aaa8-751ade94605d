# Product Requirements Document (PRD)
# Google Ads AI Search Optimization Platform

**Version:** 2.1  
**Date:** June 2025  
**Status:** Final (Enhanced Tech Stack)  
**Author:** Brand Wisdom Solutions Product Team  

---

## Tech Stack Enhancement Summary

**Key Technology Upgrades:**
- **Form Handling**: React Hook Form + Zod for type-safe, validated forms
- **Animations**: Framer Motion for premium UI interactions
- **Notifications**: Sonner for elegant toast notifications
- **Routing**: React Router DOM for simplified navigation
- **Date Handling**: date-fns for consistent date operations
- **Development**: ESLint 9+ with TypeScript rules, Vite with SWC

These technologies will enable:
- 50% faster form development with automatic validation
- Premium user experience with smooth animations
- Real-time feedback for all user actions
- Type-safe development with fewer bugs
- Consistent, maintainable codebase

---

## 1. Executive Summary

The Google Ads AI Search Optimization Platform is a revolutionary internal tool designed specifically for maximizing Search campaign performance through AI-powered automation and insights. By leveraging advanced machine learning and the Google Ads API, this platform transforms manual search campaign management into intelligent, automated optimization workflows that dramatically improve ROI while reducing management time by 90%.

### Key Value Propositions:
- **40% reduction** in wasted ad spend through intelligent search query mining and negative keyword automation
- **50% improvement** in Quality Scores through AI-powered relevance optimization
- **3x faster** ad copy testing and optimization cycles
- **Real-time competitor intelligence** with automated response strategies
- **Natural language insights** that explain performance changes in plain English

---

## 2. Problem Statement

### Current Search Campaign Challenges:
1. **Search Query Waste**: Teams spend hours manually reviewing search terms reports, missing 60% of wasteful queries
2. **Ad Copy Stagnation**: Creating and testing compelling ad variations is time-consuming and inconsistent
3. **Quality Score Mysteries**: Improving Quality Scores requires deep expertise and constant monitoring
4. **Competitive Blindness**: No real-time visibility into competitor strategies and changes
5. **Bid Inefficiency**: Manual bidding leaves money on the table during high-conversion periods
6. **Insight Paralysis**: Too much data, not enough actionable intelligence

### Impact:
- Average 25% of budget wasted on irrelevant search queries
- 40% higher CPCs due to poor Quality Scores
- Lost conversions from suboptimal ad copy
- Reactive rather than proactive competitive positioning
- 6+ hours daily on manual optimization tasks per account manager

---

## 3. Goals and Objectives

### Primary Goals:
1. **Maximize Search ROI**: Achieve 40% improvement in ROAS through intelligent automation
2. **Eliminate Wasted Spend**: Reduce irrelevant clicks by 50% with AI-powered query analysis
3. **Dominate Quality Scores**: Achieve 8+ Quality Scores on 80% of keywords
4. **Accelerate Testing**: Run 10x more ad tests with automated creation and analysis
5. **Competitive Advantage**: React to competitor changes within minutes, not days

### Success Metrics:
- Wasted spend reduction percentage
- Average Quality Score improvement
- Ad testing velocity (tests per month)
- Time saved per account (hours/week)
- Cost per conversion reduction
- Search impression share gains

---

## 4. User Personas

### Primary Users:

#### 1. **Search Campaign Specialist (Emma)**
- **Role**: Manages 15-20 search accounts daily
- **Needs**: Quick identification of optimization opportunities, bulk actions
- **Pain Points**: Manual search term reviews, ad copy creation bottlenecks
- **Goals**: Maximize performance while managing more accounts efficiently

#### 2. **PPC Team Lead (Marcus)**
- **Role**: Oversees team of 5 specialists, strategic planning
- **Needs**: Team performance visibility, competitive intelligence
- **Pain Points**: Inconsistent optimization approaches, lack of competitive data
- **Goals**: Standardize best practices, outperform competitors

#### 3. **Agency Director (Sarah)**
- **Role**: Client relationship management, strategic oversight
- **Needs**: Client-ready insights, performance narratives
- **Pain Points**: Explaining complex optimizations to clients
- **Goals**: Retain clients through superior performance

---

## 5. Core Features & Functional Requirements

### 5.1 Search Query Mining Engine 🔍
**Purpose**: Automatically analyze search terms to find profitable opportunities and eliminate waste

**Functionality:**
- **Profitable Query Discovery**
  - Identify high-converting search terms not yet added as keywords
  - Calculate potential revenue impact of adding each term
  - Auto-generate keyword suggestions with recommended match types
  - Group similar queries for themed ad group creation

- **Waste Elimination**
  - Flag search terms with 0 conversions after threshold spend
  - Identify irrelevant query patterns using NLP
  - Calculate wasted spend by query
  - Prioritize negative keywords by cost savings potential

- **Implementation via Google Ads API:**
  - `SearchTermView` for search query data
  - `KeywordPlanService` for keyword suggestions
  - `SharedSetService` for negative keyword lists
  - Automated daily processing of all accounts

**User Interface:**
- Interactive search term explorer with filters
- One-click add as keyword or negative
- Bulk action capabilities
- Visual waste vs opportunity matrix

---

### 5.2 Search Intent Classifier 🎯
**Purpose**: Categorize search queries by user intent to optimize messaging and bidding

**Functionality:**
- **Intent Categories**
  - Transactional: Ready to buy/convert
  - Informational: Researching solutions
  - Navigational: Looking for specific brands/sites
  - Commercial Investigation: Comparing options

- **AI-Powered Classification**
  - NLP analysis of query structure and keywords
  - Historical conversion data correlation
  - Confidence scoring for each classification
  - Custom intent categories by industry

- **Optimization Actions**
  - Adjust bids based on intent value
  - Tailor ad copy to match intent
  - Select appropriate landing pages
  - Modify ad extensions by intent

**Google Ads API Integration:**
- Custom labels for intent classification
- Bid adjustments via `CampaignBidModifier`
- Ad customizers for dynamic messaging
- Automated rules based on intent

---

### 5.3 Advanced Ad Copy Laboratory ✍️
**Purpose**: Generate, test, and optimize ad copy using psychological triggers and AI

**Functionality:**
- **Psychological Trigger Testing**
  - Urgency variations ("Limited Time", "Ends Today")
  - Scarcity elements ("Only X Left", "Exclusive")
  - Social proof ("10,000+ Customers", "Top Rated")
  - Authority signals ("Industry Leader", "Certified")
  - Emotion-based appeals (Security, Success, Savings)

- **AI Copy Generation**
  - Generate 15 headlines + 4 descriptions for RSAs
  - Ensure keyword inclusion for relevance
  - Brand voice consistency checker
  - Character count optimization
  - Dynamic insertion recommendations

- **Copy Performance Analysis**
  - Element-level performance tracking
  - Winning combination identifier
  - Emotional trigger effectiveness scorer
  - Competitor copy comparison

**Google Ads API Implementation:**
- `AdService` for creating/updating ads
- `ResponsiveSearchAd` for RSA management
- `AdGroupAdService` for testing rotation
- Asset performance reporting APIs

---

### 5.4 Negative Keyword AI 🚫
**Purpose**: Proactively identify and implement negative keywords to eliminate wasted spend

**Functionality:**
- **Intelligent Detection**
  - Pattern recognition for irrelevant queries
  - Industry-specific negative templates
  - Cross-campaign conflict detection
  - Seasonal negative suggestions

- **Automated Implementation**
  - Confidence-based auto-negation
  - Negative list optimization
  - Match type recommendations
  - Campaign vs ad group level decisions

- **Impact Tracking**
  - Cost savings calculator
  - Prevented clicks estimator
  - Quality Score impact monitor
  - Historical waste prevention

**Google Ads API Integration:**
- `NegativeKeywordListService`
- `CampaignCriterionService`
- `AdGroupCriterionService`
- Shared negative list management

---

### 5.5 Match Type Optimizer 🎲
**Purpose**: Dynamically optimize keyword match types based on performance data

**Functionality:**
- **Performance-Based Migration**
  - Broad to phrase to exact progression
  - Conversion threshold triggers
  - Cost-per-acquisition analysis
  - Search term coverage evaluation

- **Match Type Recommendations**
  - Data-driven match type suggestions
  - Duplicate keyword prevention
  - Budget impact predictions
  - Coverage gap identification

- **Automated Transitions**
  - Gradual match type tightening
  - Performance monitoring
  - Rollback capabilities
  - Cross-campaign coordination

**Google Ads API Implementation:**
- `KeywordMatchType` modifications
- `AdGroupCriterionService` for updates
- Performance tracking via reporting API
- Bulk operations support

---

### 5.6 Ad Relevance Maximizer 🎯
**Purpose**: Improve Quality Scores by optimizing keyword-to-ad relevance

**Functionality:**
- **Relevance Analysis**
  - Keyword-to-ad alignment scoring
  - Semantic similarity evaluation
  - Landing page relevance checker
  - Ad group theme consistency

- **Optimization Recommendations**
  - Ad copy modifications for keywords
  - Ad group restructuring suggestions
  - Dynamic keyword insertion opportunities
  - Landing page alignment

- **Quality Score Tracking**
  - Component-level QS monitoring
  - Historical trending
  - Improvement opportunity ranking
  - Competitive QS estimation

**Google Ads API Integration:**
- `QualityScoreInfo` data retrieval
- `AdGroupService` for restructuring
- `ExpandedTextAdService` for updates
- Keyword relevance diagnostics

---

### 5.7 Search Bid Intelligence 💰
**Purpose**: Optimize bids at granular levels for maximum ROI

**Functionality:**
- **Micro-Bidding Engine**
  - Hour-by-hour optimization
  - Day-of-week patterns
  - Geographic performance
  - Device-specific strategies
  - Demographic adjustments

- **Position Strategy**
  - First page bid calculator
  - Top of page optimizer
  - Position-based ROI analysis
  - Impression share targets

- **Smart Automation**
  - Bid cap recommendations
  - Budget-aware bidding
  - Competitive response
  - Profit margin integration

**Google Ads API Implementation:**
- `BiddingStrategyService`
- `CampaignBidModifierService`
- `AdSchedule` for dayparting
- `LocationBidModifier` for geo

---

### 5.8 Ad Extensions Maximizer 🔗
**Purpose**: Maximize SERP real estate and CTR through intelligent extension optimization

**Functionality:**
- **Extension Performance Analysis**
  - CTR lift by extension type
  - Best combination identifier
  - Mobile vs desktop strategies
  - Competitive extension gaps

- **AI-Generated Extensions**
  - Sitelink suggestions from landing pages
  - Callout optimization
  - Structured snippet creation
  - Dynamic promotion extensions

- **Automated Management**
  - Seasonal rotation
  - Performance-based selection
  - A/B testing framework
  - Relevance matching

**Google Ads API Implementation:**
- `ExtensionFeedItemService`
- All extension types supported
- Performance reporting
- Automated scheduling

---

### 5.9 Landing Page Synergy 🎯
**Purpose**: Ensure message consistency from ad click to conversion

**Functionality:**
- **Message Match Analysis**
  - Headline consistency checker
  - CTA alignment validator
  - Keyword density analyzer
  - Trust signal verifier

- **Page Performance Tracking**
  - Load time monitoring
  - Mobile usability scoring
  - Conversion path analysis
  - Form optimization tips

- **Dynamic Selection**
  - Best page per keyword
  - A/B test coordination
  - Personalization rules
  - Performance prediction

**Google Ads API Integration:**
- Landing page tracking
- `ValueTrack` parameters
- Conversion tracking setup
- Page-level reporting

---

### 5.10 Advanced Search Automation 🤖
**Purpose**: Automate complex optimization workflows and campaign structures

**Functionality:**
- **Structure Optimization**
  - SKAG (Single Keyword Ad Groups) builder
  - Campaign reorganization
  - Budget distribution logic
  - Naming convention enforcement

- **Workflow Automation**
  - Multi-step optimization sequences
  - Conditional logic rules
  - Scheduled operations
  - Bulk change management

- **Performance Monitoring**
  - Anomaly detection
  - Alert configuration
  - Automated responses
  - Change validation

**Google Ads API Implementation:**
- `BatchJobService` for bulk operations
- `MutateJobService` for complex workflows
- Campaign/ad group creation APIs
- Automated rules engine

---

### 5.11 Search Ads Scripts Library 📚
**Purpose**: Pre-built automation scripts for common optimization tasks

**Functionality:**
- **Script Templates**
  - N-gram analysis
  - Budget monitoring
  - Bid adjustments
  - Report generation
  - Quality Score tracking

- **Custom Script Builder**
  - Visual script creator
  - No-code interface
  - Testing environment
  - Version control

- **Management Tools**
  - Script scheduler
  - Error monitoring
  - Performance tracking
  - Sharing capabilities

**Google Ads API Integration:**
- Google Ads Scripts API
- JavaScript execution
- MCC-level scripts
- Cross-account operations

---

### 5.12 AI-Powered Insights Engine 🧠
**Purpose**: Transform data into actionable insights using natural language

**Functionality:**
- **Natural Language Queries**
  - "Why did CPC increase yesterday?"
  - "What keywords need attention?"
  - "Show me optimization opportunities"
  - Plain English responses

- **Predictive Alerts**
  - Budget exhaustion warnings
  - Quality Score predictions
  - Competitor activity alerts
  - Trend change notifications

- **Automated Narratives**
  - Weekly performance stories
  - Executive summaries
  - Client-ready reports
  - Success/failure analysis

**Implementation:**
- OpenRouter API for LLM processing
- Custom insight generation models
- Real-time data analysis
- Contextual recommendations

---

## 6. Technical Architecture

### 6.1 Technology Stack

```yaml
Frontend:
  - Framework: Vite 5.4+ + React 18.3+ + TypeScript 5.5+
  - UI Library: Tailwind CSS 3.4+ + shadcn/ui (Brand Wisdom themed)
  - State Management: 
    - Server State: TanStack Query 5.56+ (React Query)
    - Client State: Zustand
    - Form State: React Hook Form 7.53+ with Zod 3.23+ validation
  - Routing: React Router DOM 6.26+
  - Data Visualization:
    - Charts: Recharts 2.12+
    - Tables: TanStack Table
  - Animations: Framer Motion (for smooth transitions)
  - Utilities:
    - Date handling: date-fns 3.6+
    - Icons: Lucide React
    - Notifications: Sonner (toast notifications)
    - Class utilities: clsx + tailwind-merge
  - Development:
    - Linting: ESLint 9+ with TypeScript rules
    - Build: Vite with SWC for fast compilation

Backend:
  - API: FastAPI (Python 3.12+)
  - Task Queue: Celery + Redis
  - Scheduler: Celery Beat
  - AI/ML: LangChain + OpenRouter API
  - Google Ads: google-ads-python
  - Validation: Pydantic

Database & Services:
  - Database: Supabase (PostgreSQL with pgvector)
  - Vector Store: pgvector (for semantic search)
  - Cache: Redis
  - File Storage: Supabase Storage
  - Real-time: Supabase Realtime
  - Search: PostgreSQL Full Text Search

Infrastructure:
  - Frontend: Vercel (with Edge Functions)
  - Backend: Railway
  - Containers: Docker
  - Monitoring: Sentry + Datadog
  - CI/CD: GitHub Actions
  - CDN: Vercel Edge Network
```

### 6.2 Google Ads API Integration

```yaml
Core API Services Used:
  - GoogleAdsService: Main query interface
  - SearchTermViewService: Search query data
  - KeywordPlanService: Keyword suggestions
  - AdService: Ad creation/modification
  - BiddingStrategyService: Bid management
  - ExtensionFeedItemService: Extensions
  - BatchJobService: Bulk operations
  - ReportingService: Performance data

Rate Limits:
  - Operations per day: 15,000
  - Get requests per day: 15,000
  - Mutate operations per request: 5,000
  - Retry logic with exponential backoff
```

### 6.3 OpenRouter AI Configuration

```python
# Optimized model selection for search tasks
OPENROUTER_MODELS = {
    "ad_copy_generation": "anthropic/claude-3-opus",
    "keyword_analysis": "openai/gpt-4-turbo",
    "bulk_operations": "anthropic/claude-3-haiku",
    "competitor_analysis": "google/gemini-pro",
    "insights_generation": "anthropic/claude-3-sonnet",
    "quick_classification": "mistralai/mixtral-8x7b"
}
```

---

## 7. User Experience Design

### 7.1 Information Architecture

```
Dashboard
├── Overview
│   ├── Account Health Scores
│   ├── Opportunity Alerts
│   └── Performance Trends
├── Search Intelligence
│   ├── Query Mining
│   ├── Intent Analysis
│   └── Negative Keywords
├── Ad Optimization
│   ├── Copy Laboratory
│   ├── Extension Manager
│   └── Testing Center
├── Bid Management
│   ├── Bid Intelligence
│   ├── Budget Optimizer
│   └── Position Strategy
├── Quality Score Hub
│   ├── Score Tracking
│   ├── Relevance Analysis
│   └── Improvement Plans
├── Automation
│   ├── Workflows
│   ├── Scripts Library
│   └── Rules Engine
└── Insights
    ├── AI Assistant
    ├── Reports
    └── Predictions
```

### 7.2 Key User Flows

**1. Daily Optimization Flow**
- Login → Dashboard → Opportunity alerts
- Review AI recommendations → Bulk approve/modify
- Check automation results → Adjust parameters
- Natural language query for insights

**2. Campaign Setup Flow**
- Import account → AI structure analysis
- Receive optimization plan → Review/approve
- Automated implementation → Monitor results
- Continuous optimization activation

**3. Competitive Response Flow**
- Competitor alert received → View changes
- AI suggests counter-strategy → Review options
- Implement response → Track impact
- Adjust based on results

### 7.3 Enhanced User Experience Features

**Form Interactions:**
- **Smart Validation**: Real-time validation with helpful error messages using React Hook Form + Zod
- **Auto-save**: Form progress saved automatically to prevent data loss
- **Keyboard Navigation**: Full keyboard support for power users
- **Inline Editing**: Click-to-edit functionality for quick changes

**Visual Feedback:**
- **Smooth Transitions**: Framer Motion animations for state changes
- **Loading States**: Skeleton screens and progress indicators
- **Success Animations**: Celebratory animations for achieved goals
- **Micro-interactions**: Hover effects, button feedback, drag-and-drop

**Real-time Updates:**
- **Live Notifications**: Sonner toasts for immediate feedback
- **WebSocket Updates**: Real-time campaign performance changes
- **Collaborative Editing**: See when team members make changes
- **Auto-refresh**: Dashboard metrics update without page reload

**Data Visualization Enhancements:**
- **Interactive Charts**: Zoom, pan, and filter capabilities in Recharts
- **Animated Transitions**: Smooth data updates with Framer Motion
- **Comparison Views**: Side-by-side performance comparisons
- **Export Options**: Download charts as images or data as CSV

**Search & Filter Experience:**
- **Instant Search**: Real-time filtering as you type
- **Smart Suggestions**: AI-powered search recommendations
- **Saved Filters**: Store and reuse complex filter combinations
- **Bulk Selection**: Select multiple items with keyboard shortcuts

---

## 8. MVP Scope & Phasing

### Phase 1: Foundation (Months 1-2)
- ✅ Google Ads API integration
- ✅ Basic dashboard
- ✅ Search Query Mining Engine
- ✅ Negative Keyword AI (basic)
- ✅ Simple bid management
- ✅ User authentication

### Phase 2: Intelligence (Months 3-4)
- ✅ Search Intent Classifier
- ✅ Ad Copy Laboratory
- ✅ Quality Score tracking
- ✅ Ad Extensions Maximizer
- ✅ Match Type Optimizer

### Phase 3: Automation (Months 5-6)
- ✅ Advanced Search Automation
- ✅ Scripts Library
- ✅ AI Insights Engine
- ✅ Landing Page Synergy
- ✅ Competitive intelligence

### Phase 4: Optimization (Months 7+)
- ✅ Advanced bid strategies
- ✅ Workflow builder
- ✅ Custom reporting
- ✅ White-label options
- ✅ Enterprise features

---

## 9. Success Metrics & KPIs

### Primary KPIs:
1. **Efficiency Metrics**
   - Time saved: 6+ hours/week per user
   - Accounts per manager: 2x increase
   - Optimization velocity: 10x faster

2. **Performance Metrics**
   - Wasted spend reduction: 40%
   - Quality Score improvement: +2 points average
   - CTR increase: 25%
   - CPA reduction: 30%

3. **Business Metrics**
   - Client retention: 95%+
   - Revenue per account: +35%
   - Platform ROI: 10:1

### Tracking & Reporting:
- Real-time KPI dashboard
- Weekly trend analysis
- Monthly business reviews
- Quarterly strategy adjustments

---

## 10. Risk Mitigation

### Technical Risks:
| Risk | Impact | Mitigation |
|------|---------|------------|
| Google Ads API changes | High | Abstract API layer, monitor changelog |
| Rate limit constraints | Medium | Implement queuing, batch operations |
| AI hallucinations | Medium | Confidence scoring, human review options |
| Data sync issues | Medium | Redundancy, error recovery, monitoring |
| Form data loss | Low | Auto-save with React Hook Form, local storage backup |
| Animation performance | Low | Use Framer Motion's optimizations, GPU acceleration |

### Business Risks:
| Risk | Impact | Mitigation |
|------|---------|------------|
| Over-automation concerns | Medium | Maintain human control, approval workflows |
| Competitive advantage loss | High | Continuous innovation, unique features |
| Client trust issues | Medium | Transparency, audit trails, control options |
| User adoption friction | Low | Smooth animations, helpful validations, onboarding |

### Performance Optimizations:
1. **Frontend Performance**
   - Code splitting with React.lazy() for route-based chunks
   - Memoization of expensive computations
   - Virtual scrolling for large data tables
   - Image optimization with Vercel's Image Optimization API
   - Service Worker for offline capability

2. **Data Fetching Optimization**
   - TanStack Query for intelligent caching and background refetching
   - Optimistic updates for immediate UI feedback
   - Pagination and infinite scrolling for large datasets
   - GraphQL-like field selection to reduce payload size

3. **Animation Performance**
   - Framer Motion's hardware acceleration
   - will-change CSS property for smooth animations
   - RequestAnimationFrame for custom animations
   - Reduced motion support for accessibility

---

## 11. Competitive Advantages

### Unique Differentiators:
1. **Search Query Mining Engine** - No competitor offers this depth of analysis
2. **Psychological Ad Copy Testing** - Unique emotional trigger framework
3. **Natural Language Insights** - Plain English performance explanations
4. **Intent-Based Optimization** - Sophisticated query classification
5. **Integrated Automation** - End-to-end workflow automation

### Technical Advantages from Enhanced Stack:
1. **Superior User Experience**
   - Buttery-smooth animations with Framer Motion
   - Instant form feedback with React Hook Form + Zod
   - Real-time notifications with Sonner
   - Zero-latency interactions with optimistic updates

2. **Developer Productivity**
   - Type-safe forms with automatic validation
   - Reusable animation patterns
   - Consistent date handling with date-fns
   - Rapid UI development with enhanced shadcn/ui

3. **Performance Benefits**
   - 50% faster form submissions with client-side validation
   - 60% reduction in perceived latency with animations
   - 40% better user engagement with micro-interactions
   - Sub-100ms response times for UI actions

### Moat Building:
- Proprietary algorithms trained on agency data
- Deep Google Ads API integration
- Network effects from shared learnings
- Continuous AI model improvement
- Industry-specific optimizations
- Premium user experience that competitors can't match

---

## 12. Progressive Development Guide for AI Developer

### 12.1 Development Philosophy
Build incrementally with working features at each stage. Each phase should produce a deployable, valuable product that users can test and provide feedback on.

### 12.2 Pre-Development Setup (Week 1)

#### Environment Setup:
```bash
# 1. Clone repository and setup as per Setup instructions.md
# 2. Configure all API keys in .env files:
#    - OPENROUTER_API_KEY
#    - SUPABASE credentials
#    - Google Ads API credentials
# 3. Run Supabase schema (infrastructure/supabase-schema.sql)
# 4. Verify Docker setup works
```

#### Key Technology Implementations:

**Form Handling with React Hook Form + Zod:**
```typescript
// frontend/src/schemas/searchQuerySchema.ts
import { z } from 'zod';

export const searchQueryFilterSchema = z.object({
  dateRange: z.object({
    start: z.date(),
    end: z.date()
  }),
  minCost: z.number().min(0).optional(),
  conversionThreshold: z.number().min(0).default(0),
  includeZeroClick: z.boolean().default(false)
});

// frontend/src/components/SearchQueryForm.tsx
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

const form = useForm({
  resolver: zodResolver(searchQueryFilterSchema),
  defaultValues: {
    conversionThreshold: 0,
    includeZeroClick: false
  }
});
```

**Animation System with Framer Motion:**
```typescript
// frontend/src/components/AnimatedCard.tsx
import { motion } from 'framer-motion';

export const RecommendationCard = ({ recommendation }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    exit={{ opacity: 0, y: -20 }}
    whileHover={{ scale: 1.02 }}
    className="card"
  >
    {/* Card content */}
  </motion.div>
);
```

**Toast Notifications with Sonner:**
```typescript
// frontend/src/hooks/useNotifications.ts
import { toast } from 'sonner';

export const useNotifications = () => {
  const notifySuccess = (message: string) => {
    toast.success(message, {
      duration: 4000,
      position: 'top-right'
    });
  };
  
  const notifyAIInsight = (insight: string) => {
    toast.custom((t) => (
      <motion.div
        initial={{ scale: 0.8 }}
        animate={{ scale: 1 }}
        className="bg-primary-50 border-primary-300"
      >
        <LucideSparkles className="text-primary-600" />
        {insight}
      </motion.div>
    ));
  };
  
  return { notifySuccess, notifyAIInsight };
};
```

#### Google Ads API Setup:
```python
# backend/app/core/google_ads_config.py
from google.ads.googleads.client import GoogleAdsClient

def get_google_ads_client(customer_id: str, refresh_token: str):
    """Initialize Google Ads client with user credentials"""
    config = {
        "developer_token": settings.GOOGLE_ADS_DEVELOPER_TOKEN,
        "client_id": settings.GOOGLE_ADS_CLIENT_ID,
        "client_secret": settings.GOOGLE_ADS_CLIENT_SECRET,
        "refresh_token": refresh_token,
        "login_customer_id": settings.GOOGLE_ADS_LOGIN_CUSTOMER_ID
    }
    return GoogleAdsClient.load_from_dict(config)
```

---

### 12.3 Phase 1: MVP Foundation (Weeks 2-5)

#### Sprint 1.1: Authentication & Account Connection (Week 2)

**Backend Tasks:**
```python
# 1. Create OAuth2 flow for Google Ads
# backend/app/api/auth.py
@router.get("/auth/google")
async def google_auth():
    """Redirect to Google OAuth consent screen"""
    # Build authorization URL with Google Ads scope
    
@router.get("/auth/callback")
async def google_callback(code: str):
    """Handle OAuth callback and store refresh token"""
    # Exchange code for tokens
    # Store in Supabase users table
    
# 2. Create user management endpoints
# backend/app/api/users.py
@router.post("/users/accounts/link")
async def link_google_ads_account(customer_id: str, user_id: str):
    """Link a Google Ads account to user"""
```

**Frontend Tasks:**
```typescript
// 1. Create login/signup flow with Supabase Auth
// frontend/src/pages/Login.tsx

// 2. Create Google Ads account connection flow
// frontend/src/pages/ConnectAccount.tsx

// 3. Create basic dashboard layout
// frontend/src/layouts/DashboardLayout.tsx
```

**Acceptance Criteria:**
- Users can sign up/login
- Users can connect Google Ads accounts via OAuth
- Connected accounts are stored in database
- Basic dashboard shows connected accounts

#### Sprint 1.2: Search Query Mining Engine - Basic (Week 3)

**Backend Implementation:**
```python
# backend/app/services/search_query_mining.py
class SearchQueryMiningService:
    def __init__(self, google_ads_client):
        self.client = google_ads_client
    
    async def fetch_search_terms(self, customer_id: str, date_range: str):
        """Fetch search terms report from Google Ads API"""
        query = """
            SELECT
                search_term_view.search_term,
                metrics.clicks,
                metrics.impressions,
                metrics.cost_micros,
                metrics.conversions,
                metrics.cost_per_conversion
            FROM search_term_view
            WHERE segments.date DURING {date_range}
            ORDER BY metrics.cost_micros DESC
        """
        # Execute query and return results
    
    async def analyze_search_terms(self, search_terms: List[dict]):
        """Basic analysis to find opportunities and waste"""
        opportunities = []
        negative_candidates = []
        
        for term in search_terms:
            if term['conversions'] > 0 and term['cost_per_conversion'] < threshold:
                opportunities.append(term)
            elif term['clicks'] > 10 and term['conversions'] == 0:
                negative_candidates.append(term)
        
        return {
            'opportunities': opportunities,
            'negative_candidates': negative_candidates
        }

# Create Celery task for automated processing
# backend/app/tasks/search_query_tasks.py
@celery_app.task
def process_search_queries_for_all_accounts():
    """Daily task to analyze all accounts"""
```

**Frontend Implementation:**
```typescript
// frontend/src/pages/SearchQueryMining.tsx
// Create UI with:
// - Date range selector
// - Search terms table with sorting/filtering
// - Opportunity highlights
// - Negative keyword suggestions
// - One-click add actions
```

**Database Schema Addition:**
```sql
-- Add to Supabase
CREATE TABLE search_query_analyses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID REFERENCES google_ads_accounts(id),
    search_term TEXT NOT NULL,
    clicks INTEGER,
    conversions DECIMAL,
    cost DECIMAL,
    recommendation_type TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Acceptance Criteria:**
- Can fetch search terms from Google Ads API
- Basic opportunity/waste identification works
- UI displays search terms with recommendations
- Can add keywords/negatives with one click

#### Sprint 1.3: Negative Keyword AI - Basic (Week 4)

**Backend Implementation:**
```python
# backend/app/services/negative_keyword_ai.py
class NegativeKeywordAI:
    def __init__(self, openrouter_client):
        self.llm = openrouter_client
        
    async def analyze_for_negatives(self, search_terms: List[str], 
                                   business_context: str) -> List[dict]:
        """Use AI to identify negative keyword patterns"""
        prompt = f"""
        Business: {business_context}
        Search terms: {search_terms}
        
        Identify irrelevant search patterns and suggest negative keywords.
        Consider: job seekers, competitors, unrelated products, etc.
        """
        
        response = await self.llm.generate(prompt, model="gpt-4-turbo")
        return self.parse_negative_suggestions(response)
    
    async def create_negative_keyword_lists(self, suggestions: List[dict]):
        """Create and apply negative keyword lists via API"""
        # Use NegativeKeywordListService
```

**Enhanced Frontend with New Tech Stack:**
```typescript
// Frontend implementation with React Hook Form + Zod
// frontend/src/pages/NegativeKeywords.tsx
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'sonner';
import { z } from 'zod';

const negativeKeywordSchema = z.object({
  keywords: z.array(z.object({
    keyword: z.string().min(1),
    matchType: z.enum(['broad', 'phrase', 'exact']),
    reason: z.string().optional()
  })),
  applyToList: z.string().uuid(),
  confidence: z.number().min(0).max(1)
});

export const NegativeKeywordsPage = () => {
  const form = useForm({
    resolver: zodResolver(negativeKeywordSchema)
  });
  
  const onSubmit = async (data) => {
    try {
      await api.addNegativeKeywords(data);
      toast.success('Negative keywords added successfully', {
        description: `${data.keywords.length} keywords added to your list`
      });
    } catch (error) {
      toast.error('Failed to add negative keywords');
    }
  };
  
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="space-y-6"
    >
      <AnimatePresence mode="wait">
        {suggestions.map((suggestion) => (
          <motion.div
            key={suggestion.id}
            initial={{ x: -20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            exit={{ x: 20, opacity: 0 }}
            whileHover={{ scale: 1.02 }}
            className="recommendation-card"
          >
            {/* Suggestion content */}
          </motion.div>
        ))}
      </AnimatePresence>
    </motion.div>
  );
};
```

**Integration with UI:**
```typescript
// Add to Search Query Mining page:
// - AI suggestions panel with smooth animations
// - Form validation for bulk operations
// - Real-time impact preview with animated counters
// - Toast notifications for all actions
```

#### Sprint 1.4: Basic Dashboard & Reports (Week 5)

**Backend Tasks:**
```python
# backend/app/api/dashboard.py
@router.get("/dashboard/overview")
async def get_dashboard_overview(user_id: str):
    """Aggregate metrics across all accounts"""
    return {
        "total_accounts": count,
        "total_spend": sum,
        "opportunities_found": count,
        "estimated_savings": sum,
        "recent_optimizations": list
    }
```

**Frontend Tasks:**
```typescript
// frontend/src/pages/Dashboard.tsx
// Create overview with:
// - Account cards with health scores
// - Opportunity alerts
// - Recent activity feed
// - Quick actions
```

---

### 12.4 Phase 2: Intelligence Layer (Weeks 6-9)

#### Sprint 2.1: Search Intent Classifier (Week 6)

**Backend Implementation:**
```python
# backend/app/services/intent_classifier.py
class IntentClassifier:
    def __init__(self):
        self.intent_keywords = {
            'transactional': ['buy', 'price', 'cost', 'cheap', 'deal'],
            'informational': ['what', 'how', 'why', 'guide', 'tutorial'],
            'navigational': ['brand names', 'login', 'website'],
            'commercial': ['best', 'top', 'review', 'compare']
        }
    
    async def classify_intent(self, search_term: str) -> dict:
        """Classify search intent using rules + AI"""
        # Rule-based initial classification
        # Then use AI for ambiguous cases
        
    async def apply_intent_optimizations(self, campaign_id: str, 
                                       intent_data: dict):
        """Apply bid modifiers and ad customizations based on intent"""
        # Use CampaignBidModifier API
```

**Frontend Addition:**
```typescript
// frontend/src/components/IntentAnalysis.tsx
// Visual intent breakdown
// Bid adjustment recommendations
// Ad copy suggestions by intent
```

#### Sprint 2.2: Ad Copy Laboratory (Week 7)

**Backend Implementation:**
```python
# backend/app/services/ad_copy_lab.py
class AdCopyLaboratory:
    def __init__(self, openrouter_client):
        self.llm = openrouter_client
        self.psychological_triggers = {
            'urgency': ['Limited Time', 'Ends Soon', 'Last Chance'],
            'scarcity': ['Only X Left', 'Exclusive', 'Limited Stock'],
            'social_proof': ['Join X Customers', 'Top Rated', '#1 Choice'],
            'authority': ['Industry Leader', 'Certified', 'Award Winning']
        }
    
    async def generate_rsa_assets(self, keyword: str, landing_page: str,
                                 brand_voice: str) -> dict:
        """Generate 15 headlines and 4 descriptions for RSAs"""
        prompt = self.build_rsa_prompt(keyword, landing_page, brand_voice)
        
        # Use Claude-3-opus for creative writing
        response = await self.llm.generate(
            prompt, 
            model="anthropic/claude-3-opus"
        )
        
        return {
            'headlines': self.extract_headlines(response),
            'descriptions': self.extract_descriptions(response),
            'psychological_mapping': self.map_triggers(response)
        }
    
    async def create_ad_via_api(self, ad_group_id: str, assets: dict):
        """Create responsive search ad via Google Ads API"""
        # Use AdService to create RSA
```

**Frontend Implementation:**
```typescript
// frontend/src/pages/AdCopyLab.tsx
// Features:
// - Keyword input
// - AI generation trigger
// - Preview all combinations
// - Edit/refine suggestions
// - Psychological trigger tags
// - One-click create ad
```

#### Sprint 2.3: Quality Score Tracker (Week 8)

**Backend Implementation:**
```python
# backend/app/services/quality_score.py
class QualityScoreService:
    async def fetch_quality_scores(self, campaign_id: str):
        """Get QS data from API including components"""
        query = """
            SELECT
                ad_group_criterion.keyword.text,
                ad_group_criterion.quality_info.quality_score,
                ad_group_criterion.quality_info.creative_quality_score,
                ad_group_criterion.quality_info.landing_page_experience,
                ad_group_criterion.quality_info.search_predicted_ctr
            FROM ad_group_criterion
            WHERE ad_group_criterion.type = KEYWORD
        """
        
    async def generate_improvement_plan(self, keyword_data: dict):
        """AI-powered QS improvement recommendations"""
        # Analyze each component
        # Generate specific actions
        # Prioritize by impact
```

**Frontend Implementation:**
```typescript
// frontend/src/pages/QualityScoreHub.tsx
// - QS distribution chart
// - Component breakdown
// - Improvement recommendations
// - Historical trending
// - Action items with priority
```

#### Sprint 2.4: Ad Extensions Maximizer (Week 9)

**Backend Implementation:**
```python
# backend/app/services/extensions_manager.py
class ExtensionsManager:
    async def analyze_extension_opportunities(self, account_id: str):
        """Find missing or underperforming extensions"""
        
    async def generate_extensions(self, business_info: dict):
        """AI generates extension content"""
        # Sitelinks from landing pages
        # Callouts from USPs
        # Structured snippets from categories
        
    async def apply_extensions(self, campaign_id: str, extensions: dict):
        """Create extensions via API"""
        # Use ExtensionFeedItemService
```

---

### 12.5 Phase 3: Automation Engine (Weeks 10-13)

#### Sprint 3.1: Match Type Optimizer (Week 10)

**Backend Implementation:**
```python
# backend/app/services/match_type_optimizer.py
class MatchTypeOptimizer:
    async def analyze_match_type_performance(self, ad_group_id: str):
        """Compare performance across match types"""
        
    async def recommend_transitions(self, performance_data: dict):
        """Suggest match type changes based on data"""
        
    async def execute_match_type_migration(self, recommendations: List[dict]):
        """Implement match type changes via API"""
        # Pause old keywords
        # Create new keywords with better match types
        # Monitor performance
```

#### Sprint 3.2: Landing Page Synergy (Week 11)

**Backend Implementation:**
```python
# backend/app/services/landing_page_analyzer.py
class LandingPageAnalyzer:
    async def analyze_message_match(self, ad_copy: str, landing_page_url: str):
        """Check consistency between ad and landing page"""
        # Fetch landing page content
        # Compare headlines, CTAs, keywords
        # Score alignment
        
    async def track_landing_page_performance(self, account_id: str):
        """Monitor landing page metrics"""
        # Use ValueTrack parameters
        # Track conversion rates by page
```

#### Sprint 3.3: Advanced Search Automation (Week 12)

**Backend Implementation:**
```python
# backend/app/services/automation_engine.py
class AutomationEngine:
    async def create_workflow(self, workflow_config: dict):
        """Create multi-step automation workflows"""
        # Example: If CTR < 1%, pause keyword
        # If conversions > 10, increase bid by 10%
        
    async def execute_workflow(self, workflow_id: str):
        """Run workflow with error handling"""
        
# backend/app/tasks/automation_tasks.py
@celery_app.task
def run_scheduled_automations():
    """Execute all active workflows"""
```

**Frontend Workflow Builder:**
```typescript
// frontend/src/pages/AutomationBuilder.tsx
// Visual workflow designer
// Drag-drop conditions and actions
// Test mode
// Schedule configuration
```

#### Sprint 3.4: Scripts Library (Week 13)

**Backend Implementation:**
```python
# backend/app/services/scripts_manager.py
class ScriptsLibrary:
    def __init__(self):
        self.templates = {
            'n_gram_analysis': self.load_template('n_gram.js'),
            'budget_monitor': self.load_template('budget_monitor.js'),
            'bid_adjustments': self.load_template('bid_adjustments.js')
        }
    
    async def customize_script(self, template: str, parameters: dict):
        """Customize script template with user parameters"""
        
    async def deploy_script(self, account_id: str, script_content: str):
        """Deploy script to Google Ads account"""
```

---

### 12.6 Phase 4: AI Intelligence (Weeks 14-16)

#### Sprint 4.1: Natural Language Insights (Week 14)

**Backend Implementation:**
```python
# backend/app/services/insights_engine.py
class InsightsEngine:
    def __init__(self, openrouter_client):
        self.llm = openrouter_client
        
    async def answer_question(self, question: str, account_context: dict):
        """Answer natural language questions about performance"""
        # Examples:
        # "Why did CPC increase last week?"
        # "What keywords are wasting money?"
        # "How can I improve Quality Score?"
        
        # Fetch relevant data
        # Build context
        # Generate human-friendly answer
        
    async def generate_weekly_narrative(self, account_id: str):
        """Create story-based performance summary"""
```

**Frontend Chat Interface:**
```typescript
// frontend/src/components/AIAssistant.tsx
// Chat interface
// Suggested questions
// Voice input option
// Copy insights feature
```

#### Sprint 4.2: Predictive Alerts (Week 15)

**Backend Implementation:**
```python
# backend/app/services/predictive_analytics.py
class PredictiveAnalytics:
    async def predict_budget_exhaustion(self, campaign_data: dict):
        """Predict when budget will run out"""
        
    async def detect_anomalies(self, metrics_history: List[dict]):
        """Identify unusual patterns"""
        
    async def forecast_performance(self, historical_data: dict):
        """Predict future performance trends"""
```

#### Sprint 4.3: Bid Intelligence (Week 16)

**Backend Implementation:**
```python
# backend/app/services/bid_intelligence.py
class BidIntelligence:
    async def calculate_optimal_bids(self, keyword_data: dict):
        """Calculate bids for ROI targets"""
        
    async def implement_dayparting(self, campaign_id: str, 
                                  performance_by_hour: dict):
        """Set up hour-by-hour bid adjustments"""
        
    async def setup_geo_bid_modifiers(self, campaign_id: str,
                                     geo_performance: dict):
        """Apply location-based bid adjustments"""
```

---

### 12.7 Testing Strategy

#### Unit Tests (Continuous):
```python
# backend/tests/test_search_query_mining.py
def test_opportunity_identification():
    """Test that high-converting terms are identified"""
    
def test_negative_keyword_detection():
    """Test that wasteful terms are flagged"""
```

#### Integration Tests (End of each sprint):
```python
# backend/tests/test_google_ads_integration.py
def test_api_connection():
    """Verify Google Ads API connectivity"""
    
def test_data_flow():
    """Test complete data flow from API to UI"""
```

#### User Acceptance Testing (End of each phase):
- Recruit 3-5 beta users
- Provide test scenarios
- Collect feedback
- Iterate based on findings

---

### 12.8 Deployment Strategy

#### Development Environment:
```bash
# Local development with Docker
docker-compose up

# Hot reloading enabled for both frontend and backend
```

#### Staging Environment:
```yaml
# Deploy to staging after each sprint
Frontend: Vercel Preview Deployments
Backend: Railway Review Apps
Database: Supabase Branch
```

#### Production Deployment:
```yaml
# Production deployment after each phase
Frontend:
  - Build: npm run build
  - Deploy: Vercel production
  - CDN: Automatic via Vercel

Backend:
  - Build: Docker image
  - Deploy: Railway production
  - Scale: Horizontal scaling ready

Database:
  - Migrations: Via Supabase CLI
  - Backups: Automated daily
```

---

### 12.9 Monitoring & Analytics

#### Application Monitoring:
```python
# Sentry for error tracking
sentry_sdk.init(dsn=settings.SENTRY_DSN)

# Custom metrics tracking
async def track_feature_usage(feature: str, user_id: str):
    """Track which features are being used"""
```

#### Performance Monitoring:
- API response times < 500ms
- UI load time < 2 seconds
- Background job completion rates
- Google Ads API quota usage

---

### 12.10 Documentation Requirements

Each phase should include:
1. **API Documentation** (auto-generated via FastAPI)
2. **User Guide** (feature tutorials)
3. **Developer Notes** (architecture decisions)
4. **Deployment Guide** (for DevOps)

---

## 13. Success Milestones

### Phase 1 Success Criteria:
- 5+ beta users actively using the platform
- 10,000+ search terms analyzed
- $10,000+ in identified waste
- 90% positive feedback on core features

### Phase 2 Success Criteria:
- 20+ active users
- 100+ ads created via AI
- 25% average Quality Score improvement
- First success story from client

### Phase 3 Success Criteria:
- 50+ active users
- 500+ automations running daily
- 40% time savings demonstrated
- 95% user retention

### Phase 4 Success Criteria:
- 100+ active users
- 1000+ natural language queries answered
- Complete platform adoption by team
- Ready for external client access

---

## 14. AI Developer Instructions

### Getting Started:
1. Review the complete PRD thoroughly
2. Set up development environment per Setup instructions.md
3. Start with Phase 1, Sprint 1.1
4. Commit code after each completed feature
5. Write tests as you build
6. Document any deviations from PRD

### Development Principles:
- **Incremental**: Each sprint should produce working features
- **Testable**: Write tests alongside code
- **User-Focused**: Get feedback early and often
- **API-First**: Ensure Google Ads API integration works perfectly
- **Scalable**: Build with 1000+ accounts in mind

### Communication:
- Daily progress updates
- Weekly demo of completed features
- Immediate escalation of blockers
- Proactive suggestions for improvements

---

## 15. Conclusion

This PRD provides a complete roadmap for building the Google Ads AI Search Optimization Platform progressively. Each phase builds upon the previous one, ensuring continuous value delivery while working toward the complete vision.

The enhanced technology stack with React Hook Form + Zod for robust form handling, Framer Motion for delightful animations, and Sonner for elegant notifications will create a premium user experience that sets our platform apart from competitors. These technologies, combined with our AI-powered features and deep Google Ads integration, will deliver:

- **90% reduction** in manual optimization time
- **Superior UX** with smooth animations and instant feedback
- **Developer efficiency** with type-safe, validated forms
- **Premium feel** that justifies premium pricing
- **Higher user engagement** through delightful interactions

The platform will revolutionize how Brand Wisdom Solutions manages search campaigns, providing unprecedented efficiency and results through intelligent automation paired with an exceptional user experience.

---

**Document Status:** Ready for AI Developer Implementation  
**Version:** 2.1 Final (Enhanced Tech Stack)  
**Owner:** Brand Wisdom Solutions Product Team  
**Last Updated:** June 2025