#!/bin/bash
# Startup script for Google Ads AI Optimization Platform

echo "🚀 Starting Google Ads AI Optimization Platform..."
echo "================================================"

# Function to cleanup on exit
cleanup() {
    echo -e "\n🛑 Shutting down servers..."
    pkill -f "uvicorn app.main:app"
    pkill -f "npm run dev"
    exit 0
}

# Set trap for cleanup
trap cleanup INT TERM

# Start backend server
echo -e "\n📦 Starting Backend Server..."
cd backend

PYTHON_CMD="python"
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
    echo "Using Python 3..."
elif command -v python &> /dev/null; then
    echo "Using Python (fallback)..."
else
    echo "Python not found. Please install Python."
    exit 1
fi

# Activate virtual environment
if [ -f "venv/bin/activate" ]; then
    echo "Activating venv (Linux/macOS)..."
    source venv/bin/activate
elif [ -f "venv/Scripts/activate" ]; then
    echo "Activating venv (Windows Git Bash)..."
    source venv/Scripts/activate
else
    echo "Virtual environment not found. Please set it up first (e.g., python -m venv venv && pip install -r requirements.txt)."
    # Optionally, could attempt to create and install here if desired for a more "setup-and-run" script
fi

echo "Starting backend with run_server.py..."
$PYTHON_CMD run_server.py &
BACKEND_PID=$!

# Wait for backend to start
echo "Waiting for backend to start..."
sleep 5

# Check if backend is running
if curl -s http://127.0.0.1:8000/health > /dev/null 2>&1; then
    echo "✅ Backend is running at http://127.0.0.1:8000"
    echo "📚 API Docs available at http://127.0.0.1:8000/docs"
else
    echo "⚠️  Backend may not be fully started yet"
fi

# Start frontend server
echo -e "\n🎨 Starting Frontend Server..."
cd ../frontend
if command -v npm &> /dev/null; then
    npm run dev &
    FRONTEND_PID=$!
else
    echo "npm not found. Please install Node.js and npm."
    exit 1
fi

# Wait for frontend to start
echo "Waiting for frontend to start..."
sleep 5

echo -e "\n✅ Application is starting!"
echo "================================================"
echo "🌐 Frontend: http://localhost:5173"
echo "🔧 Backend API: http://127.0.0.1:8000"
echo "📚 API Documentation: http://127.0.0.1:8000/docs"
echo "================================================"
echo ""
echo "📋 Test Accounts Available:"
echo "  - Test Manager: ************ (<EMAIL>)"
echo "  - Test Client 1: ************"
echo "  - Test Client 2: 406-000-1758"
echo "  - Test Dental: 788-496-2297"
echo ""
echo "⚠️  Note: Using Test Account Access (Basic Access needed for production)"
echo ""
echo "Press Ctrl+C to stop all servers"
echo ""

# Keep script running
wait
